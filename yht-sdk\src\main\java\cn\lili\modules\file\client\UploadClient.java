package cn.lili.modules.file.client;

import cn.lili.modules.constant.ServiceConstant;
import cn.lili.modules.file.entity.dto.FileUploadDTO;
import cn.lili.modules.goods.fallback.BrandFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @author: ftyy
 * @date: 2022-01-14 10:38
 * @description: 上传Client
 */
@FeignClient(name = ServiceConstant.RESOURCE_SERVICE, contextId = "upload", fallback = BrandFallback.class)
public interface UploadClient {

    /**
     * 上传数据
     *
     * @return 更新结果
     */
    @PutMapping("/feign/file/upload")
    String upload(@RequestBody FileUploadDTO fileUploadDTO);

    @PostMapping("/feign/file/uploadByUrl")
    String uploadByUrl(@RequestBody FileUploadDTO fileUploadDTO);

    @PutMapping("/feign/file/upload/file")
    String uploadFile(@RequestBody FileUploadDTO fileUploadDTO);

    @DeleteMapping("/feign/filt/remove")
    void remove(@RequestBody List<String> keys);

    /**
     * 根据文件URL列表查询文件信息
     *
     * @param urls 文件URL列表
     * @return 文件信息列表
     */
    @PostMapping("/feign/file/getByUrls")
    List<cn.lili.modules.file.entity.File> getFilesByUrls(@RequestBody List<String> urls);
    
    /**
     * 删除文件（包括数据库记录和OSS文件）
     *
     * @param fileKeys 文件key列表
     */
    @PostMapping("/feign/file/deleteFiles")
    void deleteFiles(@RequestBody List<String> fileKeys);
}
