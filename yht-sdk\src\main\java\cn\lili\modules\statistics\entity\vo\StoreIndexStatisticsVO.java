package cn.lili.modules.statistics.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 店铺首页数据
 *
 * <AUTHOR>
 * @since 2021/3/17 4:04 下午
 */
@Data
public class StoreIndexStatisticsVO {

    @Schema(title = "商品总数量")
    private Long goodsNum;
    @Schema(title = "订单总数量")
    private Integer orderNum;
    @Schema(title = "订单总额")
    private Double orderPrice;
    @Schema(title = "访客数UV")
    private Integer storeUV;

    @Schema(title = "待付款订单数量")
    private Long unPaidOrder;
    @Schema(title = "待发货订单数量")
    private Long unDeliveredOrder;

    @Schema(title = "采购待支付订单数量")
    private Long procureUnpaid;
    @Schema(title = "待收货订单数量")
    private Long deliveredOrder;

    @Schema(title = "待处理退货数量")
    private Long returnGoods;
    @Schema(title = "待处理退款数量")
    private Long returnMoney;
    @Schema(title = "待回复评价数量")
    private Long memberEvaluation;
    @Schema(title = "待处理交易投诉数量")
    private Long complaint;

    @Schema(title = "待上架商品数量")
    private Long waitUpper;
    @Schema(title = "待审核商品数量")
    private Long waitAuth;

    @Schema(title = "可参与秒杀活动数量")
    private Long seckillNum;
    @Schema(title = "未对账结算单数量")
    private Long waitPayBill;

    /**
     * 商品收藏数
     */
    @Schema(title = "商品收藏数")
    private Long goodsCollectionNum;

    /**
     * 图片下载率统计
     */
    @Schema(title = "图片下载率统计")
    private Double imageDownloadRate;

    /**
     * 图片下载率统计
     */
    @Schema(title = "图片下载率次数")
    private Long imageDownloadCount;

    /**
     * 店铺关注/收藏数
     */
    @Schema(title = "店铺收藏数")
    private Long storeCollectionNum;



}
