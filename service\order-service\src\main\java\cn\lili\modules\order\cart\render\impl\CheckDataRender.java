package cn.lili.modules.order.cart.render.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.utils.CurrencyUtil;
import cn.lili.modules.goods.client.CategoryClient;
import cn.lili.modules.goods.client.GoodsClient;
import cn.lili.modules.goods.client.GoodsCollectionClient;
import cn.lili.modules.goods.entity.dos.GoodsSku;
import cn.lili.modules.goods.entity.dto.Wholesale;
import cn.lili.modules.goods.entity.enums.GoodsAuthEnum;
import cn.lili.modules.goods.entity.enums.GoodsMarketEnum;
import cn.lili.modules.goods.entity.enums.SalesModeEnum;
import cn.lili.modules.order.cart.entity.dto.TradeDTO;
import cn.lili.modules.order.cart.entity.enums.DeliveryMethodEnum;
import cn.lili.modules.order.cart.entity.enums.RenderStepEnums;
import cn.lili.modules.order.cart.entity.vo.CartSkuVO;
import cn.lili.modules.order.cart.entity.vo.CartVO;
import cn.lili.modules.order.cart.render.CartRenderStep;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dto.PriceDetailDTO;
import cn.lili.modules.order.order.entity.dto.ServiceFeeDTO;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.payment.client.WalletPointClient;
import cn.lili.modules.promotion.client.MemberCouponClient;
import cn.lili.modules.promotion.client.PromotionGoodsClient;
import cn.lili.modules.promotion.entity.dos.Coupon;
import cn.lili.modules.promotion.entity.dos.Pintuan;
import cn.lili.modules.promotion.entity.dos.PointsGoods;
import cn.lili.modules.promotion.entity.dos.PromotionGoods;
import cn.lili.modules.promotion.entity.dto.search.PromotionGoodsSearchParams;
import cn.lili.modules.promotion.entity.enums.PromotionsStatusEnum;
import cn.lili.modules.promotion.entity.vos.CouponVO;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import cn.lili.modules.system.client.ServiceFeeClient;
import cn.lili.modules.system.entity.dos.ServiceFee;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 商品有效性校验 && 第一次更具逻辑中的店铺进行拆单
 *
 * <AUTHOR>
 * @since 2020-07-02 14:47
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CheckDataRender implements CartRenderStep {

    private final GoodsClient goodsClient;

    private final GoodsCollectionClient goodsCollectionClient;

    private final OrderService orderService;

    private final PromotionGoodsClient promotionGoodsClient;

    private final CategoryClient categoryClient;

    private final WalletPointClient walletPointClient;

    private final MemberCouponClient memberCouponClient;

    private final ServiceFeeClient serviceFeeClient;

    @Override
    public RenderStepEnums step() {
        return RenderStepEnums.CHECK_DATA;
    }

    @Override
    public void render(TradeDTO tradeDTO) {

        //价格第一次处理
        prePrice(tradeDTO);

        //销售模式处理
        preSaleModel(tradeDTO);

        //预校验
        preCalibration(tradeDTO);

        //校验商品有效性
        checkData(tradeDTO);

        //店铺分组数据初始化
        groupStore(tradeDTO);

        //设置分类信息，以及分类佣金比例信息
        setCategoryInformation(tradeDTO);
    }

    /**
     * 预校验
     *
     * @param tradeDTO 交易信息
     */
    private void prePrice(TradeDTO tradeDTO) {
        tradeDTO.getSkuList().forEach(cartSkuVO -> cartSkuVO.getPriceDetailDTO().setGoodsPrice(CurrencyUtil.mul(cartSkuVO.getGoodsSku().getPrice(),
                cartSkuVO.getNum())));
    }

    /**
     * 校验商品属性
     *
     * @param tradeDTO 购物车视图
     */
    private void checkData(TradeDTO tradeDTO) {
        //循环购物车中的商品
        for (CartSkuVO cartSkuVO : tradeDTO.getSkuList()) {

            //如果失效，确认sku为未选中状态
            if (Boolean.TRUE.equals(cartSkuVO.getInvalid())) {
                //设置购物车未选中
                cartSkuVO.setChecked(false);
            }

            //缓存中的商品信息
            GoodsSku dataSku = goodsClient.getGoodsSkuByIdFromCache(cartSkuVO.getGoodsSku().getId());

            //商品不可用状态判定(商品不存在、商品未审核通过、商品未上架)
            boolean checkDisableGoodsStatus =
                    dataSku == null || !GoodsAuthEnum.PASS.name().equals(dataSku.getAuthFlag()) || !GoodsMarketEnum.UPPER.name().equals(dataSku.getMarketEnable());
            //商品有效性判定(商品不为空、商品更新时间不为空、购物车商品更新时间不为空、商品更新时间在购物车商品更新时间之后)
            boolean checkGoodsValid =
                    dataSku != null && dataSku.getUpdateTime() != null && cartSkuVO.getGoodsSku().getUpdateTime() != null && dataSku.getUpdateTime().after(cartSkuVO.getGoodsSku().getUpdateTime());

            // 判断商品是否处于可用状态。如果商品可用，则尝试获取商品的最新促销信息。
            if (!checkDisableGoodsStatus) {
                // 获取商品的最新促销信息
                CartSkuVO currentGoodsPromotion = promotionGoodsClient.getCurrentGoodsPromotion(cartSkuVO);
                // 如果商品有促销信息，则更新购物车中的商品信息
                if (currentGoodsPromotion != null && CollUtil.isNotEmpty(currentGoodsPromotion.getPromotionMap())) {
                    cartSkuVO.setPromotionMap(currentGoodsPromotion.getPromotionMap());
                    cartSkuVO.getGoodsSku().setPromotionFlag(currentGoodsPromotion.getGoodsSku().getPromotionFlag());
                    cartSkuVO.getGoodsSku().setPromotionPrice(currentGoodsPromotion.getGoodsSku().getPromotionPrice());
                    checkMinusLimit(cartSkuVO);
                } else {
                    cartSkuVO.setPromotionMap(null);
                    cartSkuVO.getGoodsSku().setPromotionFlag(null);
                    cartSkuVO.getGoodsSku().setPromotionPrice(null);
                }
            }

            // 判断商品是否需要更新信息或标记为失效
            if ((checkDisableGoodsStatus || checkGoodsValid)) {
                // 表示商品信息已更新，需要更新购物车中的商品信息
                if (checkGoodsValid) {
                    // 更新购物车中的商品信息
                    CartSkuVO newCartSkuVO = new CartSkuVO(dataSku);
                    newCartSkuVO.setCartType(tradeDTO.getCartSceneEnum());
                    newCartSkuVO.setNum(cartSkuVO.getNum());
                    newCartSkuVO.setSubTotal(CurrencyUtil.mul(newCartSkuVO.getPurchasePrice(), cartSkuVO.getNum()));
                    newCartSkuVO.setPromotionMap(cartSkuVO.getPromotionMap());
                    newCartSkuVO.setCollectFlag(goodsCollectionClient.isCollection(dataSku.getId()));
                    newCartSkuVO.setChecked(cartSkuVO.getChecked());
                    BeanUtils.copyProperties(newCartSkuVO, cartSkuVO);
                    newCartSkuVO.getPriceDetailDTO().setGoodsPrice(CurrencyUtil.mul(dataSku.getPrice(), cartSkuVO.getNum()));
                    log.info("商品信息已更新，更新后的商品信息为：{}", cartSkuVO);
                }
                // 表示商品已失效，需要标记购物车中的商品为失效
                if (checkDisableGoodsStatus) {
                    //设置购物车未选中
                    cartSkuVO.setChecked(false);
                    //设置购物车此sku商品已失效
                    cartSkuVO.setInvalid(true);
                    //设置失效消息
                    cartSkuVO.setErrorMessage("商品已下架");
                }
                continue;
            }
            //商品库存判定
            if (dataSku.getQuantity() < cartSkuVO.getNum()) {
                //设置购物车未选中
                cartSkuVO.setChecked(false);
                //设置失效消息
                cartSkuVO.setErrorMessage("商品库存不足,现有库存数量[" + dataSku.getQuantity() + "]");
            }
            //如果存在商品促销活动，则判定商品促销状态
            if (!PromotionTypeEnum.POINTS_GOODS.equals(cartSkuVO.getPromotionTypeEnum()) && (CollUtil.isNotEmpty(cartSkuVO.notFilterPromotionMap()) || Boolean.TRUE.equals(cartSkuVO.getGoodsSku().getPromotionFlag()))) {
                Double goodsPrice = cartSkuVO.getGoodsSku().getPromotionFlag() != null && cartSkuVO.getGoodsSku().getPromotionFlag() ?
                        cartSkuVO.getGoodsSku().getPromotionPrice() : cartSkuVO.getGoodsSku().getPrice();
                cartSkuVO.setUtilPrice(goodsPrice);
                cartSkuVO.setSubTotal(CurrencyUtil.mul(cartSkuVO.getUtilPrice(), cartSkuVO.getNum()));
            }

            if (cartSkuVO.getChecked() && cartSkuVO.getIsServiceFee()) {
                tradeDTO.setNeedServiceFee(true);
            }
        }
    }

    /**
     * 店铺分组
     *
     * @param tradeDTO 交易信息
     */
    private void groupStore(TradeDTO tradeDTO) {
        //渲染的购物车
        List<CartVO> cartList = new ArrayList<>();
        List<ServiceFeeDTO> serviceFeeDTOs = new ArrayList<>();

        //根据店铺分组
        Map<String, List<CartSkuVO>> storeCollect = tradeDTO.getSkuList().stream().collect(Collectors.groupingBy(CartSkuVO::getStoreId));
        for (Map.Entry<String, List<CartSkuVO>> storeCart : storeCollect.entrySet()) {
            if (!storeCart.getValue().isEmpty()) {
                //根据销售类型再次分类
                CartVO cartVO = new CartVO(storeCart.getValue().getFirst());
                if (tradeDTO.getDeliveryMethodEnum() == null) {
                    cartVO.setDeliveryMethodEnum(DeliveryMethodEnum.LOGISTICS);
                } else {
                    cartVO.setDeliveryMethodEnum(tradeDTO.getDeliveryMethodEnum());
                }
                cartVO.setSkuList(storeCart.getValue());

                if (!cartVO.getSelfOperated() && CharSequenceUtil.isEmpty(tradeDTO.getScene())) {
                    // 服务费
                    if (CollectionUtils.isNotEmpty(tradeDTO.getServiceFeeDTOList())) {
                        List<ServiceFeeDTO> serviceFeeDTOList = tradeDTO.getServiceFeeDTOList().stream().filter(serviceFeeDTO -> serviceFeeDTO.getStoreId().equals(cartVO.getStoreId())).toList();
                        if (CollectionUtils.isNotEmpty(serviceFeeDTOList)) {
                            cartVO.setServiceFeeDTOList(serviceFeeDTOList);
                            cartVO.getPriceDetailDTO().setServiceFeeDTOList(serviceFeeDTOList);
                        }else {
                            if(CollectionUtils.isEmpty(cartVO.getServiceFeeDTOList())) {
                                getServiceFee(tradeDTO, cartVO);
                            }
                        }
                    }else {
                        getServiceFee(tradeDTO, cartVO);
                    }
                    serviceFeeDTOs.addAll(cartVO.getServiceFeeDTOList());
                }

                try {
                    //筛选属于当前店铺的优惠券
                    storeCart.getValue().forEach(i -> i.getPromotionMap().forEach((key, value) -> {
                        if (key.contains(PromotionTypeEnum.COUPON.name())) {
                            JSONObject promotionsObj = JSONUtil.parseObj(value);
                            Coupon coupon = JSONUtil.toBean(promotionsObj, Coupon.class);
                            if (key.contains(PromotionTypeEnum.COUPON.name()) && coupon.getStoreId().equals(storeCart.getKey())) {
                                cartVO.getCanReceiveCoupon().add(new CouponVO(coupon));
                            }
                        }
                    }));

                    //去重并检测是否超过领取限制
                    Set<String> ids = new HashSet<>();
                    List<CouponVO> checkList = new ArrayList<>();
                    for (CouponVO couponVO : cartVO.getCanReceiveCoupon().stream()
                            .filter(p -> ids.add(p.getId()))
                            .toList()) {
                        Long memberCouponCount = memberCouponClient.getMemberCouponCount(UserContext.getCurrentId(), couponVO.getId());
                        if(memberCouponCount < couponVO.getCouponLimitNum()){
                            checkList.add(couponVO);
                        }
                    }
                    cartVO.setCanReceiveCoupon(checkList);
                } catch (Exception e) {
                    log.error("筛选属于当前店铺的优惠券发生异常！", e);
                }
                storeCart.getValue().stream().filter(i -> Boolean.TRUE.equals(i.getChecked())).findFirst().ifPresent(cartSkuVO -> cartVO.setChecked(true));
                cartList.add(cartVO);
            }
        }
        tradeDTO.setServiceFeeDTOList(serviceFeeDTOs);
        tradeDTO.setCartList(cartList);
    }

    public void getServiceFee(TradeDTO tradeDTO, CartVO cartVO) {
        List<ServiceFeeDTO> serviceFeeDTOList = new ArrayList<>();
        // 获取默认的服务费
        List<ServiceFee> serviceFeeList = serviceFeeClient.getDefaultServiceFeeList();
        if (CollectionUtils.isNotEmpty(serviceFeeList)) {
            List<String> parentIds = serviceFeeList.stream()
                    .map(ServiceFee::getParentId)
                    .filter(parentId -> !parentId.equals("0")).toList();
            List<ServiceFee> parentServiceFeeList = serviceFeeClient.getServiceFeeListByIds(parentIds);
            serviceFeeList.forEach(serviceFee -> {
                ServiceFeeDTO serviceFeeDTO = new ServiceFeeDTO();
                if (!serviceFee.getParentId().equals("0")) {
                    ServiceFee pServiceFee = parentServiceFeeList.stream().filter(parentServiceFee -> parentServiceFee.getId().equals(serviceFee.getParentId())).findFirst().orElse(null);
                    assert pServiceFee != null;
                    serviceFeeDTO.setId(pServiceFee.getId());
                    serviceFeeDTO.setName(pServiceFee.getName());
                }
                serviceFeeDTO.setPrice(serviceFee.getPrice());
                serviceFeeDTO.setSubId(serviceFee.getId());
                serviceFeeDTO.setSubName(serviceFee.getName());
                serviceFeeDTO.setStoreId(cartVO.getStoreId());
                serviceFeeDTO.setNums(cartVO.getSkuList().stream().map(CartSkuVO::getNum).reduce(Integer::sum).orElse(0));
                serviceFeeDTO.setTotalPrice(serviceFee.getPrice() * serviceFeeDTO.getNums());
                serviceFeeDTOList.add(serviceFeeDTO);
            });
            cartVO.getPriceDetailDTO().setServiceFeeDTOList(serviceFeeDTOList);
            cartVO.setServiceFeeDTOList(serviceFeeDTOList);
        }
    }

    /**
     * 订单预校验
     * 1、自己拼团自己创建都拼团判定、拼团限购
     * 2、积分购买，积分足够与否
     *
     * @param tradeDTO 交易
     */
    private void preCalibration(TradeDTO tradeDTO) {
        //拼团订单预校验
        if (PromotionTypeEnum.PINTUAN.equals(tradeDTO.getPromotionType())) {
            //拼团判定，不能参与自己创建的拼团
            if (tradeDTO.getParentOrderSn() != null) {
                //订单接收
                Order parentOrder = orderService.getBySnNoAuth(tradeDTO.getParentOrderSn());
                //参与活动判定
                if (parentOrder.getBuyerId().equals(UserContext.getCurrentUser().getExtendId())) {
                    throw new ServiceException(ResultCode.PINTUAN_JOIN_ERROR);
                }
            }
            //判断拼团商品的限购数量
            if (tradeDTO.getSkuList().getFirst().getPromotionMap() != null && !tradeDTO.getSkuList().getFirst().getPromotionMap().isEmpty()) {
                Optional<Entry<String, Object>> pintuanPromotions =
                        tradeDTO.getSkuList().getFirst().getPromotionMap().entrySet().stream().filter(i -> i.getKey().contains(PromotionTypeEnum.PINTUAN.name())).findFirst();
                if (pintuanPromotions.isPresent()) {
                    JSONObject promotionsObj = JSONUtil.parseObj(pintuanPromotions.get().getValue());
                    Pintuan pintuan = promotionsObj.toBean(Pintuan.class);
                    Integer limitNum = pintuan.getLimitNum();
                    for (CartSkuVO cartSkuVO : tradeDTO.getSkuList()) {
                        if (limitNum != 0 && cartSkuVO.getNum() > limitNum) {
                            throw new ServiceException(ResultCode.PINTUAN_LIMIT_NUM_ERROR);
                        }
                    }
                }
            }
            //积分商品，判断用户积分是否满足
        } else if (PromotionTypeEnum.POINTS_GOODS.equals(tradeDTO.getPromotionType()) && tradeDTO.getSkuList().getFirst().getPromotionMap() != null && !tradeDTO.getSkuList().getFirst().getPromotionMap().isEmpty()) {
            //获取积分商品VO
            Optional<Map.Entry<String, Object>> pointsPromotions =
                    tradeDTO.getSkuList().getFirst().getPromotionMap().entrySet().stream().filter(i -> i.getKey().contains(PromotionTypeEnum.POINTS_GOODS.name())).findFirst();
            if (pointsPromotions.isPresent()) {
                JSONObject promotionsObj = JSONUtil.parseObj(pointsPromotions.get().getValue());
                PointsGoods pointsGoods = promotionsObj.toBean(PointsGoods.class);
                if (pointsGoods == null) {
                    throw new ServiceException(ResultCode.POINT_GOODS_ERROR);
                }
                if (walletPointClient.getUserPoint(tradeDTO.getMemberId()) < pointsGoods.getPoints()) {
                    throw new ServiceException(ResultCode.USER_POINTS_ERROR);
                }
            }

        }

    }


    /**
     * 商品销售模式特殊处理
     *
     * @param tradeDTO 交易信息
     */
    private void preSaleModel(TradeDTO tradeDTO) {
        // 寻找同goods下销售模式为批发的商品
        Map<String, List<CartSkuVO>> goodsGroup =
                tradeDTO.getSkuList().stream().filter(i -> i.getGoodsSku().getSalesModel().equals(SalesModeEnum.WHOLESALE.name()))
                        .collect(Collectors.groupingBy(i -> i.getGoodsSku().getGoodsId()));
        if (CollUtil.isNotEmpty(goodsGroup)) {
            goodsGroup.forEach((goodsId, cartSkus) -> {

                // 获取购买总数
                int sum = cartSkus.stream().filter(i -> Boolean.TRUE.equals(i.getChecked())).mapToInt(CartSkuVO::getNum).sum();
                // 匹配符合的批发规则
                Wholesale match = cartSkus.getFirst().getGoodsSku().machineWholesale(sum);
                if (match == null) {
                    for (CartSkuVO cartSkuVO : cartSkus) {
                        //设置购物车未选中
                        cartSkuVO.setChecked(false);
                        //设置失效消息
                        cartSkuVO.setErrorMessage("购买数量不足批量商品的起批量,起批量[" + cartSkus.get(0).getGoodsSku().getWholesaleList().get(0).getNum() + "]");
                    }
                } else {
                    //SPU 计算批发价格规则
                    if (cartSkus.getFirst().getGoodsSku().getSalesModel().equals(SalesModeEnum.WHOLESALE.name())) {
                        cartSkus.forEach(cartSkuVO -> {
                            // 将符合规则的商品设置批发价格
                            cartSkuVO.setPurchasePrice(match.getPrice());
                            cartSkuVO.getGoodsSku().setPrice(match.getPrice());
                            cartSkuVO.getGoodsSku().setCost(match.getPrice());
                            cartSkuVO.setUtilPrice(match.getPrice());
                            cartSkuVO.setSubTotal(CurrencyUtil.mul(cartSkuVO.getPurchasePrice(), cartSkuVO.getNum()));
                            cartSkuVO.getPriceDetailDTO().setGoodsPrice(cartSkuVO.getSubTotal());
                        });
                    }
                }
            });
        }
    }

    /**
     * 设置分类信息，以及分类佣金比例信息
     *
     * @param tradeDTO 交易信息
     */
    public void setCategoryInformation(TradeDTO tradeDTO) {

        for (CartSkuVO cartSkuVO : tradeDTO.getSkuList()) {

            PriceDetailDTO priceDetailDTO = cartSkuVO.getPriceDetailDTO();
            //平台佣金根据分类计算
            String categoryId = cartSkuVO.getGoodsSku().getCategoryPath().substring(cartSkuVO.getGoodsSku().getCategoryPath().lastIndexOf(",") + 1);
            if (CharSequenceUtil.isNotEmpty(categoryId)) {
                Double commissionRate = categoryClient.getById(categoryId).getCommissionRate();
                priceDetailDTO.setStoreCommissionPoint(CurrencyUtil.div(commissionRate, 100));
                priceDetailDTO.setSupplierCommissionPoint(CurrencyUtil.div(commissionRate, 100));
            }
        }

    }

    private void checkMinusLimit(CartSkuVO cartSkuVO){
        PromotionGoodsSearchParams params = new PromotionGoodsSearchParams();
        params.setSkuId(cartSkuVO.getGoodsSku().getId());
        params.setPromotionStatus(PromotionsStatusEnum.START.name());
        params.setPromotionType(PromotionTypeEnum.MINUS.name());
        PromotionGoods promotionsGoods = promotionGoodsClient.getPromotionsGoods(params);
        if(promotionsGoods == null){
            return;
        }
        if (cartSkuVO.getNum() > promotionsGoods.getLimitNum()) {
            //设置购物车未选中
            cartSkuVO.setChecked(false);
            cartSkuVO.setErrorMessage("超过购买限制，最多可购买 " + promotionsGoods.getLimitNum() + "个商品");
        }
    }

}
