package cn.lili.modules.order.order.entity.dos;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.ClientTypeEnum;
import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.BeanUtil;
import cn.lili.common.utils.CurrencyUtil;
import cn.lili.common.utils.GsonUtils;
import cn.lili.modules.goods.entity.enums.GoodsTypeEnum;
import cn.lili.modules.order.cart.entity.dto.TradeCouponDTO;
import cn.lili.modules.order.cart.entity.dto.TradeDTO;
import cn.lili.modules.order.cart.entity.enums.DeliveryMethodEnum;
import cn.lili.modules.order.cart.entity.vo.CartSkuVO;
import cn.lili.modules.order.cart.entity.vo.CartVO;
import cn.lili.modules.order.order.entity.dto.PriceDetailDTO;
import cn.lili.modules.order.order.entity.dto.ServiceFeeDTO;
import cn.lili.modules.order.order.entity.enums.DeliverStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderTypeEnum;
import cn.lili.modules.order.order.entity.enums.PayStatusEnum;
import cn.lili.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 订单
 *
 * <AUTHOR>
 * @since 2020/11/17 7:30 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("li_order")
@Schema(title = "订单")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Order extends BaseStandardEntity {


    private static final long serialVersionUID = 2233811628066468683L;
    @Schema(title = "订单编号")
    private String sn;

    @Schema(title = "交易编号 关联Trade")
    private String tradeSn;

    @Schema(title = "店铺ID")
    private String storeId;

    @Schema(title = "店铺名称")
    private String storeName;

    @Schema(title = "购买人ID")
    private String buyerId;

    @Schema(title = "用户昵称")
    private String nickname;

    /**
     * @see OrderStatusEnum
     */
    @Schema(title = "订单状态")
    private String orderStatus;

    /**
     * @see PayStatusEnum
     */
    @Schema(title = "付款状态")
    private String payStatus;
    /**
     * @see DeliverStatusEnum
     */
    @Schema(title = "货运状态")
    private String deliverStatus;

    @Schema(title = "是否售后中")
    private Boolean afterSaleApplying;

    @Schema(title = "是否为全额退款")
    private Boolean fullRefund;


    @Schema(title = "第三方支付发起交易号")
    private String outTradeNo;

    @Schema(title = "第三方平台付款流水号")
    private String transactionId;


    @Schema(title = "支付方式")
    private String paymentMethod;

    @Schema(title = "支付时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;

    @Schema(title = "收件人姓名")
    private String consigneeName;

    @Schema(title = "收件人手机")
    private String consigneeMobile;

    /**
     * @see DeliveryMethodEnum
     */
    @Schema(title = "配送方式")
    private String deliveryMethod;

    @Schema(title = "地址名称， '，'分割")
    private String consigneeAddressPath;

    @Schema(title = "地址id，'，'分割 ")
    private String consigneeAddressIdPath;

    @Schema(title = "详细地址")
    private String consigneeDetail;

    @Schema(title = "总价格")
    private Double flowPrice;

    @Schema(title = "商品价格")
    private Double goodsPrice;

    @Schema(title = "运费")
    private Double freightPrice;

    @Schema(title = "优惠的金额")
    private Double discountPrice;

    @Schema(title = "修改价格")
    private Double updatePrice;

    @Schema(title = "发货单号")
    private String logisticsNo;

    @Schema(title = "物流公司CODE")
    private String logisticsCode;

    @Schema(title = "物流公司名称")
    private String logisticsName;

    @Schema(title = "订单商品总重量")
    private Double weight;

    @Schema(title = "商品数量")
    private Integer goodsNum;

    @Schema(title = "买家订单备注")
    private String remark;

    @Schema(title = "卖家订单备注")
    private String sellerRemark;

    @Schema(title = "订单取消原因")
    private String cancelReason;

    @Schema(title = "完成时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    @Schema(title = "送货时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date logisticsTime;

    /**
     * @see ClientTypeEnum
     */
    @Schema(title = "订单来源")
    private String clientType;

    @Schema(title = "是否需要发票")
    private Boolean needReceipt;

    @Schema(title = "是否为其他订单下的订单，如果是则为依赖订单的sn，否则为空")
    private String parentOrderSn = "";

    @Schema(title = "促销活动ID")
    private String promotionId;

    /**
     * @see OrderTypeEnum
     */
    @Schema(title = "订单类型")
    private String orderType;

    /**
     * @see PromotionTypeEnum
     */
    @Schema(title = "订单促销类型")
    private String orderPromotionType;

    /**
     * @see cn.lili.modules.goods.entity.enums.SalesModeEnum
     */
    @Schema(title = "销售类型")
    private String saleModelType;

    @Schema(title = "价格详情")
    private String priceDetail;

    @Schema(title = "订单是否支持原路退回")
    private Boolean canReturn;

    @Schema(title = "自提点地址")
    private String storeAddressPath;

    @Schema(title = "自提点电话")
    private String storeAddressMobile;

    @Schema(title = "促销赠送积分")
    private Long giftPoint;

    @Schema(title = "分销员ID")
    private String firstDistributionId;

    @Schema(title = "一级分销商会员 ID")
    private String firstDistributionUserId;

    @Schema(title = "分销员名称")
    private String firstDistributionName;

    @Schema(title = "一级分销商绑定ID")
    private String firstDistributionBindId;

    @Schema(title = "分销员ID")
    private String secondaryDistributionId;

    @Schema(title = " 二级分销商会员 ID")
    private String secondaryDistributionUserId;

    @Schema(title = "分销员名称")
    private String secondaryDistributionName;

    @Schema(title = "二级分销商绑定ID")
    private String secondaryDistributionBindId;

    @Schema(title = "使用的店铺会员优惠券id(,)间隔")
    private String useStoreMemberCouponIds;

    @Schema(title = "使用的平台会员优惠券id")
    private String usePlatformMemberCouponId;

    @Schema(title = "供应商ID")
    private String supplierId;

    @Schema(title = "供应商名称")
    private String supplierName;

    @Schema(title = "代理订单")
    private Boolean isProxy = false;

    @Schema(title = "异常订单补差是否成功")
    private Boolean isPaidToSupplier = false;

    @Schema(title = "保留字段1", hidden = true)
    private String field1;

    @Schema(title = "保留字段2", hidden = true)
    private String field2;

    @Schema(title = "保留字段3", hidden = true)
    private String field3;

    @Schema(title = "保留字段4", hidden = true)
    private String field4;

    @Schema(title = "保留字段5", hidden = true)
    private String field5;

    @Schema(title = "扩展字段，可自由存储，数据库为text格式", hidden = true)
    private String ext;

    @Schema(title = "服务费")
    private Double serviceFee = 0D;

    @Schema(title = "备忘录")
    private String memo;

    /**
     * 构建订单
     *
     * @param cartVO   购物车VO
     * @param tradeDTO 交易DTO
     */
    public Order(CartVO cartVO, TradeDTO tradeDTO) {
        String oldId = this.getId();
        BeanUtil.copyProperties(tradeDTO, this);
        PriceDetailDTO cartPriceDetailDTO = cartVO.getPriceDetailDTO();
        cartPriceDetailDTO.setServiceFeeDTOList(tradeDTO.getPriceDetailDTO().getServiceFeeDTOList());
        BeanUtil.copyProperties(cartPriceDetailDTO, this);
        BeanUtil.copyProperties(cartVO, this);
        //填写订单类型
        this.setTradeType(cartVO, tradeDTO);
        setId(oldId);

        this.setIsProxy(!cartVO.getSelfOperated());

        if (CollectionUtils.isNotEmpty(cartPriceDetailDTO.getServiceFeeDTOList()) && this.getIsProxy()) {
            Double serviceFee = cartPriceDetailDTO.getServiceFeeDTOList().stream()
                    .filter(serviceFeeDTO -> serviceFeeDTO.getStoreId().equals(cartVO.getStoreId()))
                    .map(ServiceFeeDTO::getTotalPrice)
                    .reduce(Double::sum).orElse(0D);
            this.flowPrice = CurrencyUtil.add(this.flowPrice, serviceFee);
        }


        if (this.getIsProxy()) {
            this.serviceFee = tradeDTO.getPriceDetailDTO().getServiceFee();
        }

        this.buyerId = tradeDTO.getMemberId();
        this.nickname = tradeDTO.getMemberName();

        // 非直营店铺就是代发订单


        //this.isProxy = !cartVO.getSelfOperated();
        //判断是否为代理订单
        /*if (CharSequenceUtil.isNotEmpty(cartVO.getSupplierId())) {
            this.isProxy = true;
            // 如果是代理订单，且店铺订单结算金额大于0，则表示为普通订单，无需补差
            // 如果店铺结算金额小于 0，需要补差，扣除店铺钱包金额后自动
            this.isPaidToSupplier = this.getPriceDetailDTO().getSellerSettlementPrice() != null
                && this.getPriceDetailDTO().getSellerSettlementPrice() > 0;
        }*/
        //如果是供应商商品，则表明内部逻辑为采购逻辑，订单类型标记为采购单
        if (SceneEnums.SUPPLIER.name().equals(cartVO.getSkuList().getFirst().getGoodsSku().getScene())) {
            this.orderType = OrderTypeEnum.PURCHASE.name();
        }
        //设置默认支付状态
        this.setOrderStatus(OrderStatusEnum.UNPAID.name());
        this.setPayStatus(PayStatusEnum.UNPAID.name());
        this.setDeliverStatus(DeliverStatusEnum.UNDELIVERED.name());
        this.setTradeSn(tradeDTO.getSn());
        this.setRemark(cartVO.getRemark());
        this.setGiftPoint(cartVO.getGiftPoint());
        this.setFreightPrice(cartVO.getPriceDetailDTO().getFreightPrice());
        //会员收件信息
        if (tradeDTO.getUserAddress() != null) {
            this.setConsigneeAddressIdPath(tradeDTO.getUserAddress().getConsigneeAddressIdPath());
            this.setConsigneeAddressPath(tradeDTO.getUserAddress().getConsigneeAddressPath());
            this.setConsigneeDetail(tradeDTO.getUserAddress().getDetail());
            this.setConsigneeMobile(tradeDTO.getUserAddress().getMobile());
            this.setConsigneeName(tradeDTO.getUserAddress().getName());
            this.setDeliveryMethod(DeliveryMethodEnum.LOGISTICS.name());
        }
        //自提点信息
        if (DeliveryMethodEnum.SELF_PICK_UP.equals(cartVO.getDeliveryMethodEnum())
            && tradeDTO.getStoreAddress() != null) {
            this.setStoreAddressPath(
                tradeDTO.getStoreAddress().getConsigneeAddressPath() + tradeDTO.getStoreAddress().getDetail());
            this.setStoreAddressMobile(tradeDTO.getStoreAddress().getMobile());
            this.setConsigneeAddressIdPath("");
            this.setConsigneeAddressPath("");
            this.setConsigneeDetail("");
            this.setDeliveryMethod(DeliveryMethodEnum.SELF_PICK_UP.name());
        }
        //平台优惠券判定
        if (tradeDTO.getPlatformCoupon() != null) {
            this.setUsePlatformMemberCouponId(tradeDTO.getPlatformCoupon().getMemberCoupon().getId());
        }
        //店铺优惠券判定
        if (tradeDTO.getStoreCoupons() != null && !tradeDTO.getStoreCoupons().isEmpty()) {
            StringBuilder storeCouponIds = new StringBuilder();
            for (TradeCouponDTO value : tradeDTO.getStoreCoupons()) {
                storeCouponIds.append(value.getMemberCouponDTO().getMemberCoupon().getId()).append(",");
            }
            this.setUseStoreMemberCouponIds(storeCouponIds.toString());
        }
        if (cartVO.getDistribution1() != null) {
            this.setFirstDistributionId(cartVO.getDistribution1().getId());
            this.setFirstDistributionName(cartVO.getDistribution1().getMemberName());
            this.setFirstDistributionUserId(cartVO.getDistribution1().getMemberId());
            this.setFirstDistributionBindId(cartVO.getDistribution1().getDistributionBindId());
        }

        if (cartVO.getDistribution2() != null) {
            this.setSecondaryDistributionId(cartVO.getDistribution2().getId());
            this.setSecondaryDistributionName(cartVO.getDistribution2().getMemberName());
            this.setSecondaryDistributionUserId(cartVO.getDistribution2().getMemberId());
            this.setSecondaryDistributionBindId(cartVO.getDistribution2().getDistributionBindId());
        }
    }


    /**
     * 填写交易（订单）类型
     * 1.判断是普通、促销订单
     * 2.普通订单进行区分：实物订单、虚拟订单
     * 3.促销订单判断货物进行区分实物、虚拟商品。
     * 4.拼团订单需要填写父订单ID
     *
     * @param cartVO   购物车VO
     * @param tradeDTO 交易DTO
     */
    private void setTradeType(CartVO cartVO, TradeDTO tradeDTO) {

        //促销订单（拼团、积分）-判断购买的是虚拟商品还是实物商品
        String goodsType = cartVO.getCheckedSkuList().get(0).getGoodsSku().getGoodsType();
        if (CharSequenceUtil.isEmpty(goodsType) || goodsType.equals(GoodsTypeEnum.PHYSICAL_GOODS.name())) {
            this.setOrderType(OrderTypeEnum.NORMAL.name());
        } else {
            this.setOrderType(OrderTypeEnum.VIRTUAL.name());
        }
        //填写订单的促销类型

        this.setOrderPromotionType(getPromotionTypes(cartVO.getSkuList()));


        //判断是否为拼团订单，如果为拼团订单获取拼团ID，判断是否为主订单
        if (PromotionTypeEnum.PINTUAN.equals(tradeDTO.getPromotionType()) && cartVO.getCheckedSkuList().get(0).getPromotionMap() != null && !cartVO.getCheckedSkuList().get(0).getPromotionMap().isEmpty()) {
            Optional<String> pintuanPromotions =
                    cartVO.getCheckedSkuList().get(0).getPromotionMap().keySet().stream().filter(i -> i.contains(PromotionTypeEnum.PINTUAN.name())).findFirst();
            pintuanPromotions.ifPresent(s -> promotionId = s.split("-")[1]);
        }
        if(PromotionTypeEnum.MINUS.equals(tradeDTO.getPromotionType())){
            Optional<String> minusPromotions = cartVO.getCheckedSkuList().get(0).getPromotionMap().keySet().stream().filter(i -> i.contains(PromotionTypeEnum.MINUS.name())).findFirst();
            minusPromotions.ifPresent(s -> promotionId = s.split("-")[1]);
        }
    }

    /**
     * 获取促销类型
     *
     * @return 促销类型 "，" 分割
     */
    private String getPromotionTypes(List<CartSkuVO> skuList) {

        StringBuilder stringBuilder = new StringBuilder();
        skuList.forEach(item -> {
            if (item.getPromotionTypeEnum() != null) {
                stringBuilder.append(item.getPromotionTypeEnum().name()).append(",");
            }
        });

        if (!stringBuilder.isEmpty()) {
            return stringBuilder.substring(0, stringBuilder.length() - 1);
        }
        return "";

    }

    public PriceDetailDTO getPriceDetailDTO() {

        try {
            return GsonUtils.fromJson(priceDetail, PriceDetailDTO.class);
        } catch (Exception e) {
            return null;
        }
    }


    public String getFullAddress() {

        //对四个参数非空判定处理
        if (CharSequenceUtil.isEmpty(consigneeName) || CharSequenceUtil.isEmpty(consigneeMobile)
            || CharSequenceUtil.isEmpty(consigneeAddressPath) || CharSequenceUtil.isEmpty(consigneeDetail)) {
            return "";
        }

        return String.format("收货人：%s，手机号：%s，地址%s-%s", consigneeName, consigneeMobile, consigneeAddressPath, consigneeDetail);
    }

    public void setPriceDetailDTO(PriceDetailDTO priceDetail) {
        this.priceDetail = JSONUtil.toJsonStr(priceDetail);
    }


    public Boolean getFullRefund() {
        if (fullRefund == null) {
            return false;
        }
        return fullRefund;
    }
}