package cn.lili.modules.payment.entity.dto;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.CurrencyUtil;
import cn.lili.modules.payment.entity.enums.PaymentClientEnum;
import cn.lili.modules.payment.entity.enums.PaymentMethodEnum;
import cn.lili.modules.payment.entity.enums.PaymentSceneEnums;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 支付参数
 *
 * <AUTHOR>
 * @since 2020/12/19 11:46
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayParam implements Serializable {

    @Serial
    private static final long serialVersionUID = -9059974890792063736L;

    @Schema(title = "支付人ID")
    private String payerId;

    @Schema(title = "合单支付编号：一般用于传递交易编号/充值单号/订单编号等")
    private String combineSn;

    @Schema(title = "支付标题")
    private String title;


    @NotNull
    @Schema(title = "支付方式")
    private PaymentMethodEnum paymentMethodEnum;

    @NotNull
    @Schema(title = "客户端类型")
    private PaymentClientEnum paymentClientEnum;

    @Schema(title = "支付场景")
    private PaymentSceneEnums paymentSceneEnums;


    @Schema(title = "支付订单参数")
    private List<PayParamItem> payParamItems;


    @Schema(title = "微信网页支付或者小程序支付时需传递id:无需传递，支付业务会进行填充")
    private String openid;

    @Schema(title = "微信网页支付或者小程序支付时需传递id:无需传递，支付业务会进行填充")
    private String wxAppid;

    @Schema(title = "是否付款给平台：默认false，一般无需传递，用于特殊场景-例如给平台充值")
    private Boolean isPaymentToPlatform;

    @Schema(title = "支付超时时间，默认3分钟")
    private Integer timeoutExpress;

    @Schema(title = "补贴金额")
    private Double subsidyAmount;

    public Double getSubsidyAmount() {
        if (subsidyAmount == null) {
            return 0.0;
        }
        return subsidyAmount;
    }
    //默认支付场景为交易
    public PaymentSceneEnums getPaymentSceneEnums() {
        return Objects.requireNonNullElse(this.paymentSceneEnums, PaymentSceneEnums.TRADE);
    }

    public String getTitle() {
        if (CharSequenceUtil.isEmpty(this.title)) {
            return "在线商城";
        }
        return title;
    }

    public String getPayerId() {

//        if (CharSequenceUtil.isEmpty(this.payerId)) {
//            throw new ServiceException(ResultCode.PARAMS_ERROR, "payerId 不能为空");
//        }
        return payerId;
    }

    public String getCombineSn() {
        if (CharSequenceUtil.isEmpty(this.combineSn)) {
            throw new ServiceException(ResultCode.PARAMS_ERROR, "combineSn 不能为空");
        }
        return combineSn;
    }

    public Double getTotalAmount() {
        List<Double> prices = this.getPayParamItems().stream().map(PayParamItem::getPrice).toList();
        return CurrencyUtil.add(prices);
    }

    public Double getServiceFee() {
        List<Double> prices = this.getPayParamItems().stream().map(PayParamItem::getServiceFee).toList();
        return CurrencyUtil.add(prices);
    }

    public PaymentMethodEnum getPaymentMethodEnum() {

        if (this.paymentMethodEnum == null) {
            throw new ServiceException(ResultCode.PARAMS_ERROR, "paymentMethodEnum 不能为空");
        }
        return paymentMethodEnum;
    }

    public PaymentClientEnum getPaymentClientEnum() {

        if (this.paymentClientEnum == null) {
            throw new ServiceException(ResultCode.PARAMS_ERROR, "paymentClientEnum 不能为空");
        }
        return paymentClientEnum;
    }

    public List<PayParamItem> getPayParamItems() {

        if (payParamItems == null || payParamItems.isEmpty()) {
            throw new ServiceException(ResultCode.PARAMS_ERROR, "payParamItems 不能为空");
        }
        return payParamItems;
    }


    public Boolean getPaymentToPlatform() {

        return Objects.requireNonNullElse(this.isPaymentToPlatform, false);
    }

    public Integer getTimeoutExpress() {
        if (timeoutExpress == null || timeoutExpress < 0) {
            return 3;
        }
        return timeoutExpress;
    }


    @Schema(title = "是否合单支付：合单支付，支付目标时商家账户，非合单支付则入账目标为平台")
    public Boolean getIsCombine() {
        return payParamItems.size() > 1;
    }

}
