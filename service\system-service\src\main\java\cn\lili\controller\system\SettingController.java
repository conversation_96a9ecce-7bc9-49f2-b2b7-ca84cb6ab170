package cn.lili.controller.system;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.aop.annotation.DemoSite;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.properties.SystemSettingProperties;
import cn.lili.common.utils.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.other.service.ArticleService;
import cn.lili.modules.page.entity.dos.Article;
import cn.lili.modules.system.entity.dos.Setting;
import cn.lili.modules.system.entity.dto.BaseSetting;
import cn.lili.modules.system.entity.dto.EmailSetting;
import cn.lili.modules.system.entity.dto.ExperienceSetting;
import cn.lili.modules.system.entity.dto.GoodsSetting;
import cn.lili.modules.system.entity.dto.ImSetting;
import cn.lili.modules.system.entity.dto.KuaidiSetting;
import cn.lili.modules.system.entity.dto.LogisticsSetting;
import cn.lili.modules.system.entity.dto.MpPrivacySetting;
import cn.lili.modules.system.entity.dto.OrderSetting;
import cn.lili.modules.system.entity.dto.OssSetting;
import cn.lili.modules.system.entity.dto.PointSetting;
import cn.lili.modules.system.entity.dto.PremiumMemberSetting;
import cn.lili.modules.system.entity.dto.ProviderCategorySetting;
import cn.lili.modules.system.entity.dto.SeckillSetting;
import cn.lili.modules.system.entity.dto.SmsSetting;
import cn.lili.modules.system.entity.dto.WithdrawalSetting;
import cn.lili.modules.system.entity.dto.connect.QQConnectSetting;
import cn.lili.modules.system.entity.dto.connect.WechatConnectSetting;
import cn.lili.modules.system.entity.dto.payment.AlipayPaymentSetting;
import cn.lili.modules.system.entity.dto.payment.PaymentSupportSetting;
import cn.lili.modules.system.entity.dto.payment.WechatPaymentSetting;
import cn.lili.modules.system.entity.enums.SettingEnum;
import cn.lili.modules.system.service.SettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统设置接口
 *
 * <AUTHOR>
 * @since 2020/11/26 15:53
 */
@RestController
@Tag(name = "系统设置接口")
@RequestMapping("/system/setting")
@RequiredArgsConstructor
public class SettingController {

    private final SettingService settingService;

    private final ArticleService articleService;

    private final SystemSettingProperties systemSettingProperties;

    @DemoSite
    @Operation(summary = "更新配置")
    @PutMapping(value = "/put/{key}")
    public ResultMessage<Object> saveConfig(@PathVariable String key, @RequestBody String configValue) {
        SettingEnum settingEnum = SettingEnum.valueOf(key);
        //获取系统配置
        Setting setting = settingService.getById(settingEnum.name());
        if (setting == null) {
            setting = new Setting();
            setting.setId(settingEnum.name());
        }

        setting.setSettingValue(configValue);
        settingService.saveUpdate(setting);
        return ResultUtil.success();
    }


    @DemoSite
    @Operation(summary = "查看配置")
    @GetMapping(value = "/get/{key}")
    public ResultMessage<Object> settingGet(@PathVariable String key) {
        return createSetting(key);
    }


    /**
     * 对配置进行过滤
     *
     * @param settingEnum
     * @param configValue
     */
    private String filter(SettingEnum settingEnum, String configValue) {
        if (settingEnum.equals(SettingEnum.POINT_SETTING)) {
            PointSetting pointSetting = JSONUtil.toBean(configValue, PointSetting.class);
            if (pointSetting.getPointSettingItems() != null && !pointSetting.getPointSettingItems().isEmpty()) {
                Collections.sort(pointSetting.getPointSettingItems());
                if (pointSetting.getPointSettingItems().size() > 4) {
                    pointSetting.setPointSettingItems(pointSetting.getPointSettingItems().subList(0, 4));
                }
            }
            configValue = JSONUtil.toJsonStr(pointSetting);
        }
        return configValue;
    }

    /**
     * 获取表单
     * 这里主要包含一个配置对象为空，导致转换异常问题的处理，解决配置项增加减少，带来的系统异常，无法直接配置
     *
     * @param key 配置key
     * @return 配置对象
     */
    private ResultMessage<Object> createSetting(String key) {
        SettingEnum settingEnum = SettingEnum.valueOf(key);
        Setting setting = settingService.get(key);

        ResultMessage<Object> resultMessage;
        switch (settingEnum) {
            case BASE_SETTING -> resultMessage = getResultMessage(setting, BaseSetting.class);
            case WITHDRAWAL_SETTING -> resultMessage = getResultMessage(setting, WithdrawalSetting.class);
            case EMAIL_SETTING -> resultMessage = getResultMessage(setting, EmailSetting.class);
            case GOODS_SETTING -> resultMessage = getResultMessage(setting, GoodsSetting.class);
            case KUAIDI_SETTING -> resultMessage = getResultMessage(setting, KuaidiSetting.class);
            case ORDER_SETTING -> resultMessage = getResultMessage(setting, OrderSetting.class);
            case OSS_SETTING -> resultMessage = getResultMessage(setting, OssSetting.class);
            case SMS_SETTING -> resultMessage = getResultMessage(setting, SmsSetting.class);
            case POINT_SETTING -> resultMessage = getResultMessage(setting, PointSetting.class);
            case QQ_CONNECT -> resultMessage = getResultMessage(setting, QQConnectSetting.class);
            case PAYMENT_SUPPORT -> resultMessage = getResultMessage(setting, PaymentSupportSetting.class);
            case ALIPAY_PAYMENT -> resultMessage = getResultMessage(setting, AlipayPaymentSetting.class);
            case WECHAT_CONNECT -> resultMessage = getResultMessage(setting, WechatConnectSetting.class);
            case WECHAT_PAYMENT -> {
                if (Boolean.TRUE.equals(systemSettingProperties.getIsDemoSite())) {
                    throw new ServiceException(ResultCode.DEMO_SITE_EXCEPTION);
                }
                resultMessage = getResultMessage(setting, WechatPaymentSetting.class);
            }
            case SECKILL_SETTING -> resultMessage = getResultMessage(setting, SeckillSetting.class);
            case EXPERIENCE_SETTING -> resultMessage = getResultMessage(setting, ExperienceSetting.class);
            case IM_SETTING -> resultMessage = getResultMessage(setting, ImSetting.class);
            case PRIVACY -> resultMessage = getResultMessage(setting, MpPrivacySetting.class);
            case LOGISTICS_SETTING -> resultMessage = getResultMessage(setting, LogisticsSetting.class);
            case PROVIDER_CATEGORY_SETTING -> resultMessage = getResultMessage(setting, ProviderCategorySetting.class);
            case PREMIUM_MEMBER_SETTING -> {
                if (setting == null) {
                    resultMessage = ResultUtil.data(new PremiumMemberSetting());
                } else {
                    PremiumMemberSetting bean = JSONUtil.toBean(setting.getSettingValue(), PremiumMemberSetting.class);
                    if (CharSequenceUtil.isNotBlank(bean.getArticleIds())) {
                        List<Article> articles = articleService.listByIds(List.of(bean.getArticleIds().split(",")));
                        bean.setArticleInfo(articles);
                    }
                    resultMessage = ResultUtil.data(bean);
                }
            }
            default -> throw new ServiceException(ResultCode.SETTING_NOT_TO_SET);
        }
        return resultMessage;
    }

    private <T> ResultMessage<Object> getResultMessage(Setting setting, Class<T> clazz) {
        if (setting == null) {
            try {
                return ResultUtil.data(clazz.getDeclaredConstructor().newInstance());
            } catch (Exception e) {
                throw new ServiceException(ResultCode.SETTING_NOT_TO_SET);
            }
        } else {
            return ResultUtil.data(JSONUtil.toBean(setting.getSettingValue(), clazz));
        }
    }
}