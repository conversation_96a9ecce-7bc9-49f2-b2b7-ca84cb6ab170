package cn.lili.modules.system.serviceimpl;

import cn.hutool.core.util.StrUtil;
import cn.lili.cache.Cache;
import cn.lili.cache.CachePrefix;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.BeanUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.goods.client.GoodsClient;
import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.goods.entity.dto.GoodsSearchParams;
import cn.lili.modules.store.entity.dos.FreightTemplate;
import cn.lili.modules.store.entity.dos.FreightTemplateChild;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import cn.lili.modules.system.mapper.FreightTemplateMapper;
import cn.lili.modules.system.service.FreightTemplateChildService;
import cn.lili.modules.system.service.FreightTemplateService;
import cn.lili.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 店铺运费模板业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/22 16:00
 */
@Service
@RequiredArgsConstructor
public class FreightTemplateServiceImpl extends ServiceImpl<FreightTemplateMapper, FreightTemplate> implements FreightTemplateService {
    /**
     * 配送子模板
     */
    private final FreightTemplateChildService freightTemplateChildService;

    /**
     * 商品
     */
    private final GoodsClient goodsClient;

    /**
     * 缓存
     */
    private final Cache cache;


    @Override
    public List<FreightTemplateVO> getFreightTemplateList(String storeId) {
        //先从缓存中获取运费模板，如果有则直接返回，如果没有则查询数据后再返回
        List<FreightTemplateVO> list = (List<FreightTemplateVO>) cache.get(CachePrefix.SHIP_TEMPLATE.getPrefix() + storeId);
        if (CollectionUtils.isNotEmpty(list)) {
            return list;
        }
        list = new ArrayList<>();
        //查询运费模板
        LambdaQueryWrapper<FreightTemplate> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(FreightTemplate::getStoreId, storeId);
        lambdaQueryWrapper.orderByDesc(FreightTemplate::getCreateTime);
        List<FreightTemplate> freightTemplates = this.baseMapper.selectList(lambdaQueryWrapper);
        if (!freightTemplates.isEmpty()) {
            //如果模板不为空则查询子模板信息
            for (FreightTemplate freightTemplate : freightTemplates) {
                FreightTemplateVO freightTemplateVO = new FreightTemplateVO();
                BeanUtil.copyProperties(freightTemplate, freightTemplateVO);
                List<FreightTemplateChild> freightTemplateChildren = freightTemplateChildService.getFreightTemplateChild(freightTemplate.getId());
                if (!freightTemplateChildren.isEmpty()) {
                    freightTemplateVO.setFreightTemplateChildList(freightTemplateChildren);
                }
                list.add(freightTemplateVO);
            }
        }
        cache.put(CachePrefix.SHIP_TEMPLATE.getPrefix() + storeId, list);
        return list;

    }


    @Override
    public List<FreightTemplateVO> getFreightTemplateList(String storeId,String createFrom) {
        //商品id不为空，获取商家的运费模板
        if(StrUtil.isNotBlank(storeId)){
            return getFreightTemplateList(storeId);
        }

        //以下获取管理端的运费模板

        //先从缓存中获取运费模板，如果有则直接返回，如果没有则查询数据后再返回
        List<FreightTemplateVO> list = (List<FreightTemplateVO>) cache.get(CachePrefix.SHIP_TEMPLATE.getPrefix() + "manager");
        if (CollectionUtils.isNotEmpty(list)) {
            return list;
        }
        list = new ArrayList<>();
        //查询运费模板
        LambdaQueryWrapper<FreightTemplate> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(FreightTemplate::getCreateFrom, createFrom);
        lambdaQueryWrapper.orderByDesc(FreightTemplate::getCreateTime);
        List<FreightTemplate> freightTemplates = this.baseMapper.selectList(lambdaQueryWrapper);
        if (!freightTemplates.isEmpty()) {
            //如果模板不为空则查询子模板信息
            for (FreightTemplate freightTemplate : freightTemplates) {
                FreightTemplateVO freightTemplateVO = new FreightTemplateVO();
                BeanUtil.copyProperties(freightTemplate, freightTemplateVO);
                List<FreightTemplateChild> freightTemplateChildren = freightTemplateChildService.getFreightTemplateChild(freightTemplate.getId());
                if (!freightTemplateChildren.isEmpty()) {
                    freightTemplateVO.setFreightTemplateChildList(freightTemplateChildren);
                }
                list.add(freightTemplateVO);
            }
        }
        cache.put(CachePrefix.SHIP_TEMPLATE.getPrefix() +  "manager", list);
        return list;

    }


    @Override
    public Page<FreightTemplate> getFreightTemplate(PageVO pageVo) {
        LambdaQueryWrapper<FreightTemplate> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(FreightTemplate::getStoreId, UserContext.getCurrentUser().getExtendId());
        return this.baseMapper.selectPage(PageUtil.initPage(pageVo), lambdaQueryWrapper);
    }

    @Override
    public FreightTemplateVO getFreightTemplate(String id) {
        FreightTemplateVO freightTemplateVO = new FreightTemplateVO();
        //获取运费模板
        FreightTemplate freightTemplate = this.getById(id);
        if (freightTemplate != null) {
            //复制属性
            BeanUtils.copyProperties(freightTemplate, freightTemplateVO);
            //填写运费模板子内容
            List<FreightTemplateChild> freightTemplateChildList = freightTemplateChildService.getFreightTemplateChild(id);
            freightTemplateVO.setFreightTemplateChildList(freightTemplateChildList);
        }
        return freightTemplateVO;
    }

    @Override
    @Transactional
    public FreightTemplateVO addFreightTemplate(FreightTemplateVO freightTemplateVO) {
        //获取当前登录商家账号
        AuthUser tokenUser = Objects.requireNonNull(UserContext.getCurrentUser());

        LambdaQueryWrapper<FreightTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FreightTemplate::getName, freightTemplateVO.getName());
        queryWrapper.eq(FreightTemplate::getStoreId, tokenUser.getExtendId());

        if (this.count(queryWrapper) > 0) {
            throw new ServiceException(ResultCode.FREIGHT_TEMPLATE_NAME_EXIST);
        }

        FreightTemplate freightTemplate = new FreightTemplate();
        //设置店铺ID
        freightTemplateVO.setStoreId(tokenUser.getExtendId());
        //复制属性
        BeanUtils.copyProperties(freightTemplateVO, freightTemplate);
        //添加运费模板
        this.save(freightTemplate);
        //给子模板赋父模板的id
        List<FreightTemplateChild> list = new ArrayList<>();
        //如果子运费模板不为空则进行新增
        if (freightTemplateVO.getFreightTemplateChildList() != null) {
            for (FreightTemplateChild freightTemplateChild : freightTemplateVO.getFreightTemplateChildList()) {
                freightTemplateChild.setFreightTemplateId(freightTemplate.getId());
                list.add(freightTemplateChild);
            }
            //添加运费模板子内容
            freightTemplateChildService.addFreightTemplateChild(list);
        }

        //更新缓存
        cache.remove(CachePrefix.SHIP_TEMPLATE.getPrefix() + tokenUser.getExtendId());
        cache.remove(CachePrefix.SHIP_TEMPLATE.getPrefix() +  "manager");
        return freightTemplateVO;
    }

    @Override
    @Transactional
    public FreightTemplateVO editFreightTemplate(FreightTemplateVO freightTemplateVO) {
        String cacheKey = CachePrefix.SHIP_TEMPLATE.getPrefix();
        LambdaQueryWrapper<FreightTemplate> queryWrapper = new LambdaQueryWrapper<>();
        if(freightTemplateVO.getCreateFrom().equals(SceneEnums.STORE.value())){
            //获取当前登录商家账号
            AuthUser tokenUser = Objects.requireNonNull(UserContext.getCurrentUser());

            if (freightTemplateVO.getId().equals(tokenUser.getExtendId())) {
                throw new ServiceException(ResultCode.USER_AUTHORITY_ERROR);
            }

            cacheKey = cacheKey+ tokenUser.getExtendId();

            queryWrapper.eq(FreightTemplate::getStoreId, tokenUser.getExtendId());

        }else{
            cacheKey = cacheKey +"manager";
            queryWrapper.eq(FreightTemplate::getCreateFrom, SceneEnums.MEMBER.value());
        }


        queryWrapper.eq(FreightTemplate::getName, freightTemplateVO.getName());

        queryWrapper.ne(FreightTemplate::getId, freightTemplateVO.getId());

        if (this.count(queryWrapper) > 0) {
            throw new ServiceException(ResultCode.FREIGHT_TEMPLATE_NAME_EXIST);
        }


        FreightTemplate freightTemplate = new FreightTemplate();
        //复制属性
        BeanUtils.copyProperties(freightTemplateVO, freightTemplate);
        //修改运费模板
        this.updateById(freightTemplate);
        //删除模板子内容
        freightTemplateChildService.removeFreightTemplate(freightTemplate.getId());
        //给子模板赋父模板的id
        List<FreightTemplateChild> list = new ArrayList<>();
        for (FreightTemplateChild freightTemplateChild : freightTemplateVO.getFreightTemplateChildList()) {
            freightTemplateChild.setId("");
            freightTemplateChild.setFreightTemplateId(freightTemplate.getId());
            list.add(freightTemplateChild);
        }
        //添加模板子内容
        freightTemplateChildService.addFreightTemplateChild(list);
        //更新缓存
        cache.remove(cacheKey);
        return null;
    }


    @Override
    @Transactional
    public boolean removeFreightTemplate(String id) {


        GoodsSearchParams goodsSearchParams = new GoodsSearchParams();
        goodsSearchParams.setTemplateId(id);
        List<Goods> goods = goodsClient.queryListByParams(goodsSearchParams);
        if(goods.size() > 0){
            throw new ServiceException(ResultCode.FREIGHT_TEMPLATE_BIND_NOT_EXIST);
        }

        String cacheKey = CachePrefix.SHIP_TEMPLATE.getPrefix();
        //获取当前登录商家账号
        AuthUser tokenUser = UserContext.getCurrentUser();

        LambdaQueryWrapper<FreightTemplate> lambdaQueryWrapper = Wrappers.lambdaQuery();

        if(tokenUser.getScene() == SceneEnums.STORE){
            cacheKey = cacheKey + tokenUser.getExtendId();
            lambdaQueryWrapper.eq(FreightTemplate::getStoreId, tokenUser.getExtendId());
        }else{
            cacheKey = cacheKey + "manager";
        }

        lambdaQueryWrapper.eq(FreightTemplate::getId, id);
        //如果删除成功则删除运费模板子项
        if (this.remove(lambdaQueryWrapper)) {
            cache.remove(cacheKey);
            return freightTemplateChildService.removeFreightTemplate(id);
        }
        return false;
    }
}