package cn.lili.controller.goods;

import cn.lili.common.utils.ResultUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.goods.entity.dto.OffShelfGoodsSearchParams;
import cn.lili.modules.goods.entity.vos.OffShelfGoodsCategoryCountVO;
import cn.lili.modules.goods.service.GoodsService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 下架商品控制器
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@RestController
@Tag(name = "下架商品管理")
@RequestMapping("/goods/offShelf")
public class OffShelfGoodsController {

    @Autowired
    private GoodsService goodsService;

    
    @Operation(summary = "分页查询下架商品")
    @GetMapping("/page")
    public ResultMessage<Page<Goods>> getOffShelfGoodsByPage(OffShelfGoodsSearchParams searchParams) {
        Page<Goods> page = goodsService.getOffShelfGoodsByPage(searchParams);
        return ResultUtil.data(page);
    }

    @Operation(summary = "获取下架商品分类统计")
    @GetMapping("/category/count")
    public ResultMessage<List<OffShelfGoodsCategoryCountVO>> getOffShelfGoodsCategoryCount(
            @RequestParam String storeId) {
        List<OffShelfGoodsCategoryCountVO> categoryCount = goodsService.getOffShelfGoodsCategoryCount(storeId);
        return ResultUtil.data(categoryCount);
    }

    @Operation(summary = "自动删除超过一年的下架商品")
    @PostMapping("/auto/delete")
    public ResultMessage<Integer> autoDeleteExpiredOffShelfGoods() {
        int deletedCount = goodsService.autoDeleteExpiredOffShelfGoods();
        return ResultUtil.data(deletedCount);
    }
}
