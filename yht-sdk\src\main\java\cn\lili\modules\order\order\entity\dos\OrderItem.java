package cn.lili.modules.order.order.entity.dos;

import cn.hutool.core.annotation.PropIgnore;
import cn.hutool.core.collection.CollUtil;
import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.utils.BeanUtil;
import cn.lili.common.utils.GsonUtils;
import cn.lili.common.utils.SnowFlake;
import cn.lili.modules.order.cart.entity.dto.TradeDTO;
import cn.lili.modules.order.cart.entity.vo.CartSkuVO;
import cn.lili.modules.order.cart.entity.vo.CartVO;
import cn.lili.modules.order.order.entity.dto.PriceDetailDTO;
import cn.lili.modules.order.order.entity.dto.ServiceFeeDTO;
import cn.lili.modules.order.order.entity.enums.CommentStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderComplaintStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderItemAfterSaleStatusEnum;
import cn.lili.modules.promotion.entity.vos.PromotionSkuVO;
import cn.lili.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

/**
 * 子订单
 *
 * <AUTHOR>
 * @since 2020/11/17 7:30 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("li_order_item")
@Schema(title = "子订单")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderItem extends BaseStandardEntity {

    @Serial
    private static final long serialVersionUID = 2108971190191410182L;

    @Schema(title = "订单编号")
    private String orderSn;

    @Schema(title = "子订单编号")
    private String sn;

    @Schema(title = "单价")
    private Double unitPrice;

    @Schema(title = "小记")
    private Double subTotal;

    @Schema(title = "商品ID")
    private String goodsId;

    @Schema(title = "货品ID")
    private String skuId;

    @Schema(title = "销售量")
    private Integer num;

    @Schema(title = "交易编号")
    private String tradeSn;

    @Schema(title = "图片")
    private String image;

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "分类ID")
    private String categoryId;

    @Schema(title = "快照id")
    private String snapshotId;

    @Schema(title = "规格json")
    private String specs;

    @Schema(title = "促销类型")
    private String promotionType;

    @Schema(title = "促销id")
    private String promotionId;

    @Schema(title = "销售金额")
    private Double goodsPrice;

    @Schema(title = "实际金额")
    private Double flowPrice;

    /**
     * @see CommentStatusEnum
     */
    @Schema(title = "评论状态:未评论(UNFINISHED),待追评(WAIT_CHASE),评论完成(FINISHED)，")
    private String commentStatus;

    /**
     * @see OrderItemAfterSaleStatusEnum
     */
    @Schema(title = "售后状态")
    private String afterSaleStatus;


    @Schema(title = "价格详情")
    private String priceDetail;

    /**
     * @see OrderComplaintStatusEnum
     */
    @Schema(title = "投诉状态")
    private String complainStatus;

    @Schema(title = "交易投诉id")
    private String complainId;

    @Schema(title = "退货商品数量")
    private Integer returnNum;

    @Schema(title = "供应商ID")
    private String supplierId;


    @Schema(title = "供应商名称")
    private String supplierName;

    @Schema(title = "供应商SKU ID")
    private String supplierSkuId;

    @Schema(title = "供应商商品ID")
    private String supplierGoodsId;

    @Schema(title = "发货数量")
    private Integer deliverNumber;

    @Schema(title = "sku成本价")
    private Double costPrice;

    @Schema(title = "服务费")
    private Double serviceFee = 0D;

    @Schema(title = "是否代发订单")
    private Boolean isProxy;


    public OrderItem(CartSkuVO cartSkuVO, CartVO cartVO, TradeDTO tradeDTO) {
        String oldId = this.getId();
        BeanUtil.copyProperties(cartSkuVO.getGoodsSku(), this);
        BeanUtil.copyProperties(cartSkuVO.getPriceDetailDTO(), this);
        BeanUtil.copyProperties(cartSkuVO, this);
        this.setId(oldId);
        if (cartSkuVO.getPriceDetailDTO().getJoinPromotion() != null && !cartSkuVO.getPriceDetailDTO().getJoinPromotion().isEmpty()) {
            String promotionType = CollUtil.join(cartSkuVO.getPriceDetailDTO().getJoinPromotion().stream().map(PromotionSkuVO::getPromotionType).toList(), ",");
            if(!PromotionTypeEnum.GIFT.name().equals(promotionType)){
                this.setPromotionType(CollUtil.join(cartSkuVO.getPriceDetailDTO().getJoinPromotion().stream().map(PromotionSkuVO::getPromotionType).toList(), ","));
            }
            this.setPromotionId(CollUtil.join(cartSkuVO.getPriceDetailDTO().getJoinPromotion().stream().map(PromotionSkuVO::getActivityId).toList(), ","));
        }else if(cartSkuVO.getPromotionTypeEnum() != null){
            this.setPromotionType(cartSkuVO.getPromotionTypeEnum().name());
        }
        this.setAfterSaleStatus(OrderItemAfterSaleStatusEnum.NEW.name());
        this.setCommentStatus(CommentStatusEnum.NEW.name());
        this.setComplainStatus(OrderComplaintStatusEnum.NEW.name());
        this.setPriceDetailDTO(cartSkuVO.getPriceDetailDTO());
        this.setOrderSn(cartVO.getSn());
        this.setTradeSn(tradeDTO.getSn());
        this.setImage(cartSkuVO.getGoodsSku().getThumbnail());
        this.setGoodsName(cartSkuVO.getGoodsSku().getGoodsName());
        this.setSkuId(cartSkuVO.getGoodsSku().getId());
        this.setCategoryId(cartSkuVO.getGoodsSku().getCategoryPath().substring(
                cartSkuVO.getGoodsSku().getCategoryPath().lastIndexOf(",") + 1
        ));
        this.setDeliverNumber(0);
        this.setGoodsPrice(cartSkuVO.getGoodsSku().getPrice());
        this.setUnitPrice(cartSkuVO.getPurchasePrice());
        this.setCostPrice(cartSkuVO.getGoodsSku().getCost());
        this.setSubTotal(cartSkuVO.getSubTotal());
        this.setSn(SnowFlake.createStr("OI"));
        this.setReturnNum(0);
    }

    public OrderItem(CartSkuVO cartSkuVO, CartVO cartVO, TradeDTO tradeDTO, Double costPrice) {
        String oldId = this.getId();
        BeanUtil.copyProperties(cartSkuVO.getGoodsSku(), this);
        BeanUtil.copyProperties(cartSkuVO.getPriceDetailDTO(), this);
        BeanUtil.copyProperties(cartSkuVO, this);
        this.setIsProxy(!cartVO.getSelfOperated());
        if (CollectionUtils.isNotEmpty(cartVO.getServiceFeeDTOList()) && this.getIsProxy()) {
            List<ServiceFeeDTO> serviceFeeDTOList = new ArrayList<>();
            for (ServiceFeeDTO serviceFeeDTO : cartVO.getServiceFeeDTOList()) {
                ServiceFeeDTO sfDto = new ServiceFeeDTO();
                BeanUtil.copyProperties(serviceFeeDTO, sfDto);
                sfDto.setNums(cartSkuVO.getNum());
                sfDto.setTotalPrice(serviceFeeDTO.getPrice() * cartSkuVO.getNum());
                serviceFeeDTOList.add(sfDto);
            }
            Double serviceFeeTotalPrice = serviceFeeDTOList.stream().mapToDouble(ServiceFeeDTO::getTotalPrice).sum();
            this.setServiceFee(serviceFeeTotalPrice);
            this.getPriceDetailDTO().setServiceFeeDTOList(serviceFeeDTOList);
        }
        this.setId(oldId);
        if (cartSkuVO.getPriceDetailDTO().getJoinPromotion() != null && !cartSkuVO.getPriceDetailDTO().getJoinPromotion().isEmpty()) {
            String promotionType = CollUtil.join(cartSkuVO.getPriceDetailDTO().getJoinPromotion().stream().map(PromotionSkuVO::getPromotionType).toList(), ",");
            if(!PromotionTypeEnum.GIFT.name().equals(promotionType)){
                this.setPromotionType(CollUtil.join(cartSkuVO.getPriceDetailDTO().getJoinPromotion().stream().map(PromotionSkuVO::getPromotionType).toList(), ","));
            }
            this.setPromotionId(CollUtil.join(cartSkuVO.getPriceDetailDTO().getJoinPromotion().stream().map(PromotionSkuVO::getActivityId).toList(), ","));
        }else if(cartSkuVO.getPromotionTypeEnum() != null){
            this.setPromotionType(cartSkuVO.getPromotionTypeEnum().name());
        }
        this.setAfterSaleStatus(OrderItemAfterSaleStatusEnum.NEW.name());
        this.setCommentStatus(CommentStatusEnum.NEW.name());
        this.setComplainStatus(OrderComplaintStatusEnum.NEW.name());
        this.setPriceDetailDTO(cartSkuVO.getPriceDetailDTO());
        this.setOrderSn(cartVO.getSn());
        this.setTradeSn(tradeDTO.getSn());
        this.setImage(cartSkuVO.getGoodsSku().getThumbnail());
        this.setGoodsName(cartSkuVO.getGoodsSku().getGoodsName());
        this.setSkuId(cartSkuVO.getGoodsSku().getId());
        this.setCategoryId(cartSkuVO.getGoodsSku().getCategoryPath().substring(
                cartSkuVO.getGoodsSku().getCategoryPath().lastIndexOf(",") + 1
        ));
        this.setDeliverNumber(0);
        this.setGoodsPrice(cartSkuVO.getGoodsSku().getPrice());
        this.setUnitPrice(cartSkuVO.getPurchasePrice());
        this.setCostPrice(costPrice);
        this.setSubTotal(cartSkuVO.getSubTotal());
        this.setSn(SnowFlake.createStr("OI"));
        this.setReturnNum(0);
    }


    @PropIgnore
    public PriceDetailDTO getPriceDetailDTO() {
        return GsonUtils.fromJson(priceDetail, PriceDetailDTO.class);
    }

    public void setPriceDetailDTO(PriceDetailDTO priceDetail) {
        this.priceDetail = GsonUtils.toJson(priceDetail);
    }

    public Integer getDeliverNumber() {
        if(deliverNumber == null){
            return 0;
        }
        return deliverNumber;
    }

    public Integer getReturnNum(){
        if(this.returnNum == null){
            return 0;
        }
        return returnNum;
    }
}