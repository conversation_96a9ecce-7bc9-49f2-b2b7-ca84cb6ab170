package cn.lili.modules.cashier.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.utils.DateUtil;
import cn.lili.modules.cashier.CashierInterface;
import cn.lili.modules.order.order.client.OrderClient;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.Trade;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.order.trade.client.TradeClient;
import cn.lili.modules.payment.entity.dto.CashierParam;
import cn.lili.modules.payment.entity.dto.PayParam;
import cn.lili.modules.payment.entity.dto.PayParamItem;
import cn.lili.modules.payment.entity.dto.PaymentCallback;
import cn.lili.modules.payment.entity.enums.PaymentSceneEnums;
import cn.lili.modules.system.client.SettingClient;
import cn.lili.modules.system.entity.dto.BaseSetting;
import cn.lili.modules.system.entity.dto.OrderSetting;
import cn.lili.modules.system.entity.enums.SettingEnum;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 交易收银台
 *
 * <AUTHOR>
 * @since 2021-01-25 20:00
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TradeCashier implements CashierInterface {

    private final TradeClient tradeClient;

    /**
     * 订单
     */
    private final OrderClient orderClient;

    /**
     * 设置
     */
    private final SettingClient settingClient;


    /**
     * 加工付款VO
     *
     * @param cashierParam 订单收银台参数
     * @return 支付参数
     */
    public CashierParam handlerCashierParams(CashierParam cashierParam) {
        //订单信息获取
        Trade trade = tradeClient.getBySn(cashierParam.getOrderSn());

        List<Order> orders = orderClient.getByTradeSn(cashierParam.getOrderSn());
        //交易有效性判定 交易不存在||交易状态为已支付||订单不存在||订单为空
        if (trade == null || trade.getOrderStatus().equals(OrderStatusEnum.PAID.name()) || orders == null
            || orders.isEmpty()) {
            throw new ServiceException(ResultCode.PAY_NOT_EXIST_ORDER);
        }
        for (Order order : orders) {
            //如果订单状态不是待付款，则抛出异常
            if (!order.getOrderStatus().equals(OrderStatusEnum.UNPAID.name())) {
                throw new ServiceException(ResultCode.PAY_BAN);
            }
        }
        cashierParam.setPrice(trade.getFlowPrice());

        try {
            BaseSetting baseSetting = JSONUtil.toBean(settingClient.get(SettingEnum.BASE_SETTING.name()).getSettingValue(), BaseSetting.class);
            cashierParam.setTitle(baseSetting.getSiteName());
        } catch (Exception e) {
            cashierParam.setTitle("多用户商城，在线支付");
        }
        String subject = "在线支付";
        cashierParam.setDetail(subject);

        cashierParam.setCreateTime(trade.getCreateTime());

        try {
            cashierParam.setAutoCancel(DateUtil.offsetMinute(trade.getCreateTime(), getAutoCancelTime()).getTime());
        } catch (Exception e) {
            cashierParam.setAutoCancel(0L);
        }
        return cashierParam;
    }


    /**
     * 生成支付参数
     *
     * @param payParam 交易信息
     */
    @Override
    public void generatePaymentParams(PayParam payParam) {

        // 支付参数
        Trade trade = tradeClient.getBySn(payParam.getCombineSn());

        //交易有效性判定
        assert trade != null;
        // 如果订单状态不是待付款，则抛出异常
        if(trade.getOrderStatus().equals(OrderStatusEnum.PAID.name())){
            throw new ServiceException(ResultCode.PAY_BAN);
        }


        // 支付参数
        List<PayParamItem> payParamItems = new ArrayList<>();
        orderClient.getByTradeSn(payParam.getCombineSn()).forEach(order -> {
            // 支付参数
            PayParamItem payParamItem = PayParamItem.builder()
                    .sn(order.getSn())
                    .price(order.getFlowPrice())
                    .payeeId(order.getStoreId())
                    .description(order.getStoreName())
                    .specialOrder(false)
                    .serviceFee(order.getServiceFee())
                // 平台补贴金额 平台结算金额为负数时为平台补贴
                    .subsidyAmount(order.getPriceDetailDTO().getPlatformSettlementPrice() < 0 ? -order.getPriceDetailDTO().getPlatformSettlementPrice() : 0)
                    .build();

            //如果订单结算金额为负数，则为特殊订单
            if (order.getPriceDetailDTO().getPlatformSettlementPrice() < 0
                || order.getPriceDetailDTO().getSellerSettlementPrice() < 0
                || order.getPriceDetailDTO().getSupplierSettlementPrice() < 0) {
                payParamItem.setSpecialOrder(true);
            }

            //如果header中携带小程序id则将信息传入
            if (CharSequenceUtil.isNotEmpty(UserContext.getAppId())) {
                payParamItem.setWxAppid(UserContext.getAppId());
            }
            payParamItems.add(payParamItem);
        });

        payParam.setPayParamItems(payParamItems);
        payParam.setTimeoutExpress(getAutoCancelTime());
    }

    /**
     * 支付成功
     *
     * @param paymentCallback 支付回调
     */
    @Override
    public void paymentCallback(PaymentCallback paymentCallback) {
        tradeClient.paymentCallback(paymentCallback);
    }

    @Override
    public Boolean paymentResult(String orderSn) {
        Trade trade = tradeClient.getBySn(orderSn);
        return trade != null && trade.getOrderStatus().equals(OrderStatusEnum.PAID.name());
    }

    /**
     * 服务的枚举类型
     */
    @Override
    public PaymentSceneEnums cashierEnum() {
        return PaymentSceneEnums.TRADE;
    }

    /**
     * 获取自动取消时间
     *
     * @return 自动取消时间
     */
    @Override
    public Integer getAutoCancelTime() {
        OrderSetting orderSetting = JSONUtil.toBean(settingClient.get(SettingEnum.ORDER_SETTING.name()).getSettingValue(), OrderSetting.class);
        return orderSetting.getAutoCancel();
    }
}