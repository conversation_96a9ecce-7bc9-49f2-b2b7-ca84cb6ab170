package cn.lili.modules.store.entity.dos;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.ValidateParamsUtil;
import cn.lili.modules.store.entity.enums.FreightTemplateEnum;
import cn.lili.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 运费模板
 *
 * <AUTHOR>
 * @since 2020/11/17 4:27 下午
 */
@Data
@TableName("li_freight_template")
@Schema(title = "运费模板")
@EqualsAndHashCode(callSuper = true)
public class FreightTemplate extends BaseStandardEntity {

    private static final long serialVersionUID = 2545078990582216985L;

    @Schema(title = "店铺ID", hidden = true)
    private String storeId;

    @NotEmpty(message = "模板名称不能为空")
    @Schema(title = "模板名称")
    private String name;

    @Schema(title = "创建来源，取值为STORE(商家)/MANAGER(管理)")
    private String createFrom = "STORE";

    /**
     * @see FreightTemplateEnum
     */
    @NotEmpty(message = "计价方式不能为空")
    @Schema(title = "计价方式：按件、按重量", allowableValues = "WEIGHT,NUM,FREE")
    private String pricingMethod;

    public boolean validateParams() {
        if (!ValidateParamsUtil.isValidString(name, 2, 50)) {
            ValidateParamsUtil.throwInvalidParamError("模板名称长度需要为2-50个字符");
        }
        if (createFrom.equals(SceneEnums.STORE.value()) && CharSequenceUtil.isBlank(storeId)) {
            ValidateParamsUtil.throwInvalidParamError("店铺ID不能为空");
        }
        if (!ValidateParamsUtil.isValidEnumValue(pricingMethod, FreightTemplateEnum.class)) {
            ValidateParamsUtil.throwInvalidParamError("计价方式不正确");
        }
        return true;
    }
}
