package cn.lili.modules.store.service;

import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.member.entity.dos.User;
import cn.lili.modules.member.entity.dto.CollectionDTO;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.dto.*;
import cn.lili.modules.store.entity.enums.StoreStatusEnum;
import cn.lili.modules.store.entity.vos.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 店铺业务层
 *
 * <AUTHOR>
 * @since 2020/11/18 11:45 上午
 */
public interface StoreService extends IService<Store> {
    /**
     * 增加店铺
     * 用于后台添加店铺
     *
     * @param storeApplyDTO 后台添加店铺信息
     * @return 店铺
     */
    Store add(StoreApplyDTO storeApplyDTO);

    /**
     * 分页条件查询
     * 用于展示店铺列表
     *
     * @return 店铺列表
     */
    Page<StoreVO> findByConditionPage(StoreSearchParams entity, PageVO page);

    /**
     * 列表查询
     *
     * @param searchParams 查询参数
     * @return 店铺列表
     */
    List<Store> list(StoreSearchParams searchParams);


    /**
     * 编辑店铺
     *
     * @param storeEditDTO 店铺修改信息
     * @return 店铺
     */
    Store edit(StoreEditDTO storeEditDTO);

    /**
     * 自定义创建店铺，直接持久化保存DO
     * 用于中台调用，非标准业务流程调用
     *
     * @param store 店铺
     * @return 店铺
     */
    Store customAdd(Store store);


    /**
     * 自定义编辑店铺，持久层修改店铺
     * 用于中台调用，非标准业务流程调用
     *
     * @param store 店铺信息
     * @return 返回店铺信息
     */
    Store customEdit(Store store);

    /**
     * 更新店铺状态
     *
     * @param ids    店铺集合
     * @param status 店铺状态
     * @param scene  场景
     * @return 操作结果
     */
    boolean updateStoreStatus(List<String> ids, StoreStatusEnum status, SceneEnums scene);

    /**
     * 更新店铺商品数量
     *
     * @param storeId 店铺ID
     * @param num     商品数量
     */
    void updateStoreGoodsNum(String storeId, Long num);

    /**
     * 更新店铺收藏数量
     *
     * @param collectionDTO 收藏信息
     */
    void updateStoreCollectionNum(CollectionDTO collectionDTO);

    /**
     * 更新评分
     *
     * @param storeId          店铺ID
     * @param deliveryScore    物流评分
     * @param serviceScore     服务评分
     * @param descriptionScore 描述评分
     */
    void updateScore(String storeId, String deliveryScore, String serviceScore, String descriptionScore);

    /**
     * 根据店铺ID获取店铺信息VO
     *
     * @param storeId 店铺ID
     * @return 店铺信息VO
     */
    StoreVO getStoreDetailVO(String storeId);

    /**
     * 根据当前已经登陆的店铺信息VO
     *
     * @return 店铺信息VO
     */
    StoreVO getOwnerStoreDetail();

    /**
     * 根据用户ID和场景获取店铺信息VO
     *
     * @param extendId 场景id
     * @param scene    场景
     * @return 店铺信息VO
     */
    Store getStoreDetailBySceneAndUserId(String extendId, SceneEnums scene);

    /**
     * 根据店铺ID获取店铺信息
     *
     * @param managerId 店铺ID
     * @param scene     场景
     * @return 店铺信息
     */
    Store getStoreByManagerId(String managerId, SceneEnums scene);

    /**
     * 修改商家设置
     *
     * @param storeSettingDTO 店铺设置信息
     * @return 店铺详情
     */
    Boolean editStoreSetting(StoreSettingDTO storeSettingDTO);

    /**
     * 获取店铺基本信息
     * 用于前端店铺信息展示
     *
     * @param storeId 店铺ID
     * @return 店铺基本信息
     */
    StoreBasicInfoVO getStoreBasicInfoDTO(String storeId);


    /**
     * 获取店铺经营范围
     *
     * @param storeId 店铺ID
     * @return 店铺经营范围
     */
    List<StoreManagementCategoryVO> goodsManagementCategory(String storeId);

    /**
     * 店铺公司信息
     *
     * @param storeId 店铺ID
     * @return 店铺其他信息
     */
    StoreLicenceVO getStoreLicenceVO(String storeId);

    /**
     * 更新店铺时，同步更新店铺内所有商品信息
     *
     * @param store 店铺信息
     */
    void updateStoreGoodsInfo(Store store);

    /**
     * 店铺所有者信息修改
     *
     * @param storeEditDTO 店铺信息
     */
    void ownerEdit(StoreOwnerEditDTO storeEditDTO);

    /**
     * 修改店铺设置
     * @param configKey 设置key
     * @param configValue 设置值
     */
    void editStoreSetting(String configKey, String configValue);

    /**
     * 注册店铺
     *
     * @param user 用户信息
     * @return 店铺信息
     */
    Store registerByUser(User user);

    /**
     * 更新店铺认证状态
     *
     * @param extendId 店铺ID
     * @param b        认证状态
     */
    void updateStoreVerifyStatus(String extendId, boolean b);

    /**
     * 获取店铺经营类目
     * @param storeId 店铺id
     */
    String getBusinessCategory(String storeId);

    /**
     * 通过域名获取店铺
     * @return
     */
    String getDomainStore();

    /**
     * 修改店铺域名
     * @param storeId 店铺Id
     * @param domain PC端域名
     * @param mDomain 移动端域名
     */
    void editDomain(String storeId, String domain, String mDomain);

    /**
     * 检查店铺域名信息
     * @param store 店铺信息
     */
    void checkStoreDomain(Store store);

    /**
     * 设置店铺标签
     * @param storeId 店铺ID
     * @param storeTag 店铺标签
     */
    void setStoreTag(String storeId, String storeTag);

    /**
     * 设置店铺实拍状态
     * @param storeId 店铺ID
     * @param realPhoto 是否实拍
     */
    void setStoreRealPhoto(String storeId, Boolean realPhoto);

    /**
     * 设置店铺发货快状态
     * @param storeId 店铺ID
     * @param fastDelivery 是否发货快
     */
    void setStoreFastDelivery(String storeId, Boolean fastDelivery);

    Page<StoreRankStatisticsVO> getStoreRankStatistics(PageVO page, String storeName, String sortField);

    /**
     * 更新店铺统计数据
     * @param storeId 店铺ID
     * @param num 数量
     * @param field 字段名称
     */
    void updateStoreStatistics(String storeId, Double num, String field);

    /**
     * 获取店铺详情
     * @param storeId 店铺ID
     * @return 店铺详情
     */
    StoreVO getStoreDetailById(String storeId);
}