package cn.lili.modules.order.order.entity.dto;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.order.order.entity.enums.CommentStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderComplaintStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderItemAfterSaleStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderTagEnum;
import cn.lili.modules.order.order.entity.enums.OrderTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 订单查询参数
 *
 * <AUTHOR>
 * @since 2020/11/17 4:33 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderSearchParams extends PageVO {

    @Serial
    private static final long serialVersionUID = -6380573339089959194L;

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "订单编号")
    private String orderSn;

    @Schema(title = "页面标签", example = "ALL:全部," + "WAIT_PAY:待付款," + "WAIT_ROG:待收货," + "CANCELLED:已取消," + "COMPLETE:已完成")
    private String tag;

    @Schema(title = "商家ID")
    private String storeId;

    @Schema(title = "是否为采购单")
    private boolean purchase;

    @Schema(title = "供应商ID")
    private String supplierId;

    @Schema(title = "购买人ID")
    private String buyerId;

    @Schema(title = "买家昵称")
    private String nickname;

    @Schema(title = "收货人")
    private String shipName;

    @Schema(title = "订单状态")
    private String orderStatus;

    @Schema(title = "订单状态")
    private String unOrderStatus;

    @Schema(title = "付款状态")
    private String payStatus;

    @Schema(title = "关键字 商品名称/买家昵称/店铺名称")
    private String keywords;

    @Schema(title = "付款方式")
    private String paymentType;

    /**
     * @see OrderTypeEnum
     */
    @Schema(title = "订单类型", allowableValues = "NORMAL,VIRTUAL,GIFT,PINTUAN,POINT,PURCHASE,BE_IN_PLACE_OF")
    private String orderType;

    @Schema(title = "支付方式")
    private String paymentMethod;

    @Schema(title = "支付方式")
    private String paymentClient;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "支付时间")
    private Date paymentTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "下单开始时间")
    private Date startDate;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "小于等于的开始时间")
    private Date leCreateTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "小于等于的订单发货时间")
    private Date leLogisticsTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "小于等于的完成时间")
    private Date leCompleteTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "下单结束时间")
    private Date endDate;

    @Schema(title = "订单来源")
    private String clientType;

    /**
     * @see CommentStatusEnum
     */
    @Schema(title = "评论状态:未评论(UNFINISHED),待追评(WAIT_CHASE),评论完成(FINISHED)，")
    private String commentStatus;

    @Schema(title = "是否为其他订单下的订单，如果是则为依赖订单的sn，否则为空")
    private String parentOrderSn;

    @Schema(title = "是否为某订单类型的订单，如果是则为订单类型的id，否则为空")
    private String promotionId;

    /**
     * @see OrderItemAfterSaleStatusEnum
     */
    @Schema(title = "售后状态")
    private String afterSaleStatus;

    /**
     * @see OrderComplaintStatusEnum
     */
    @Schema(title = "投诉状态")
    private String complainStatus;

    /**
     * @see PromotionTypeEnum
     */
    @Schema(title = "订单促销类型")
    private String orderPromotionType;

    @Schema(title = "总价格,可以为范围，如10_1000")
    private String flowPrice;

    @Schema(title = "是否为代理商品订单")
    private Boolean isProxy;

    @Schema(title = "是否支付成功")
    private Boolean isPay;

    @Schema(title = "是否合并支付订单")
    private Boolean isCombine;

    @Schema(title = "标记是否需要支付给供应商 为 true 表明已经支付/无需补差给供应商或平台，为 false 表明需要支付给供应商")
    private Boolean isPaidToSupplier;

    @Schema(title = "配送方式")
    private String deliveryMethod;

    @Schema(title = "是否根据场景查询")
    private Boolean sceneFlag = true;

    @Schema(title = "商品规格Id")
    private String skuId;

    @Schema(title = "收件人手机")
    private String consigneeMobile;

    @Schema(title = "是否检测全部订单")
    private Boolean checkAllFlag = false;

    @Schema(title = "物流单号")
    private String logisticsNo;

    @Schema(title = "店铺类型筛选", description = "true:直营店铺订单, false:非直营店铺订单, null:不筛选")
    private Boolean selfOperated;


    /**
     * 导出参数校验
     */
    public void checkoutExportParams() {
        if (this.startDate == null || this.endDate == null) {
            throw new ServiceException(ResultCode.PARAMS_ERROR, "导出订单必须选择时间范围");
        }
        if (this.startDate.after(this.endDate)) {
            throw new ServiceException(ResultCode.PARAMS_ERROR, "导出订单开始时间不能大于结束时间");
        }
        //最多导出180天数据
        if ((this.startDate.getTime() + 180L * 24 * 60 * 60 * 1000) < this.endDate.getTime()) {
            throw new ServiceException(ResultCode.PARAMS_ERROR, "导出信息周期最多180天");
        }
    }


    public <T> QueryWrapper<T> queryWrapper() {
        //查询条件
        QueryWrapper<T> wrapper = new QueryWrapper<>();

        //关键字查询
        if (CharSequenceUtil.isNotEmpty(keywords)) {
            wrapper.and(qw -> qw.like("o.sn", keywords).or().like("oi.goods_name", keywords).or().like("o.nickname", keywords));
        }
        //按照收件人手机查询
        wrapper.like(CharSequenceUtil.isNotEmpty(consigneeMobile), "o.consignee_mobile", consigneeMobile);
        //根据skuId查询
        wrapper.eq(CharSequenceUtil.isNotEmpty(skuId), "oi.sku_id", skuId);
        //按照买家查询
        wrapper.eq(CharSequenceUtil.isNotEmpty(buyerId), "o.buyer_id", buyerId);
        //按照场景查询
        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser != null && sceneFlag) {
            //根据场景查询
            switch (currentUser.getScene()) {
                // 会员
                case MEMBER -> {
                    wrapper.eq(CharSequenceUtil.isNotEmpty(storeId), "o.store_id", storeId);
                    if(!checkAllFlag){
                        wrapper.eq("o.buyer_id", currentUser.getId());
                    }
                    wrapper.eq("o.delete_flag", false);
                }
                // 店铺
                case STORE -> {
                    //采购订单，则查询购买人ID，否则查询店铺ID
                    if (purchase) {
                        wrapper.eq("o.buyer_id", currentUser.getExtendId());
                    } else {
                        wrapper.eq("o.store_id", currentUser.getExtendId());
                        wrapper.eq(isPaidToSupplier != null, "o.is_paid_to_supplier", isPaidToSupplier);
                    }
                }
                // 供应商
                case SUPPLIER -> {
                    // 如果是代理商品订单，则查询供应商ID，否则查询店铺ID
                    if (isProxy != null && isProxy) {
                        wrapper.eq("o.supplier_id", currentUser.getExtendId());
                        // 代理商品订单，查询是否支付给供应商 如果为空则默认为已支付
                        if (isPaidToSupplier == null) {
                            isPaidToSupplier = true;
                        }
                        wrapper.eq("o.is_paid_to_supplier", isPaidToSupplier);
                    } else {
                        wrapper.eq("o.store_id", currentUser.getExtendId());
                    }
                }
                // 管理员
                case MANAGER -> {
                    wrapper.eq(CharSequenceUtil.isNotEmpty(storeId), "o.store_id", storeId);
                    wrapper.eq(CharSequenceUtil.isNotEmpty(buyerId), "o.buyer_id", buyerId);
                }
                default -> {
                    wrapper.eq("o.delete_flag", false);
                }
            }
        }

        //按时间查询
        wrapper.ge(startDate != null, "o.create_time", startDate);

        wrapper.le(endDate != null, "o.create_time", endDate);

        wrapper.le(leCreateTime != null, "o.create_time", leCreateTime);

        wrapper.le(leLogisticsTime != null, "o.logistics_time", leLogisticsTime);

        wrapper.le(leCompleteTime != null, "o.complete_time", leCompleteTime);
        //按购买人用户名
        wrapper.eq(CharSequenceUtil.isNotEmpty(nickname), "o.nickname", nickname);

        wrapper.eq(CharSequenceUtil.isNotEmpty(logisticsNo), "o.logistics_no", logisticsNo);

        //按订单类型
        if (CharSequenceUtil.isNotEmpty(orderType)) {
            if (orderType.indexOf(",") > 0) {
                wrapper.in("o.order_type", orderType.split(","));
            } else {
                wrapper.eq("o.order_type", orderType);
            }
        }

        //物流查询
        wrapper.eq(CharSequenceUtil.isNotEmpty(shipName), "o.consignee_name", shipName);

        //按商品名称查询
        wrapper.like(CharSequenceUtil.isNotEmpty(goodsName), "oi.goods_name", goodsName);

        //付款方式
        wrapper.eq(CharSequenceUtil.isNotEmpty(paymentType), "o.payment_type", paymentType);

        //按支付方式
        wrapper.eq(CharSequenceUtil.isNotEmpty(paymentMethod), "o.payment_method", paymentMethod);

        if (CharSequenceUtil.isNotEmpty(orderStatus)) {
            if (OrderStatusEnum.AFTER_SALE.name().equals(orderStatus)) {
                //订单状态
                wrapper.eq("o.after_sale_applying", true);
            } else {
                //订单状态
                wrapper.eq("o.order_status", orderStatus);
            }
        }

        //订单状态
        wrapper.ne(CharSequenceUtil.isNotEmpty(unOrderStatus), "o.order_status",unOrderStatus);

        //付款状态
        wrapper.eq(CharSequenceUtil.isNotEmpty(payStatus), "o.pay_status", payStatus);

        //售后状态
        wrapper.eq(CharSequenceUtil.isNotEmpty(afterSaleStatus), "oi.after_sale_status", afterSaleStatus);

        //配送方式
        wrapper.eq(CharSequenceUtil.isNotEmpty(deliveryMethod), "o.delivery_method", deliveryMethod);

        //投诉状态
        wrapper.eq(CharSequenceUtil.isNotEmpty(complainStatus), "oi.complain_status", complainStatus);

        //订单来源
        wrapper.eq(CharSequenceUtil.isNotEmpty(clientType), "o.client_type", clientType);

        //按评价状态
        wrapper.eq(CharSequenceUtil.isNotEmpty(commentStatus), "oi.comment_status", commentStatus);

        //按评价状态
        wrapper.eq(isProxy != null, "o.is_proxy", isProxy);


        //按标签查询
        if (CharSequenceUtil.isNotEmpty(tag)) {
            String orderStatusColumn = "o.order_status";
            OrderTagEnum tagEnum = OrderTagEnum.valueOf(tag);
            switch (tagEnum) {
                //待付款
                case WAIT_PAY -> wrapper.eq(orderStatusColumn, OrderStatusEnum.UNPAID.name());

                //待发货
                case WAIT_SHIP -> wrapper.eq(orderStatusColumn, OrderStatusEnum.UNDELIVERED.name());

                //待收货
                case WAIT_ROG -> wrapper.eq(orderStatusColumn, OrderStatusEnum.DELIVERED.name());

                //已取消
                case CANCELLED -> wrapper.eq(orderStatusColumn, OrderStatusEnum.CANCELLED.name());

                //已完成
                case COMPLETE -> wrapper.eq(orderStatusColumn, OrderStatusEnum.COMPLETED.name());
                default -> {
                }
            }
        }

        // 依赖订单（同时满足此条件时，查询未拼团订单，需要单独的一个查询wrapper）
        if (parentOrderSn != null && orderSn != null && PromotionTypeEnum.PINTUAN.name().equals(orderPromotionType)) {
            wrapper.and(i -> i.eq("o.parent_order_sn", parentOrderSn).or().eq("o.sn", orderSn));
        } else {
            wrapper.eq(parentOrderSn != null, "o.parent_order_sn", parentOrderSn);
            //按订单编号查询
            wrapper.eq(CharSequenceUtil.isNotEmpty(orderSn), "o.sn", orderSn);
        }

        // 促销活动id
        wrapper.eq(CharSequenceUtil.isNotEmpty(promotionId), "o.promotion_id", promotionId);
        if (CharSequenceUtil.isNotEmpty(orderPromotionType)) {
            if (OrderTypeEnum.NORMAL.name().equals(orderPromotionType)) {
                wrapper.eq("o.order_promotion_type", "");
            } else {
                wrapper.eq("o.order_promotion_type", orderPromotionType);
            }
        }

        if (CharSequenceUtil.isNotEmpty(flowPrice)) {
            String[] s = flowPrice.split("_");
            if (s.length > 1) {
                wrapper.between("o.flow_price", s[0], s[1]);
            } else {
                wrapper.ge("o.flow_price", s[0]);
            }
        }

        return wrapper;
    }

}
