package cn.lili.modules.promotion.serviceimpl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.cache.Cache;
import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.event.TransactionCommitSendMQEvent;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.ResultUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.exchange.AmqpExchangeProperties;
import cn.lili.modules.promotion.client.PromotionGoodsClient;
import cn.lili.modules.promotion.entity.dos.BaseStandardPromotions;
import cn.lili.modules.promotion.entity.dos.PromotionGoods;
import cn.lili.modules.promotion.entity.dto.search.BasePromotionsSearchParams;
import cn.lili.modules.promotion.entity.enums.PromotionsScopeTypeEnum;
import cn.lili.modules.promotion.entity.enums.PromotionsStatusEnum;
import cn.lili.modules.promotion.service.AbstractPromotionsService;
import cn.lili.modules.promotion.service.PromotionGoodsService;
import cn.lili.modules.promotion.tools.PromotionTools;
import cn.lili.mybatis.util.PageUtil;
import cn.lili.routing.GoodsRoutingKey;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.seata.spring.annotation.GlobalTransactional;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static cn.lili.modules.promotion.tools.PromotionTools.queryPromotionStatus;

/**
 * <AUTHOR>
 * @since 2021/11/30
 **/
public abstract class AbstractPromotionsServiceImpl<M extends BaseMapper<T>, T extends BaseStandardPromotions> extends ServiceImpl<M, T> implements AbstractPromotionsService<T> {

    /**
     * 促销商品
     */
    @Autowired
    private PromotionGoodsService promotionGoodsService;

    @Autowired
    private Cache cache;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private AmqpExchangeProperties amqpExchangeProperties;

    /**
     * 通用促销保存
     * 调用顺序:
     * 1. initPromotion 初始化促销信息
     * 2. checkPromotions 检查促销参数
     * 3. save 保存促销信息
     * 4. updatePromotionGoods 更新促销商品信息
     * 5。 updateEsGoodsIndex 更新商品索引促销信息
     *
     * @param promotions 促销信息
     * @return 是否保存成功
     */
    @Override
    @Transactional
    @GlobalTransactional
    public boolean savePromotions(T promotions) {
        this.initPromotion(promotions);
        this.checkPromotions(promotions);
        boolean save = this.save(promotions);
        if (this.updatePromotionsGoods(promotions)) {
            this.updateEsGoodsIndex(promotions);
        }
        return save;
    }

    /**
     * 通用促销更新
     * 调用顺序:
     * 1. checkStatus 检查促销状态
     * 2. checkPromotions 检查促销参数
     * 3. saveOrUpdate 保存促销信息
     * 4. updatePromotionGoods 更新促销商品信息
     * 5. updateEsGoodsIndex 更新商品索引促销信息
     *
     * @param promotions 促销信息
     * @return 是否更新成功
     */
    @Override
    @Transactional
    @GlobalTransactional
    public boolean updatePromotions(T promotions) {
        this.checkStatus(promotions);
        this.checkPromotions(promotions);
        boolean save = this.saveOrUpdate(promotions);
        if (this.updatePromotionsGoods(promotions)) {
            this.updateEsGoodsIndex(promotions);
        }
        return save;
    }

    /**
     * 更新促销状态
     * 如果要更新促销状态为关闭，startTime和endTime置为空即可
     *
     * @param ids       促销id集合
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 是否更新成功
     */
    @Override
    @Transactional
    @GlobalTransactional
    public boolean updateStatus(List<String> ids, Long startTime, Long endTime) {
        List<T> promotionsList = this.list(new QueryWrapper<T>().in("id", ids));
        for (T t : promotionsList) {
            if (startTime != null && endTime != null) {
                t.setStartTime(new Date(startTime));
                t.setEndTime(new Date(endTime));
                this.checkPromotions(t);
            } else {
                t.setStartTime(null);
                t.setEndTime(null);
            }
            this.checkStatus(t);
            this.updatePromotionsGoods(t);
            this.updateEsGoodsIndex(t);
        }
        if (startTime != null && endTime != null) {
            return this.update(new UpdateWrapper<T>()
                    .in("id", ids)
                    .set("start_time", new Date(startTime))
                    .set("end_time", new Date(endTime))
                    .set("last_start_time", new Date(startTime))
                    .set("last_end_time", new Date(endTime))
            );
        } else {
            return this.update(new UpdateWrapper<T>().in("id", ids).set("start_time", null).set("end_time", null));
        }
    }

    /**
     * 移除促销活动
     *
     * @param ids 促销活动id集合
     * @return 是否移除成功
     */
    @Override
    @Transactional
    @GlobalTransactional
    public boolean removePromotions(List<String> ids) {
        for (String id : ids) {
            T promotions = this.getById(id);
            this.checkStatus(promotions);
            promotions.setStartTime(null);
            promotions.setEndTime(null);
            this.updateEsGoodsIndex(promotions);
        }
        this.promotionGoodsService.deletePromotionGoods(ids);
        return super.removeByIds(ids);
    }

    /**
     * 分页查询促销信息
     *
     * @param searchParams 查询参数，继承自继承促销查询参数
     * @param page         分页参数
     * @return 分页促销信息
     */
    @Override
    public <S extends BasePromotionsSearchParams> Page<T> pageFindAll(S searchParams, PageVO page) {

        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        if (SceneEnums.SUPPLIER.equals(currentUser.getScene()) || SceneEnums.STORE.equals(currentUser.getScene())) {
            searchParams.setStoreId(currentUser.getExtendId());
        }
        return this.page(PageUtil.initPage(page), searchParams.queryWrapper());

    }

    /**
     * 列表查询促销信息
     *
     * @param searchParams 查询参数，继承自继承促销查询参数
     * @return 列表促销信息
     */
    @Override
    public <S extends BasePromotionsSearchParams> List<T> listFindAll(S searchParams) {
        return this.list(searchParams.queryWrapper());
    }

    @Override
    public <S extends BasePromotionsSearchParams> T detail(S searchParams) {
        return this.getOne(searchParams.queryWrapper(), false);
    }

    @Override
    public PromotionTypeEnum getPromotionType() {
        return null;
    }

    @Override
    public boolean allowExistSame() {
        return false;
    }

    /**
     * 初始化促销字段
     *
     * @param promotions 促销实体
     */
    @Override
    public void initPromotion(T promotions) {
        if (CharSequenceUtil.isEmpty(promotions.getScopeType())) {
            promotions.setScopeType(PromotionsScopeTypeEnum.PORTION_GOODS.name());
        }
    }

    /**
     * 检查促销参数
     *
     * @param promotions 促销实体
     */
    @Override
    public void checkPromotions(T promotions) {
        PromotionTools.checkPromotionTime(promotions.getOriginStartTime(), promotions.getOriginEndTime());
        if (!this.allowExistSame()) {
            this.checkSamePromotions(promotions);
            this.checkPromotionsName(promotions);
        }
    }

    /**
     * 检查促销状态
     *
     * @param promotions 促销实体
     */
    @Override
    public void checkStatus(T promotions) {
        T byId = this.getById(promotions.getId());
        if (byId == null) {
            throw new ServiceException(ResultCode.PROMOTION_ACTIVITY_ERROR);
        }
        if (byId.getOriginStartTime() != null) {
            UpdateWrapper<T> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", promotions.getId());
            updateWrapper.set("last_start_time", byId.getOriginStartTime());
            this.update(updateWrapper);
        }
        if (byId.getOriginEndTime() != null) {
            UpdateWrapper<T> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", promotions.getId());
            updateWrapper.set("last_end_time", byId.getOriginEndTime());
            this.update(updateWrapper);
        }
    }

    /**
     * 更新促销商品信息
     *
     * @param promotions 促销实体
     * @return 是否更新成功
     */
    @Override
    public boolean updatePromotionsGoods(T promotions) {
        if (promotions.getOriginStartTime() == null && promotions.getOriginEndTime() == null) {
            String promotionGoodsStockCacheKey = PromotionGoodsClient.getPromotionStockCacheKey(this.getPromotionType(), promotions.getId());
            cache.vagueDel(promotionGoodsStockCacheKey);
            // 活动关闭，不删除活动商品，保留此记录
            this.promotionGoodsService.closePromotionGoods(Collections.singletonList(promotions.getId()));
            return true;
        } else {
            // 活动开启，删除无效活动商品
            this.promotionGoodsService.deletePromotionGoods(Collections.singletonList(promotions.getId()), promotions.getStoreId());
        }
        boolean result = true;
        if (CharSequenceUtil.equalsAny(promotions.getScopeType(), PromotionsScopeTypeEnum.ALL.name(),
                PromotionsScopeTypeEnum.PORTION_GOODS_CATEGORY.name())) {
            PromotionGoods promotionGoods = getPromotionGoodsByPromotionalInfo(promotions);
            this.promotionGoodsService.deletePromotionGoods(Collections.singletonList(promotions.getId()));
            result = this.promotionGoodsService.save(promotionGoods);
        }
        return result;
    }

    /**
     * 根据促销活动信息封装促销商品信息（适用于非指定商品）
     *
     * @param promotions 促销活动信息
     * @return 促销商品信息
     */
    public PromotionGoods getPromotionGoodsByPromotionalInfo(T promotions) {
        PromotionGoods promotionGoods = new PromotionGoods();
        promotionGoods.setScopeId(promotions.getScopeId());
        promotionGoods.setScopeType(promotions.getScopeType());
        promotionGoods.setPromotionId(promotions.getId());
        promotionGoods.setStoreId(promotions.getStoreId());
        promotionGoods.setStoreName(promotions.getStoreName());
        promotionGoods.setStartTime(promotions.getOriginStartTime());
        promotionGoods.setEndTime(promotions.getOriginEndTime());
        promotionGoods.setPromotionType(this.getPromotionType().name());
        promotionGoods.setTitle(promotions.getPromotionName());
        return promotionGoods;
    }

    /**
     * 更新促销信息到商品索引
     *
     * @param promotions 促销实体
     */
    @Override
    public void updateEsGoodsIndex(T promotions) {
        if (promotions.getOriginStartTime() == null && promotions.getOriginEndTime() == null) {
            Map<Object, Object> build = MapBuilder.create()
                    .put("promotionKey", this.getPromotionType() + "-" + promotions.getId())
                    .put("scopeType", promotions.getScopeType())
                    .put("scopeId", promotions.getScopeId()).build();
            //删除商品促销消息
            applicationEventPublisher.publishEvent(TransactionCommitSendMQEvent.builder().source("删除商品促销事件").exchange(amqpExchangeProperties.getGoods()).routingKey(GoodsRoutingKey.DELETE_GOODS_INDEX_PROMOTIONS).message(build).build());

        } else {
            this.sendUpdateEsGoodsMsg(promotions);
        }
    }

    @Override
    public void sendUpdateEsGoodsMsg(T promotions) {
        String esPromotionKey = this.getPromotionType().name() + "-" + promotions.getId();
        Map<String, Object> map = new HashMap<>();
        // es促销key
        map.put("esPromotionKey", esPromotionKey);
        // 促销类型全路径名
        map.put("promotionsType", promotions.getClass().getName());
        // 促销实体
        map.put("promotions", promotions);
        applicationEventPublisher.publishEvent(TransactionCommitSendMQEvent.builder().source("更新商品索引促销事件").exchange(amqpExchangeProperties.getGoods()).routingKey(GoodsRoutingKey.UPDATE_GOODS_INDEX_PROMOTIONS).message(map).build());
    }

    public void checkSamePromotions(T promotions) {
        if (promotions.getOriginStartTime() == null || promotions.getOriginEndTime() == null) {
            return;
        }

        // 遍历编辑活动开始时间的当天开始时间到结束的当天结束时间内的活动
        QueryWrapper<T> querySameWrapper = getSameWrapper(promotions);

        this.list(querySameWrapper).forEach(promotion -> {
            if (promotion.getOriginStartTime() == null || promotion.getOriginEndTime() == null) {
                return;
            }

            if (isOverlapping(promotions.getOriginStartTime(), promotions.getOriginEndTime(), promotion.getOriginStartTime(), promotion.getOriginEndTime())) {
                throw new ServiceException(ResultCode.PROMOTION_SAME_ACTIVE_EXIST);
            }

        });

    }

    public void checkPromotionsName(T promotions){
        if(promotions.getPromotionName().length() < 2 || promotions.getPromotionName().length() > 30){
            throw new ServiceException(ResultCode.PROMOTION_NAME_EXIST);
        }
    }

    /**
     * 获取相同时间段的促销活动
     *
     * @param promotions 促销活动
     * @return 相同时间段的促销活动
     * @param <T> 促销活动类型
     */
    private static <T extends BaseStandardPromotions> QueryWrapper<T> getSameWrapper(T promotions) {
        QueryWrapper<T> querySameWrapper = new QueryWrapper<>();
        querySameWrapper.eq("store_id", promotions.getStoreId());
        querySameWrapper.eq("delete_flag", false);
        querySameWrapper.ne(CharSequenceUtil.isNotEmpty(promotions.getId()),"id", promotions.getId());
        querySameWrapper.and(i -> i.or(queryPromotionStatus(PromotionsStatusEnum.NEW)).or(queryPromotionStatus(PromotionsStatusEnum.START)));
        querySameWrapper.and(i -> i.or(j -> j.between("start_time", DateUtil.beginOfDay(promotions.getOriginStartTime()), DateUtil.endOfDay(promotions.getOriginEndTime()))
                .or().between("end_time", DateUtil.beginOfDay(promotions.getOriginStartTime()), DateUtil.endOfDay(promotions.getOriginEndTime()))));
        return querySameWrapper;
    }

    /**
     * 判断两个时间段是否有交集
     *
     * @param a1 原时间段开始时间
     * @param a2 原时间段结束时间
     * @param b1 新时间段开始时间
     * @param b2 新时间段结束时间
     * @return 是否有交集
     */
    public static boolean isOverlapping(Date a1, Date a2, Date b1, Date b2) {
         // 两个时间段有交集的条件是：b1在a1和a2之间，或者b2在a1和a2之间，或者a1在b1和b2之间
        return (b1.before(a2) && b2.after(a1)) || (b1.before(a1) && b2.after(a2)) || (a1.before(b1) && a2.after(b2));
    }

}
