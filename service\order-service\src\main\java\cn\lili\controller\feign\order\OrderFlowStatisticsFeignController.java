package cn.lili.controller.feign.order;

import cn.lili.modules.order.order.client.OrderFlowStatisticsClient;
import cn.lili.modules.order.order.service.OrderFlowStatisticsService;
import cn.lili.modules.statistics.entity.dto.GoodsStatisticsQueryParam;
import cn.lili.modules.statistics.entity.vo.CategoryStatisticsDataVO;
import cn.lili.modules.statistics.entity.vo.GoodsStatisticsDataVO;
import cn.lili.modules.statistics.entity.vo.OrderOverviewVO;
import cn.lili.modules.statistics.entity.vo.StoreStatisticsDataVO;
import cn.lili.modules.store.entity.dto.StoreStatisticsOverviewSearchParams;
import cn.lili.modules.store.entity.dto.StoreStatisticsSearchParams;
import cn.lili.mybatis.util.PageUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/6/24
 **/
@RestController
@RequiredArgsConstructor
public class OrderFlowStatisticsFeignController implements OrderFlowStatisticsClient {

    private final OrderFlowStatisticsService orderFlowStatisticsService;

    @Override
    public List<GoodsStatisticsDataVO> getGoodsStatisticsData(GoodsStatisticsQueryParam goodsStatisticsQueryParam, Integer num) {
        return orderFlowStatisticsService.getGoodsStatisticsData(goodsStatisticsQueryParam, num);
    }

    @Override
    public long getGoodsSalesVolume(GoodsStatisticsQueryParam queryDTO) {
        return orderFlowStatisticsService.getGoodsSalesVolume(queryDTO);
    }

    @Override
    public List<CategoryStatisticsDataVO> getCategoryStatisticsData(GoodsStatisticsQueryParam goodsStatisticsQueryParam) {
        return orderFlowStatisticsService.getCategoryStatisticsData(goodsStatisticsQueryParam);
    }

    @Override
    public List<StoreStatisticsDataVO> getStoreStatisticsData(StoreStatisticsSearchParams searchParams) {
        return orderFlowStatisticsService.getStoreStatisticsData(PageUtil.initPage(searchParams), searchParams.storeStatisticsDataWrapper());
    }

    @Override
    public Map<String, Object> getOrderStatisticsPrice(String storeId) {
        return orderFlowStatisticsService.getOrderStatisticsPrice(storeId);
    }

    @Override
    public OrderOverviewVO overview(StoreStatisticsOverviewSearchParams searchParams) {
        OrderOverviewVO orderOverviewVO = new OrderOverviewVO();
        orderFlowStatisticsService.overview(searchParams.getDates(), orderOverviewVO, searchParams.getStatisticsQueryParam());
        return orderOverviewVO;
    }

    @Override
    public GoodsStatisticsDataVO getSaleNumByStoreId(String storeId) {
        return orderFlowStatisticsService.getSaleNumByStoreId(storeId);
    }
}
