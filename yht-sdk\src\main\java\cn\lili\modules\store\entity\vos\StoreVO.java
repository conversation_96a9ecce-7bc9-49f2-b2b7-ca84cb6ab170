package cn.lili.modules.store.entity.vos;

import cn.lili.common.utils.BeanUtil;
import cn.lili.modules.promotion.entity.vos.CouponVO;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.enums.StoreStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.util.List;

/**
 * 店铺详细VO
 *
 * <AUTHOR>
 * @since 2020-03-09 21:53:20
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StoreVO extends Store {

    @Serial
    private static final long serialVersionUID = -7783778318124995269L;

    @NotBlank(message = "店铺经营类目不能为空")
    @Schema(title = "店铺经营类目")
    private String[] businessCategorys;

    @Schema(title = "是否为店铺管理员")
    private Boolean isManager;

    /**
     * 扩展如果需要返回店铺昵称非店铺名称则写入此字段
     */
    private String nickName;
    /**
     * 扩展是会员店铺在某些情况会返回这个字段
     */
    private String username;

    private String mobile;

    @Schema(title = "直营状态Name")
    private String selfOperatedName;

    @Schema(title = "是否允许购买商品Name")
    private String isBuyGoodsName;

    @Schema(title = "是否支持退现Name")
    private String isBackMoneyName;

    @Schema(title = "是否推荐Name")
    private String isRecommendName;


    public StoreVO(Store store) {
        BeanUtil.copyProperties(store, this);
        if (store.getBusinessCategory() != null) {
            this.businessCategorys = store.getBusinessCategory().split(",");
        }
    }

    /**
     * 检查店铺是否有效
     *
     * @return 是否有效 true有效 false无效
     */

    public boolean checkValid() {
        if (Boolean.TRUE.equals(this.getDeleteFlag())) {
            return false;

        }
        return StoreStatusEnum.OPEN.name().equals(this.getStoreStatus());
    }
}
