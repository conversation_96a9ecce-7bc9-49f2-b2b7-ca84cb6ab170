package cn.lili.modules.store.mapper;

import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import cn.lili.modules.store.entity.vos.StoreVO;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 店铺数据处理层
 *
 * <AUTHOR>
 * @since2020-03-07 09:18:56
 */
public interface StoreMapper extends BaseMapper<Store> {

    /**
     * 获取店铺分页列表
     *
     * @param page         分页
     * @param queryWrapper 查询条件
     * @return 店铺VO分页列表
     */
    @Select("select * from li_store ${ew.customSqlSegment}")
    Page<StoreVO> getStoreList(Page<StoreVO> page, @Param(Constants.WRAPPER) Wrapper<StoreVO> queryWrapper);


    /**
     * 修改店铺收藏数据
     *
     * @param storeId 店铺id
     * @param num     收藏数量
     */
    @Update("update li_store set collection_num = collection_num + #{num} where id = #{storeId}")
    void updateCollection(String storeId, Integer num);

    /**
     * 获取店铺数据
     *
     * @param storeId 店铺id
     */
    @Select("select s.*,u.mobile as mobile,u.username as username from li_store s left join li_user u on s.manager_id = u.id where s.id = #{storeId}")
    StoreVO getStoreVO(String storeId);

    /**
     * 获取店铺收藏数据
     *
     */
    @Select("SELECT " +
            "ls.id as storeId, " +
            "ls.collection_num as collectionNum " +
            "FROM li_store ls " +
            "${ew.customSqlSegment}")
    Page<StoreRankStatisticsVO> getCollectionNumByStore(Page<Store> page, @Param(Constants.WRAPPER) Wrapper queryWrapper);

    @Select("SELECT DENSE_RANK() OVER (ORDER BY sale_num DESC) AS sales_rank FROM li_store WHERE id = #{storeId}")
    Long getStoreRankBySaleNum (String storeId);

}