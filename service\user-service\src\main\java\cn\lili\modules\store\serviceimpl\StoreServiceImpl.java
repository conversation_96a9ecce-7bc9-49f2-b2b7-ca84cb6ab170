package cn.lili.modules.store.serviceimpl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.cache.Cache;
import cn.lili.cache.CachePrefix;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.properties.DomainProperties;
import cn.lili.common.properties.SystemSettingProperties;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.BeanUtil;
import cn.lili.common.utils.SnowFlake;
import cn.lili.common.vo.PageVO;
import cn.lili.exchange.AmqpExchangeProperties;
import cn.lili.modules.goods.client.CategoryClient;
import cn.lili.modules.goods.client.GoodsClient;
import cn.lili.modules.goods.entity.dos.Category;
import cn.lili.modules.goods.entity.dto.BatchUpdateGoodsDTO;
import cn.lili.modules.member.entity.dos.User;
import cn.lili.modules.member.entity.dos.UserAddress;
import cn.lili.modules.member.entity.dto.AddressSearchParams;
import cn.lili.modules.member.entity.dto.CollectionDTO;
import cn.lili.modules.member.entity.dto.UserInfoDTO;
import cn.lili.modules.member.entity.dto.UserSearchParams;
import cn.lili.modules.member.entity.enums.AddressTypeEnum;
import cn.lili.modules.member.service.UserAddressService;
import cn.lili.modules.member.service.UserService;
import cn.lili.modules.order.order.client.OrderClient;
import cn.lili.modules.search.utils.EsIndexUtil;
import cn.lili.modules.statistics.client.PlatformViewClient;
import cn.lili.modules.statistics.entity.dos.PlatformViewData;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.dos.StoreVerify;
import cn.lili.modules.store.entity.dto.*;
import cn.lili.modules.store.entity.enums.StoreSceneEnum;
import cn.lili.modules.store.entity.enums.StoreStatusEnum;
import cn.lili.modules.store.entity.params.StoreRankStatisticsParams;
import cn.lili.modules.store.entity.vos.*;
import cn.lili.modules.store.mapper.StoreMapper;
import cn.lili.modules.store.service.StoreService;
import cn.lili.modules.store.service.StoreVerifyService;
import cn.lili.modules.system.client.StoreMarketClient;
import cn.lili.modules.system.entity.dos.StoreMarket;
import cn.lili.modules.system.entity.dto.ExperienceSetting;
import cn.lili.modules.system.entity.dto.PointSetting;
import cn.lili.modules.system.entity.enums.SettingEnum;
import cn.lili.mybatis.util.PageUtil;
import cn.lili.routing.GoodsRoutingKey;
import cn.lili.routing.UserRoutingKey;
import cn.lili.util.AmqpMessage;
import cn.lili.util.AmqpSender;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * 店铺业务层实现
 *
 * <AUTHOR>
 * @since 2020-03-07 16:18:56
 */
@Slf4j
@Service
//@RequiredArgsConstructor
public class StoreServiceImpl extends ServiceImpl<StoreMapper, Store> implements StoreService {

    /**
     * 会员
     */
    @Lazy
    @Autowired
    private UserService userService;

    //语法 注入警告处理。不要修改这里，否则会循环依赖
    @Lazy
    @Autowired
    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    @Lazy
    @Autowired
    private CategoryClient categoryClient;

    @Lazy
    @Autowired
    private GoodsClient goodsClient;

    @Lazy
    @Autowired
    private OrderClient orderClient;

    @Lazy
    @Autowired
    private StoreVerifyService storeVerifyService;

    @Lazy
    @Autowired
    private Cache cache;

    @Lazy
    @Autowired
    private AmqpSender amqpSender;

    @Lazy
    @Autowired
    private AmqpExchangeProperties amqpExchangeProperties;

    @Lazy
    @Autowired
    private SystemSettingProperties systemSettingProperties;

    @Lazy
    @Autowired
    private DomainProperties domainProperties;

    @Lazy
    @Autowired
    private UserAddressService userAddressService;

    @Lazy
    @Autowired
    private PlatformViewClient platformViewClient;

    @Lazy
    @Autowired
    private StoreMarketClient storeMarketClient;

    @Override
    @Transactional
    public Store add(StoreApplyDTO storeApplyDTO) {

        storeApplyDTO.validateParams();

        // 只有直营店铺才能操作是否购买商品
        if (!storeApplyDTO.getSelfOperated() && storeApplyDTO.getIsBuyGoods()) {
            throw new ServiceException(ResultCode.SELF_OPERATED_STORE_ERROR);
        }

        //判断店铺名称是否存在
        LambdaQueryWrapper<Store> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Store::getStoreName, storeApplyDTO.getStoreName());
        queryWrapper.eq(Store::getScene, storeApplyDTO.getScene());
        List<Store> list = this.list(queryWrapper);
        if (ObjectUtil.isNotNull(list) && !list.isEmpty()) {
            throw new ServiceException(ResultCode.STORE_NAME_EXIST_ERROR);
        }

        LambdaQueryWrapper<User> mobileQueryWrapper = Wrappers.lambdaQuery();
        mobileQueryWrapper.eq(User::getMobile, storeApplyDTO.getMobile());
        mobileQueryWrapper.eq(User::getScene, storeApplyDTO.getScene());
        if (userService.count(mobileQueryWrapper) > 0) {
            throw new ServiceException(ResultCode.STORE_MOBILE_EXIST_ERROR);
        }

        //添加店铺
        Store store = new Store(storeApplyDTO);
        store.setId(SnowFlake.getIdStr());
        store.setExtendId(store.getId());
        //获取userName为创建店铺使用的手机号，否则无法登陆。
        User user = userService.registerHandler(UserInfoDTO.builder()
                .username(storeApplyDTO.getMobile())
                .password(new BCryptPasswordEncoder().encode(storeApplyDTO.getPassword()))
                .mobile(storeApplyDTO.getMobile())
                .extendName(storeApplyDTO.getStoreName())
                .nickName("用户" + storeApplyDTO.getMobile())
                .scene(SceneEnums.valueOf(storeApplyDTO.getScene()))
                .extendId(store.getId())
                .isSuper(true)
                .shopkeeper(true)
                .build());

        store.setManagerId(user.getId());
        this.save(store);
        return store;
    }

    @Override
    public Page<StoreVO> findByConditionPage(StoreSearchParams storeSearchParams, PageVO page) {
        Page<StoreVO> voPage = this.baseMapper.getStoreList(PageUtil.initPage(page), storeSearchParams.queryWrapper());
        if (CollectionUtils.isNotEmpty(voPage.getRecords())) {
            voPage.getRecords().forEach(storeVO -> {
                storeVO.setSelfOperatedName(storeVO.getSelfOperated() ? "OPEN" : "CLOSE");
                storeVO.setIsBuyGoodsName(storeVO.getIsBuyGoods() ? "OPEN" : "CLOSE");
                storeVO.setIsBackMoneyName(storeVO.getIsBackMoney() ? "OPEN" : "CLOSE");
                storeVO.setIsRecommendName(storeVO.getIsRecommend() ? "OPEN" : "CLOSE");
            });
        }
        return voPage;
    }


    /**
     * 列表查询
     *
     * @param searchParams 查询参数
     * @return 店铺列表
     */
    @Override
    public List<Store> list(StoreSearchParams searchParams) {
        return this.list(searchParams.queryWrapper());
    }


    @Override
    @Transactional
    public Store edit(StoreEditDTO storeEditDTO) {
        if (storeEditDTO != null) {
            // 只有直营店铺才能操作是否购买商品
            if (null != storeEditDTO.getSelfOperated() && null != storeEditDTO.getIsBuyGoods()
                    && !storeEditDTO.getSelfOperated() && storeEditDTO.getIsBuyGoods()) {
                throw new ServiceException(ResultCode.SELF_OPERATED_STORE_ERROR);
            }
            //判断店铺名是否唯一
            QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("store_name", storeEditDTO.getStoreName());
            Store store = this.getById(storeEditDTO.getStoreId());
            queryWrapper.eq("scene", store.getScene());
            List<Store> list = this.list(queryWrapper);
            //如果店铺名存在，判断是否是当前店铺
            if (ObjectUtil.isNotNull(list) && !list.isEmpty()) {
                list.forEach(item -> {
                    if (!item.getId().equals(storeEditDTO.getStoreId())) {
                        throw new ServiceException(ResultCode.STORE_NAME_EXIST_ERROR);
                    }
                });
            }

            if (store != null) {
                BeanUtil.copyProperties(storeEditDTO, store);
                store.setId(storeEditDTO.getStoreId());
                store.setBusinessCategoryArray(storeEditDTO.getBusinessCategory());
                boolean result = this.updateById(store);
                if (result) {
                    this.updateStoreGoodsInfo(store);
                }
            }

            orderClient.updateByStoreInfo(store);
            cache.remove(CachePrefix.STORE.getPrefix() + storeEditDTO.getStoreId());
            return store;
        } else {
            throw new ServiceException(ResultCode.STORE_NOT_EXIST);
        }
    }

    @Override
    public Store customAdd(Store store) {
        this.save(store);
        return store;
    }

    @Override
    public Store customEdit(Store store) {
        this.updateById(store);
        return this.getById(store.getId());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStoreStatus(List<String> ids, StoreStatusEnum status, SceneEnums scene) {

        LambdaUpdateWrapper<Store> storeLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        storeLambdaUpdateWrapper.in(Store::getId, ids);
        storeLambdaUpdateWrapper.set(Store::getStoreStatus, status.value());
        if (status.equals(StoreStatusEnum.CLOSED)) {
            storeLambdaUpdateWrapper.set(Store::getStoreEndTime, new Date());
        }
        boolean update = this.update(storeLambdaUpdateWrapper);

        for (String id : ids) {
            if (status.equals(StoreStatusEnum.CLOSED)) {

                BatchUpdateGoodsDTO batchUpdateGoodsDTO = new BatchUpdateGoodsDTO();
                batchUpdateGoodsDTO.setStoreId(id);
                // 设置批量操作类型 2：下架
                batchUpdateGoodsDTO.setOperationType(2);
                batchUpdateGoodsDTO.setUnderReason("店铺关闭");
                // 下架所有此店铺商品
                this.goodsClient.batchUpdateGoods(batchUpdateGoodsDTO);
                // 清除店铺登录token
                List<Map<String, Object>> userIds = this.userService.listFieldsByCondition("id",
                        UserSearchParams.builder().scene(scene).extendId(id).build());
                if (!userIds.isEmpty()) {
                    for (Map<String, Object> map : userIds) {
                        cache.vagueDel(CachePrefix.ACCESS_TOKEN.getPrefix(scene, map.get("id").toString()));
                        cache.vagueDel(CachePrefix.REFRESH_TOKEN.getPrefix(scene, map.get("id").toString()));
                    }
                }

            }
            cache.remove(CachePrefix.STORE.getPrefix() + id);
        }

        return update;
    }

    @Override
    public void updateStoreGoodsNum(String storeId, Long num) {
        //修改店铺商品数量
        this.update(new LambdaUpdateWrapper<Store>()
                .set(Store::getGoodsNum, num)
                .eq(Store::getId, storeId));
        cache.remove(CachePrefix.STORE.getPrefix() + storeId);
    }

    @Override
    public void updateStoreCollectionNum(CollectionDTO collectionDTO) {
        baseMapper.updateCollection(collectionDTO.getId(), collectionDTO.getNum());
        cache.remove(CachePrefix.STORE.getPrefix() + collectionDTO.getId());
    }

    /**
     * 更新评分
     *
     * @param storeId          店铺ID
     * @param deliveryScore    物流评分
     * @param serviceScore     服务评分
     * @param descriptionScore 描述评分
     */
    @Override
    @Transactional
    public void updateScore(String storeId, String deliveryScore, String serviceScore, String descriptionScore) {
        LambdaUpdateWrapper<Store> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(Store::getId, storeId);
        lambdaUpdateWrapper.set(Store::getDescriptionScore, descriptionScore);
        lambdaUpdateWrapper.set(Store::getDeliveryScore, deliveryScore);
        lambdaUpdateWrapper.set(Store::getServiceScore, serviceScore);
        this.update(lambdaUpdateWrapper);
    }

    @Override
    public StoreVO getStoreDetailVO(String storeId) {
        StoreVO storeVO = (StoreVO) cache.get(CachePrefix.STORE.getPrefix() + storeId);
        if (storeVO == null) {
            storeVO = this.baseMapper.getStoreVO(storeId);
            if (storeVO != null) {
                cache.put(CachePrefix.STORE.getPrefix() + storeId, storeVO, 7200L);
            }
        }
        checkStoreDomain(storeVO);
        return storeVO;
    }

    @Override
    public StoreVO getOwnerStoreDetail() {
        AuthUser currentUser = UserContext.getCurrentExistUser();
        StoreVO storeVO = (StoreVO) cache.get(CachePrefix.STORE.getPrefix() + currentUser.getExtendId());
        if (storeVO == null) {
            storeVO = this.baseMapper.getStoreVO(currentUser.getExtendId());
            if (storeVO != null) {
                cache.put(CachePrefix.STORE.getPrefix() + currentUser.getExtendId(), storeVO, 7200L);
            } else {
                throw new ServiceException(ResultCode.STORE_NOT_EXIST);
            }
        }
        checkStoreDomain(storeVO);
        storeVO.setIsManager(currentUser.getId().equals(storeVO.getManagerId()));
        storeVO.setNickName(currentUser.getNickName());
        return storeVO;
    }

    @Override
    public Store getStoreDetailBySceneAndUserId(String extendId, SceneEnums scene) {
        LambdaQueryWrapper<Store> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Store::getExtendId, extendId);
        queryWrapper.eq(Store::getScene, scene.value());
        return this.getOne(queryWrapper, false);
    }

    @Override
    public Store getStoreByManagerId(String managerId, SceneEnums scene) {
        LambdaQueryWrapper<Store> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Store::getId, managerId);
        if (scene != null) {
            queryWrapper.eq(scene != null, Store::getScene, scene.value());
        }
        return this.getOne(queryWrapper, false);
    }

    @Override
    public Boolean editStoreSetting(StoreSettingDTO storeSettingDTO) {
        AuthUser tokenUser = Objects.requireNonNull(UserContext.getCurrentUser());
        //修改店铺
        Store store = this.getById(tokenUser.getExtendId());
        BeanUtil.copyProperties(storeSettingDTO, store);
        this.updateById(store);
        this.updateStoreGoodsInfo(store);
        return true;
    }

    @Override
    public void updateStoreGoodsInfo(Store store) {
        goodsClient.updateStoreDetail(store);
        Map<String, Object> updateIndexFieldsMap = EsIndexUtil.getUpdateIndexFieldsMap(
                MapUtil.builder(new HashMap<String, Object>()).put("storeId", store.getId()).build(),
                MapUtil.builder(new HashMap<String, Object>()).put("storeName", store.getStoreName())
                        .put("selfOperated", store.getSelfOperated()).build());
        amqpSender.send(AmqpMessage.builder()
                .exchange(amqpExchangeProperties.getGoods())
                .routingKey(GoodsRoutingKey.UPDATE_GOODS_INDEX_FIELD)
                .message(updateIndexFieldsMap)
                .build());
    }


    @Override
    public StoreBasicInfoVO getStoreBasicInfoDTO(String storeId) {
        StoreVO storeVO = this.getStoreDetailVO(storeId);
        StoreBasicInfoVO storeBasicInfoVO = new StoreBasicInfoVO();
        BeanUtils.copyProperties(storeVO, storeBasicInfoVO);
        return storeBasicInfoVO;
    }

    @Override
    public List<StoreManagementCategoryVO> goodsManagementCategory(String storeId) {

        //获取顶部分类列表
        List<Category> categoryList = categoryClient.firstCategory();

        //获取店铺分类
        String[] storeCategoryList = this.getBusinessCategory(storeId).split(",");
        List<StoreManagementCategoryVO> list = new ArrayList<>();
        for (Category category : categoryList) {
            StoreManagementCategoryVO storeManagementCategoryVO = new StoreManagementCategoryVO(category);
            for (String storeCategory : storeCategoryList) {
                if (storeCategory.equals(category.getId())) {
                    storeManagementCategoryVO.setSelected(true);
                }
            }
            list.add(storeManagementCategoryVO);
        }
        return list;
    }

    @Override
    public StoreLicenceVO getStoreLicenceVO(String storeId) {
        StoreLicenceVO storeLicenceVO = new StoreLicenceVO();
        StoreVerify store = storeVerifyService.getByStore(storeId);
        if (store != null) {
            storeLicenceVO.setLicencePhoto(store.getBusinessLicenseInfo());
        }
        return storeLicenceVO;
    }

    @Override
    @Transactional
    public void ownerEdit(StoreOwnerEditDTO storeOwnerEditDTO) {

        if (storeOwnerEditDTO.getEnablePickup() != null && storeOwnerEditDTO.getEnablePickup()) {
            AddressSearchParams addressSearchParams = new AddressSearchParams();
            addressSearchParams.setExtendId(UserContext.getExtendId());
            addressSearchParams.setType(AddressTypeEnum.SELF_PICKUP);
            addressSearchParams.setScene(UserContext.getScene());
            List<UserAddress> userAddresses = userAddressService.addressList(addressSearchParams);
            if (userAddresses == null || userAddresses.isEmpty()) {
                throw new ServiceException(ResultCode.SELF_PICKUP_ADDRESS_NOT_HAVE);
            }
        }

        QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
        if (UserContext.getCurrentUser() != null) {
            SceneEnums scene = Objects.requireNonNull(UserContext.getCurrentUser()).getScene();
            if (scene.equals(SceneEnums.STORE) || scene.equals(SceneEnums.SUPPLIER)) {
                queryWrapper.eq("id", UserContext.getCurrentUser().getExtendId());
                queryWrapper.eq("scene", UserContext.getCurrentUser().getScene().value());
            } else {
                return;
            }
        }
        Store store = this.getOne(queryWrapper);

        if (store == null) {
            throw new ServiceException(ResultCode.STORE_NOT_EXIST);
        }

        BeanUtils.copyProperties(storeOwnerEditDTO, store);

        this.updateById(store);
        this.removeCache(store.getId());

        amqpSender.send(AmqpMessage.builder()
                .exchange(amqpExchangeProperties.getUser())
                .routingKey(UserRoutingKey.STORE_UPDATE)
                .message(store)
                .build());
    }

    @Override
    public void editStoreSetting(String configKey,String configValue) {
        //判断登录用户
        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            throw new ServiceException(ResultCode.USER_NOT_LOGIN);
        }

        if (!currentUser.getScene().equals(SceneEnums.STORE)) {
            throw new ServiceException(ResultCode.STORE_NOT_LOGIN_ERROR);
        }
        //对参数进行过滤
        if (configKey.equals(SettingEnum.POINT_SETTING.name())) {
            editStorePointSetting(configValue, currentUser);
        } else if (configKey.equals(SettingEnum.EXPERIENCE_SETTING.name())) {
            editStoreExperienceSetting(configValue, currentUser);
        } else if (configKey.equals(SettingEnum.PREMIUM_MEMBER_SETTING.name())) {
            //修改店铺积分设置
            this.update(new LambdaUpdateWrapper<Store>().eq(Store::getId, currentUser.getExtendId()).set(Store::getPremiumMemberSetting, configValue));
        }
        this.removeCache(currentUser.getExtendId());
    }

    private void editStorePointSetting(String configValue, AuthUser currentUser) {
        PointSetting pointSetting = JSONUtil.toBean(configValue, PointSetting.class);
        if (pointSetting.getPointSettingItems() != null && !pointSetting.getPointSettingItems().isEmpty()) {
            Collections.sort(pointSetting.getPointSettingItems());
            if (pointSetting.getPointSettingItems().size() > 4) {
                pointSetting.setPointSettingItems(pointSetting.getPointSettingItems().subList(0, 4));
            }
        }
        configValue = JSONUtil.toJsonStr(pointSetting);
        //修改店铺积分设置
        this.update(new LambdaUpdateWrapper<Store>().eq(Store::getId, currentUser.getExtendId()).set(Store::getPointSetting, configValue));
    }

    private void editStoreExperienceSetting(String configValue, AuthUser currentUser) {
        ExperienceSetting experienceSetting = JSONUtil.toBean(configValue, ExperienceSetting.class);
        if (experienceSetting.getExperienceSettingItems() != null && !experienceSetting.getExperienceSettingItems().isEmpty()) {
            Collections.sort(experienceSetting.getExperienceSettingItems());
            if (experienceSetting.getExperienceSettingItems().size() > 4) {
                experienceSetting.setExperienceSettingItems(experienceSetting.getExperienceSettingItems().subList(0, 4));
            }
        }
        configValue = JSONUtil.toJsonStr(experienceSetting);
        //修改店铺积分设置
        this.update(new LambdaUpdateWrapper<Store>().eq(Store::getId, currentUser.getExtendId()).set(Store::getExperienceSetting, configValue));
    }

    @Override
    public Store registerByUser(User user) {
        Store store = new Store(user);
        this.save(store);
        return store;
    }

    @Override
    public void updateStoreVerifyStatus(String extendId, boolean b) {
        this.update(new LambdaUpdateWrapper<Store>().set(Store::getVerify, b).eq(Store::getExtendId, extendId));
        removeCache(extendId);
    }

    @Override
    public String getBusinessCategory(String storeId) {

        if (systemSettingProperties.getPlatformAssignedCategory()) {
            return this.getById(storeId).getBusinessCategory();
        } else {
            List<Category> categoryList = categoryClient.firstCategory();
            List<String> categoryIds = new ArrayList<>();
            for (Category category : categoryList) {
                categoryIds.add(category.getId());
            }
            return String.join(",", categoryIds);
        }
    }

    @Override
    public String getDomainStore() {
        if (RequestContextHolder.getRequestAttributes() != null && RequestContextHolder.getRequestAttributes() instanceof ServletRequestAttributes servletRequestAttributes) {
            HttpServletRequest request = servletRequestAttributes.getRequest();
            String origin = request.getHeader("Origin"); //使用 Host 请求头
            String str = origin.split("\\.")[0];
            String[] split = str.split("-");
            String storeId = "";
            if (split.length > 1) {
                storeId = split[1];
            } else {
                try {
                    URL url = new URL(origin);
                    String host = url.getHost();
                    storeId = cache.get(CachePrefix.STORE_DOMAIN.getPrefix() + host).toString();
                } catch (Exception e) {
                    log.error("获取店铺域名失败", e);
                }
            }
            return storeId;
//            return this.getById(storeId);
        }
        return null;
    }

    @Override
    public void editDomain(String storeId, String pcDomain, String moveDomain) {
        this.update(new LambdaUpdateWrapper<Store>().eq(Store::getId, storeId).set(Store::getPcDomain, pcDomain).set(Store::getMoveDomain, moveDomain));
        //移动端域名不为空设置缓存
        if (CharSequenceUtil.isNotEmpty(moveDomain)) {
            cache.put(CachePrefix.STORE_DOMAIN.getPrefix() + moveDomain, storeId);
        }
        //PC端域名不为空设置缓存
        if (CharSequenceUtil.isNotEmpty(pcDomain)) {
            cache.put(CachePrefix.STORE_DOMAIN.getPrefix() + pcDomain, storeId);
        }
    }

    @Override
    public void checkStoreDomain(Store store) {
        if (store == null) {
            return;
        }
        if(CharSequenceUtil.isEmpty(store.getPcDomain())){
            String pc = domainProperties.getPc();
            int index = pc.indexOf('.');
            store.setPcDomain(pc.substring(0, index) + "-" + store.getId() + pc.substring(index));
        }
        if(CharSequenceUtil.isEmpty(store.getMoveDomain())){
            String wap = domainProperties.getWap();
            int index = wap.indexOf('.');
            store.setMoveDomain(wap.substring(0, index) + "-" + store.getId() + wap.substring(index));
        }
    }

    @Override
    public void setStoreTag(String storeId, String storeTag) {
        LambdaUpdateWrapper<Store> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Store::getId, storeId);
        updateWrapper.set(Store::getStoreTag, storeTag);
        this.update(updateWrapper);
        removeCache(storeId);
    }

    @Override
    public void setStoreRealPhoto(String storeId, Boolean realPhoto) {
        LambdaUpdateWrapper<Store> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Store::getId, storeId);
        updateWrapper.set(Store::getRealPhoto, realPhoto);
        this.update(updateWrapper);
        removeCache(storeId);
    }

    @Override
    public void setStoreFastDelivery(String storeId, Boolean fastDelivery) {
        LambdaUpdateWrapper<Store> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Store::getId, storeId);
        updateWrapper.set(Store::getFastDelivery, fastDelivery);
        this.update(updateWrapper);
        removeCache(storeId);
    }

    @Override
    public Page<StoreRankStatisticsVO> getStoreRankStatistics(PageVO page, String storeName, String sortField) {
        // 1. 获取店铺基础信息
        List<Store> stores = getFilteredStores(storeName);
        if (stores.isEmpty()) {
            return new Page<>(page.getPageNumber(), page.getPageSize());
        }

        List<String> storeIds = stores.stream().map(Store::getId).collect(Collectors.toList());

        // 2. 创建店铺统计VO列表，初始化基础信息
        Map<String, StoreRankStatisticsVO> storeStatisticsMap = new HashMap<>();
        for (Store store : stores) {
            StoreRankStatisticsVO vo = new StoreRankStatisticsVO();
            vo.setStoreId(store.getId());
            vo.setStoreName(store.getStoreName());
            vo.setPv(0L);
            vo.setUv(0L);
            vo.setCollectionNum(store.getCollectionNum() != null ? store.getCollectionNum().longValue() : 0L);
            vo.setImageDownloadNum(0L);
            storeStatisticsMap.put(store.getId(), vo);
        }

        // 3. 分别获取各种统计数据并合并
        try {
            // 获取PV/UV数据
            aggregatePvUvData(storeIds, sortField, storeStatisticsMap);

            // 获取图片下载数据
            aggregateImageDownloadData(storeIds, storeStatisticsMap);

            // 获取销售金额数据
            aggregateSaleAmountData(storeIds, storeStatisticsMap);

        } catch (Exception e) {
            // 记录日志，但不影响主流程
            log.warn("获取店铺统计数据时发生异常: {}", e.getMessage());
        }

        // 4. 转换为列表并排序
        List<StoreRankStatisticsVO> resultList = new ArrayList<>(storeStatisticsMap.values());
        sortStoreStatistics(resultList, sortField);

        // 5. 分页处理
        return paginateResults(resultList, page);
    }

    /**
     * 获取过滤后的店铺列表
     *
     * @param storeName 店铺名称（可选）
     * @return 店铺列表
     */
    private List<Store> getFilteredStores(String storeName) {
        LambdaQueryWrapper<Store> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Store::getScene, StoreSceneEnum.STORE.name());
        queryWrapper.eq(Store::getStoreStatus, StoreStatusEnum.OPEN.name());

        if (CharSequenceUtil.isNotEmpty(storeName)) {
            queryWrapper.like(Store::getStoreName, storeName);
        }

        return this.list(queryWrapper);
    }

    /**
     * 聚合PV/UV数据
     *
     * @param storeIds 店铺ID列表
     * @param storeStatisticsMap 店铺统计数据Map
     */
    private void aggregatePvUvData(List<String> storeIds, String sortField, Map<String, StoreRankStatisticsVO> storeStatisticsMap) {
        try {
            // 检查platformViewClient是否可用
            if (platformViewClient == null) {
                log.warn("PlatformViewClient不可用，跳过PV/UV数据聚合");
                return;
            }

            StoreRankStatisticsParams params = new StoreRankStatisticsParams();
            params.setStoreIds(storeIds);
            if (CharSequenceUtil.isNotEmpty(sortField)) {
                if (sortField.equals("pv") || sortField.equals("uv")) {
                    params.setSortField(sortField);
                }else {
                    params.setSortField("pv");
                }
            }


            // 调用统计服务获取PV/UV数据
            Page<StoreRankStatisticsVO> pvUvData = platformViewClient.getStoreStatistics(params);

            if (pvUvData != null && CollectionUtils.isNotEmpty(pvUvData.getRecords())) {
                for (StoreRankStatisticsVO pvUvVO : pvUvData.getRecords()) {
                    StoreRankStatisticsVO existingVO = storeStatisticsMap.get(pvUvVO.getStoreId());
                    if (existingVO != null) {
                        existingVO.setPv(pvUvVO.getPv() != null ? pvUvVO.getPv() : 0L);
                        existingVO.setUv(pvUvVO.getUv() != null ? pvUvVO.getUv() : 0L);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取PV/UV数据失败: {}", e.getMessage());
        }
    }

    /**
     * 聚合图片下载数据
     *
     * @param storeIds 店铺ID列表
     * @param storeStatisticsMap 店铺统计数据Map
     */
    private void aggregateImageDownloadData(List<String> storeIds, Map<String, StoreRankStatisticsVO> storeStatisticsMap) {
        try {
            // 检查goodsClient是否可用
            if (goodsClient == null) {
                log.warn("GoodsClient不可用，跳过图片下载数据聚合");
                return;
            }

            StoreRankStatisticsParams params = new StoreRankStatisticsParams();
            params.setStoreIds(storeIds);
            params.setSortField("imageDownload");

            // 调用商品服务获取图片下载数据
            Page<StoreRankStatisticsVO> imageDownloadData = goodsClient.getGoodsImageDownloadCountByStore(params);

            if (imageDownloadData != null && CollectionUtils.isNotEmpty(imageDownloadData.getRecords())) {
                for (StoreRankStatisticsVO imageVO : imageDownloadData.getRecords()) {
                    StoreRankStatisticsVO existingVO = storeStatisticsMap.get(imageVO.getStoreId());
                    if (existingVO != null) {
                        existingVO.setImageDownloadNum(imageVO.getImageDownloadNum() != null ? imageVO.getImageDownloadNum() : 0L);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取图片下载数据失败: {}", e.getMessage());
        }
    }

    /**
     * 聚合销售金额数据
     *
     * @param storeIds 店铺ID列表
     * @param storeStatisticsMap 店铺统计数据Map
     */
    private void aggregateSaleAmountData(List<String> storeIds, Map<String, StoreRankStatisticsVO> storeStatisticsMap) {
        try {
            // 检查orderClient是否可用
            if (orderClient == null) {
                log.warn("OrderClient不可用，跳过销售金额数据聚合");
                return;
            }

            StoreRankStatisticsParams params = new StoreRankStatisticsParams();
            params.setStoreIds(storeIds);
            params.setSortField("saleAmount");

            // 调用订单服务获取销售金额数据
            Page<StoreRankStatisticsVO> saleAmountData = orderClient.getSaleAmountByStore(params);

            if (saleAmountData != null && CollectionUtils.isNotEmpty(saleAmountData.getRecords())) {
                for (StoreRankStatisticsVO saleAmountVO : saleAmountData.getRecords()) {
                    StoreRankStatisticsVO existingVO = storeStatisticsMap.get(saleAmountVO.getStoreId());
                    if (existingVO != null) {
                        existingVO.setSaleAmount(saleAmountVO.getSaleAmount() != null ? saleAmountVO.getSaleAmount() : 0D);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取销售金额数据失败: {}", e.getMessage());
        }
    }

    /**
     * 对店铺统计数据进行排序
     *
     * @param resultList 结果列表
     * @param sortField 排序字段
     */
    private void sortStoreStatistics(List<StoreRankStatisticsVO> resultList, String sortField) {
        if (CharSequenceUtil.isEmpty(sortField)) {
            sortField = "pv"; // 默认按PV排序
        }

        switch (sortField) {
            case "pv":
                resultList.sort((a, b) -> Long.compare(b.getPv() != null ? b.getPv() : 0L, a.getPv() != null ? a.getPv() : 0L));
                break;
            case "uv":
                resultList.sort((a, b) -> Long.compare(b.getUv() != null ? b.getUv() : 0L, a.getUv() != null ? a.getUv() : 0L));
                break;
            case "collectionNum":
                resultList.sort((a, b) -> Long.compare(b.getCollectionNum() != null ? b.getCollectionNum() : 0L, a.getCollectionNum() != null ? a.getCollectionNum() : 0L));
                break;
            case "imageDownload":
                resultList.sort((a, b) -> Long.compare(b.getImageDownloadNum() != null ? b.getImageDownloadNum() : 0L, a.getImageDownloadNum() != null ? a.getImageDownloadNum() : 0L));
                break;
            case "saleAmount":
                resultList.sort((a, b) -> Double.compare(b.getSaleAmount() != null ? b.getSaleAmount() : 0D, a.getSaleAmount() != null ? a.getSaleAmount() : 0D));
                break;
            default:
                resultList.sort((a, b) -> Long.compare(b.getPv() != null ? b.getPv() : 0L, a.getPv() != null ? a.getPv() : 0L));
                break;
        }
    }

    /**
     * 对结果进行分页处理
     *
     * @param resultList 结果列表
     * @param page 分页参数
     * @return 分页结果
     */
    private Page<StoreRankStatisticsVO> paginateResults(List<StoreRankStatisticsVO> resultList, PageVO page) {
        int pageNumber = page.getPageNumber();
        int pageSize = page.getPageSize();
        int total = resultList.size();

        int startIndex = (pageNumber - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);

        List<StoreRankStatisticsVO> pageData = new ArrayList<>();
        if (startIndex < total) {
            pageData = resultList.subList(startIndex, endIndex);
        }

        Page<StoreRankStatisticsVO> result = new Page<>(pageNumber, pageSize, total);
        result.setRecords(pageData);

        return result;
    }

    /**
     * 删除缓存
     *
     * @param storeId 店铺id
     */
    private void removeCache(String storeId) {
        cache.remove(CachePrefix.STORE.getPrefix() + storeId);
    }

    @Override
    public void updateStoreStatistics(String storeId, Double num, String field) {
        switch (field) {
            case "goodsRealRate":
                this.update(new LambdaUpdateWrapper<Store>().set(Store::getGoodsRealRate, num).eq(Store::getId, storeId));
                break;
            case "onTimeDeliveryRate":
                this.update(new LambdaUpdateWrapper<Store>().set(Store::getOnTimeDeliveryRate, num).eq(Store::getId, storeId));
                break;
            case "returnSuccessRate":
                this.update(new LambdaUpdateWrapper<Store>().set(Store::getReturnSuccessRate, num).eq(Store::getId, storeId));
                break;
            case "qualityConformanceRate":
                this.update(new LambdaUpdateWrapper<Store>().set(Store::getQualityConformanceRate, num).eq(Store::getId, storeId));
                break;
            case "saleNum":
                this.update(new LambdaUpdateWrapper<Store>().set(Store::getSaleNum, Integer.valueOf(String.valueOf(num))).eq(Store::getId, storeId));
                break;
            default:
                break;
        }
        cache.remove(CachePrefix.STORE.getPrefix() + storeId);
    }

    @Override
    public StoreVO getStoreDetailById(String storeId) {
        StoreVO storeVO = (StoreVO) cache.get(CachePrefix.STORE.getPrefix() + storeId);
        if (storeVO == null) {
            storeVO = this.baseMapper.getStoreVO(storeId);
            if (storeVO != null) {
                cache.put(CachePrefix.STORE.getPrefix() + storeId, storeVO, 7200L);
            }
        }

        assert storeVO != null;
        if (CharSequenceUtil.isNotEmpty(storeVO.getMarket())) {
            StoreMarket storeMarket = storeMarketClient.getStoreMarketById(storeVO.getMarket());
            if (null != storeMarket) {
                storeVO.setMarketName(storeMarket.getRegionNamePath().replaceAll(",", "-"));
                storeVO.setMarketDetail(storeMarket.getMarketName() + "-" + storeMarket.getAddress());
            }
        }
        //storeVO.setGoodsNum(goodsClient.count(storeVO.getId()));
        storeVO.setTop(this.baseMapper.getStoreRankBySaleNum(storeVO.getId()));

        checkStoreDomain(storeVO);
        return storeVO;
    }
}