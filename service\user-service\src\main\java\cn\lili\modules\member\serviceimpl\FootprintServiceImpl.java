package cn.lili.modules.member.serviceimpl;

import cn.lili.common.security.context.UserContext;
import cn.lili.modules.goods.client.GoodsClient;
import cn.lili.modules.goods.entity.dos.GoodsSku;
import cn.lili.modules.member.entity.dos.FootPrint;
import cn.lili.modules.member.entity.dto.FootPrintQueryParams;
import cn.lili.modules.member.mapper.FootprintMapper;
import cn.lili.modules.member.service.FootprintService;
import cn.lili.modules.search.entity.dos.EsGoodsIndex;
import cn.lili.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.IntStream;

/**
 * 会员浏览历史业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/18 10:46 上午
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FootprintServiceImpl extends ServiceImpl<FootprintMapper, FootPrint> implements FootprintService {

    private final GoodsClient goodsClient;

    @Override
    public FootPrint saveFootprint(FootPrint footPrint) {
        if (footPrint == null || "undefined".equals(footPrint.getGoodsId())) {
            return null;
        }

        //将之前的浏览记录删除
        LambdaQueryWrapper<FootPrint> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(FootPrint::getUserId, footPrint.getUserId());
        queryWrapper.eq(FootPrint::getGoodsId, footPrint.getGoodsId());
        //如果已存在某商品记录，则更新其修改时间
        //如果不存在则添加记录
        //为了保证足迹的排序,将原本足迹删除后重新添加
        List<FootPrint> oldPrints = list(queryWrapper);
        if (oldPrints != null && !oldPrints.isEmpty()) {
            FootPrint oldPrint = oldPrints.get(0);
            this.removeById(oldPrint.getId());
        }
        if (footPrint.getSkuId() == null || "undefined".equals(footPrint.getSkuId())) {
            return footPrint;
        }
        //添加新的浏览记录
        footPrint.setCreateTime(new Date());
        this.save(footPrint);

        // 增加商品访客统计数量（只有在新增浏览记录时才增加，避免重复统计）
        if (oldPrints == null || oldPrints.isEmpty()) {
            try {
                goodsClient.incrementVisitorCount(footPrint.getGoodsId());
            } catch (Exception e) {
                // 记录日志但不影响主流程
                log.warn("增加商品访客统计失败，商品ID: {}", footPrint.getGoodsId(), e);
            }
        }

        //删除超过100条后的记录
        this.baseMapper.deleteLastFootPrint(footPrint.getUserId());
        return footPrint;
    }

    @Override
    public boolean clean() {
        LambdaQueryWrapper<FootPrint> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(FootPrint::getUserId, UserContext.getCurrentExistUser().getExtendId());
        return this.remove(lambdaQueryWrapper);
    }

    @Override
    public boolean deleteByIds(List<String> ids) {
        LambdaQueryWrapper<FootPrint> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(FootPrint::getUserId, UserContext.getCurrentExistUser().getExtendId());
        lambdaQueryWrapper.in(FootPrint::getGoodsId, ids);
        return this.remove(lambdaQueryWrapper);
    }

    @Override
    public Page<EsGoodsIndex> footPrintPage(FootPrintQueryParams params) {
        params.setSort("createTime");
        this.cleanInvalid(params);
        Page<FootPrint> footPrintPages = this.page(PageUtil.initPage(params), params.queryWrapper());
        //定义结果
        Page<EsGoodsIndex> esGoodsIndexIPage = new Page<>();

        if (footPrintPages.getRecords() != null && !footPrintPages.getRecords().isEmpty()) {
            List<String> skuIds =
                    footPrintPages.getRecords().stream().map(FootPrint::getSkuId).toList();
            List<GoodsSku> goodsSkuByIdFromCache = goodsClient.getGoodsSkuByIdFromCache(skuIds);

            // 清除无效的足迹并删除无效的足迹数据
            footPrintPages.getRecords().removeIf(footPrint -> {
                if (goodsSkuByIdFromCache.stream().noneMatch(goodsSku -> goodsSku.getId().equals(footPrint.getSkuId()))) {
                    this.removeById(footPrint.getId());
                    return true;
                }
                return false;
            });


            List<EsGoodsIndex> collect = IntStream.range(0, goodsSkuByIdFromCache.size())
                    .mapToObj(i -> {
                        if (goodsSkuByIdFromCache.get(i) == null) {
                            EsGoodsIndex esGoodsIndex = new EsGoodsIndex();
                            FootPrint footPrint = footPrintPages.getRecords().get(i);
                            esGoodsIndex.setGoodsId(footPrint.getGoodsId());
                            esGoodsIndex.setId(footPrint.getSkuId());
                            esGoodsIndex.setReleaseTime(footPrintPages.getRecords().get(i).getCreateTime());
                            return esGoodsIndex;
                        }
                        Optional<FootPrint> first = footPrintPages.getRecords().stream().filter(j -> j.getSkuId().equals(goodsSkuByIdFromCache.get(i).getId())).findFirst();
                        return first.map(footPrint -> new EsGoodsIndex(goodsSkuByIdFromCache.get(i), footPrint.getCreateTime())).orElseGet(() -> new EsGoodsIndex(goodsSkuByIdFromCache.get(i)));
                    })
                    .toList();
            esGoodsIndexIPage.setPages(footPrintPages.getPages());

            esGoodsIndexIPage.setRecords(collect);
            esGoodsIndexIPage.setTotal(footPrintPages.getTotal());
            esGoodsIndexIPage.setSize(footPrintPages.getSize());
            esGoodsIndexIPage.setCurrent(footPrintPages.getCurrent());
            return esGoodsIndexIPage;
        }
        return esGoodsIndexIPage;
    }

    private void cleanInvalid(FootPrintQueryParams params) {
        // 查询条件
        QueryWrapper<FootPrint> queryWrapper = params.queryWrapper();
        queryWrapper.select("sku_id");
        List<FootPrint> footPrints = this.list(queryWrapper);
        if (footPrints == null || footPrints.isEmpty()) {
            return;
        }
        // 清除无效的足迹并删除无效的足迹数据
        footPrints.removeIf(footPrint -> {
            if (goodsClient.getGoodsSkuByIdFromCache(footPrint.getSkuId()) == null) {
                this.removeById(footPrint.getId());
                return true;
            }
            return false;
        });
    }

    @Override
    public long getFootprintNum() {
        //用户
        LambdaQueryWrapper<FootPrint> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(FootPrint::getUserId, UserContext.getCurrentUser().getExtendId());
        return this.count(lambdaQueryWrapper);
    }

}