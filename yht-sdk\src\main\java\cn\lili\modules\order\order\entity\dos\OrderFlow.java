package cn.lili.modules.order.order.entity.dos;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.CurrencyUtil;
import cn.lili.common.utils.SnowFlake;
import cn.lili.modules.order.order.entity.dto.CurrentRefundPriceDTO;
import cn.lili.modules.order.order.entity.dto.OrderFlowLog;
import cn.lili.modules.order.order.entity.dto.PriceDetailDTO;
import cn.lili.modules.order.order.entity.enums.OrderFlowStatusEnum;
import cn.lili.modules.order.order.entity.enums.ProfitSharingStatusEnum;
import cn.lili.modules.payment.entity.enums.PaymentMethodEnum;
import cn.lili.mybatis.model.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 商家订单流水
 *
 * <AUTHOR>
 * @since 2020/11/17 7:31 下午
 */
@Data
@TableName("li_order_flow")
@Schema(title = "订单流水")
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class OrderFlow extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -5998757398902747939L;

    @Schema(title = "流水编号")
    private String sn;

    @Schema(title = "流水金额")
    private Double flowPrice;

    @Schema(title = "退款金额")
    private Double refundPrice;

    @Schema(title = "采购价")
    private Double procurePrice;

    @Schema(title = "采购退款金额")
    private Double procureRefundPrice;

    @Schema(title = "是否付款，true为已付款")
    private Boolean isPay;
    @Schema(title = "是否全部退款，true为全部退款")
    private Boolean fullRefund;

    @Schema(title = "订单sn")
    private String orderSn;

    /**
     * @see PaymentMethodEnum
     */
    @Schema(title = "支付方式名称")
    private String paymentMethod;

    @Schema(title = "第三方交易流水号")
    private String transactionId;

    @CreatedDate
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    @Schema(title = "创建时间", hidden = true)
    private Date createTime;

    @Schema(title = "会员id")
    private String memberId;

    @Schema(title = "会员名称")
    private String nickname;

    @Schema(title = "平台商户id")
    private String platformMchid;

    @Schema(title = "店铺ID")
    private String storeId;
    @Schema(title = "店铺名称")
    private String storeName;

    @Schema(title = "店铺子商户ID")
    private String storeSubMchid;

    @Schema(title = "供应商标识")
    private String supplierId;
    @Schema(title = "供应商名称")
    private String supplierName;

    @Schema(title = "供应商子商户ID")
    private String supplierSubMchid;

    //============================================distribution================================================

    /**
     * @see cn.lili.modules.distribution.entity.enums.DistributionModelEnum
     */
    @Schema(title = "分销佣金模式(平台承担/商家承担)")
    private String commissionModel;

    @Schema(title = "一级分销商会员 ID")
    private String firstDistributionUserId;

    @Schema(title = "一级分销商")
    private String firstDistributionId;

    @Schema(title = "一级分销商昵称")
    private String firstDistributionName;

    @Schema(title = "一级分销返现支出")
    private Double firstCommission = 0D;

    @Schema(title = "一级分销商绑定ID")
    private String firstDistributionBindId;

    @Schema(title = " 二级分销商会员 ID")
    private String secondaryDistributionUserId;

    @Schema(title = "二级分销商")
    private String secondaryDistributionId;

    @Schema(title = "二级分销商昵称")
    private String secondaryDistributionName;

    @Schema(title = "二级分销返佣支出")
    private Double secondaryCommission = 0D;

    @Schema(title = "二级分销商绑定ID")
    private String secondaryDistributionBindId;

    //============================================profitsharing================================================

    @Schema(title = "店铺结算金额")
    private Double sellerSettlementPrice = 0D;
    @Schema(title = "供应商结算金额")
    private Double supplierSettlementPrice = 0D;
    @Schema(title = "分销佣金")
    private Double distributionSettlementPrice = 0D;
    @Schema(
        title = "平台盈利金额=实际支付金额(flowPrice)-商家结算金额(sellerSettlementPrice)-供应商结算金额(supplierSettlementPrice)")
    private Double platformSettlementPrice = 0D;

    //============================================end profitsharing================================================

    @Schema(title = "流水详情")
    private String priceDetail;
    //用户支付金额=平台佣金+供应商金额+店铺金额+分销佣金

    @Schema(title = "店铺核验状态")
    private Boolean storeVerify = true;

    @Schema(title = "供应商核验状态")
    private Boolean supplierVerify = true;

    @Schema(title = "平台核验状态")
    private Boolean platformVerify = true;

    @Schema(
        title = "是否为特殊订单：特殊订单为某一端结算金额为负数，那么付款目标转向平台子商户，然后分账统一从平台余额中提取")
    private Boolean specialOrder;

    @Schema(title = "店铺补交差额流水号")
    private String storeOutTradeNo;

    @Schema(title = "供应商补交差额流水号")
    private String supplierOutTradeNo;

    @Schema(title = "平台补交差额流水号")
    private String platformOutTradeNo;

    /**
     * @see OrderFlowStatusEnum
     */
    @Schema(title = "审核状态")
    private String orderFlowStatus = OrderFlowStatusEnum.PASS.name();

    /**
     * @see cn.lili.modules.order.order.entity.enums.ProfitSharingStatusEnum
     */
    @Schema(title = "分账状态")
    private String profitSharing;

    @Schema(title = "分账错误消息")
    private String errorMsg;

    @Schema(title = "订单流水日志")
    private String orderFlowLog;

    @Schema(title = "订单商品项")
    private String skuJson;

    @Schema(title = "是否代发订单")
    private Boolean isProxy;

    /**
     * 构造方法
     *
     * @param order 订单
     */
    public OrderFlow(Order order, List<OrderItem> orderItems) {

        //去掉时间。
        this.setRefundPrice(0D);
        this.setFullRefund(false);
        this.setIsPay(true);

        this.setIsProxy(order.getIsProxy());

        if (order.getIsProxy()) {
            this.setProfitSharing(ProfitSharingStatusEnum.FINISHED.name());
        }else {
            this.setProfitSharing(ProfitSharingStatusEnum.WAIT_COMPLETE.name());
        }


        //入账
        this.setSn(SnowFlake.createStr("OF"));

        this.setStoreId(order.getStoreId());

        this.setStoreName(order.getStoreName());
        this.setMemberId(order.getBuyerId());
        this.setNickname(order.getNickname());
        //添加支付方式
        this.setPaymentMethod(order.getPaymentMethod());
        //添加第三方支付流水号
        this.setTransactionId(order.getTransactionId());
        this.setOrderSn(order.getSn());

        this.setSupplierName(order.getSupplierName());
        this.setSupplierId(order.getSupplierId());


        //获取金额详情
        PriceDetailDTO priceDetailDTO = PriceDetailDTO.getModel(order.getPriceDetail());

        if (priceDetailDTO != null) {
            this.setFlowPrice(CurrencyUtil.add(priceDetailDTO.getFlowPrice(), order.getServiceFee()));
            this.setSupplierSettlementPrice(priceDetailDTO.getSupplierSettlementPrice());
            this.setSellerSettlementPrice(priceDetailDTO.getSellerSettlementPrice());
            this.setPlatformSettlementPrice(priceDetailDTO.getPlatformSettlementPrice());

            this.setDistributionSettlementPrice(priceDetailDTO.getDistributorSettlementPrice());



            if (priceDetailDTO.getFirstCommission() == null) {
                priceDetailDTO.setFirstCommission(0D);
            }
            if (priceDetailDTO.getSecondaryCommission() == null) {
                priceDetailDTO.setSecondaryCommission(0D);
            }

            this.setFirstCommission(priceDetailDTO.getFirstCommission());
            this.setSecondaryCommission(priceDetailDTO.getSecondaryCommission());
            if (priceDetailDTO.getFirstCommission() > 0D) {
                this.setFirstDistributionId(order.getFirstDistributionId());
                this.setFirstDistributionName(order.getFirstDistributionName());
                this.setFirstDistributionUserId(order.getFirstDistributionUserId());
            }
            if(priceDetailDTO.getSecondaryCommission() > 0D) {
                this.setSecondaryDistributionId(order.getSecondaryDistributionId());
                this.setSecondaryDistributionName(order.getSecondaryDistributionName());
                this.setSecondaryDistributionUserId(order.getSecondaryDistributionUserId());
            }

            this.setPriceDetail(priceDetailDTO.toJson());

            this.setCommissionModel(priceDetailDTO.getCommissionModel());
        } else {
            throw new ServiceException("订单流水金额详情为空");
        }
        initSkus(orderItems);
    }

    private void initSkus(List<OrderItem> orderItems) {
        if (orderItems == null || orderItems.isEmpty()) {
            return;
        }
        List<OrderFlowSku> orderFlowSkus = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            OrderFlowSku orderFlowSku = new OrderFlowSku();
            orderFlowSku.setGoodsId(orderItem.getGoodsId());
            orderFlowSku.setGoodsName(orderItem.getGoodsName());
            orderFlowSku.setGoodsImage(orderItem.getImage());
            orderFlowSku.setNum(orderItem.getNum());
            orderFlowSku.setSpecs(orderItem.getSpecs());
            orderFlowSku.setSkuId(orderItem.getSkuId());
            orderFlowSku.setPrice(orderItem.getGoodsPrice());
            orderFlowSkus.add(orderFlowSku);
        }
        this.skuJson = JSONUtil.toJsonStr(orderFlowSkus);
    }

    public List<OrderFlowSku> getSkus() {
        if (CharSequenceUtil.isEmpty(skuJson)) {
            return new ArrayList<>();
        } else {
            return JSONUtil.toList(skuJson, OrderFlowSku.class);
        }
    }

    public List<OrderFlowLog> getOrderFlowPaymentLog() {
        if (CharSequenceUtil.isEmpty(orderFlowLog)) {
            return new ArrayList<>();
        } else {
            return JSONUtil.toList(orderFlowLog, OrderFlowLog.class);
        }
    }

    public void setOrderFlowPaymentLog(List<OrderFlowLog> paymentLog) {
        this.orderFlowLog = JSONUtil.toJsonStr(paymentLog);
    }

    /**
     * 校验结果，如果三个都为true，则可以发货操作，否则需要手动审核操作才可以
     *
     * @return 校验结果
     */
    public Boolean verify() {
        return this.storeVerify && this.supplierVerify && this.platformVerify;
    }

    /**
     * 校验消息
     *
     * @return 校验消息
     */
    public String verifyMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append("订单编号{").append(this.orderSn).append("}");
        if (Boolean.FALSE.equals(this.storeVerify)) {
            sb.append("异常订单需店铺手动审核。");
        }
        if (Boolean.FALSE.equals(this.supplierVerify)) {
            sb.append("异常订单需供应商手动审核。");
        }
        if (Boolean.FALSE.equals(this.platformVerify)) {
            sb.append("异常订单需平台手动审核。");
        }
        return sb.toString();
    }

    public PriceDetailDTO getPriceDetailDTO() {
        return PriceDetailDTO.getModel(this.getPriceDetail());
    }

    /**
     * 部分退款
     *
     * @param currentRefundPriceDTO 退款金额DTO
     */
    public void appendRefundPrice(CurrentRefundPriceDTO currentRefundPriceDTO) {
        this.appendRefundPrice(currentRefundPriceDTO.getTotalRefundPrice());

        // 如果退款后剩余金额小于等于流水金额，则表示全部退款，则无需再进行一次扣减，金额已经归零
        if (!this.getFullRefund()) {
            this.reduceDistributionPrice(currentRefundPriceDTO.getDistributionRefundPrice());

            this.reduceFirstCommission(currentRefundPriceDTO.getFirstCommission());
            this.reduceSecondaryCommission(currentRefundPriceDTO.getSecondaryCommission());

            this.reducePlatformPrice(currentRefundPriceDTO.getPlatformRefundPrice());
            this.reduceSellerPrice(currentRefundPriceDTO.getSellerRefundPrice());
            this.reduceSupplierPrice(currentRefundPriceDTO.getSupplierRefundPrice());
        }
    }

    /**
     * 全额退款
     *
     * @param refundPrice 退款金额
     */
    public void appendRefundPrice(Double refundPrice) {
        this.setRefundPrice(CurrencyUtil.add(this.getRefundPrice(), refundPrice));
        if (this.getRefundPrice() >= (this.getFlowPrice())) {
            this.setFullRefund(true);
            this.setProfitSharing(ProfitSharingStatusEnum.ORDER_CANCEL.name());
            this.sellerSettlementPrice = 0D;
            this.supplierSettlementPrice = 0D;
            this.distributionSettlementPrice = 0d;
            this.firstCommission = 0d;
            this.secondaryCommission = 0d;
            this.platformSettlementPrice = 0d;
        }
    }

    public void reducePlatformPrice(Double price) {
        if (price == null) {
            return;
        }
        this.setPlatformSettlementPrice(CurrencyUtil.sub(this.getPlatformSettlementPrice(), price));
    }

    public void reduceSupplierPrice(Double price) {
        if (price == null) {
            return;
        }
        this.setSupplierSettlementPrice(CurrencyUtil.sub(this.getSupplierSettlementPrice(), price));
    }

    public void reduceSellerPrice(Double price) {
        if (price == null) {
            return;
        }
        this.setSellerSettlementPrice(CurrencyUtil.sub(this.getSellerSettlementPrice(), price));
    }

    public void reduceDistributionPrice(Double price) {
        if (price == null) {
            return;
        }
        this.setDistributionSettlementPrice(CurrencyUtil.sub(this.getDistributionSettlementPrice(), price));
    }

    public void reduceFirstCommission(Double price) {
        if (price == null) {
            return;
        }
        this.setFirstCommission(CurrencyUtil.sub(this.getFirstCommission(), price));
    }

    public void reduceSecondaryCommission(Double price) {
        if (price == null) {
            return;
        }
        this.setSecondaryCommission(CurrencyUtil.sub(this.getSecondaryCommission(), price));
    }

    public Double getSellerSettlementPrice() {
        if (sellerSettlementPrice == null) {
            return 0D;
        }
        return sellerSettlementPrice;
    }

    public Double getSupplierSettlementPrice() {
        if (supplierSettlementPrice == null) {
            return 0D;
        }
        return supplierSettlementPrice;
    }

    public Double getDistributionSettlementPrice() {
        if (distributionSettlementPrice == null) {
            return 0D;
        }
        return distributionSettlementPrice;
    }

    public Double getPlatformSettlementPrice() {
        if (platformSettlementPrice == null) {
            return 0D;
        }
        return platformSettlementPrice;
    }

    public Double getRefundPrice() {
        if (refundPrice == null) {
            return 0D;
        }
        return refundPrice;
    }

    public void appendProcureRefundPrice(Double price) {
        this.setProcureRefundPrice(CurrencyUtil.add(this.getProcureRefundPrice(), price));
    }

    public Double getProcurePrice() {
        if (procurePrice == null) {
            return 0D;
        }
        return procurePrice;
    }

    public Double getProcureRefundPrice() {
        if (procureRefundPrice == null) {
            return 0D;
        }
        return procureRefundPrice;
    }

    /**
     * 生成补差日志
     *
     * @return 补差日志
     */
    public String generateSubsidiseLog() {
        // 生成补差日志
        StringBuilder stringBuilder = new StringBuilder("订单补差-");
        if (platformSettlementPrice < 0) {
            stringBuilder.append("平台补差金额：").append(platformSettlementPrice);
            if (sellerSettlementPrice < 0) {
                stringBuilder.append("; ");
            }
        }
        if (sellerSettlementPrice < 0) {
            stringBuilder.append("店铺补差金额：").append(sellerSettlementPrice);
        }
        return stringBuilder.toString();
    }
}