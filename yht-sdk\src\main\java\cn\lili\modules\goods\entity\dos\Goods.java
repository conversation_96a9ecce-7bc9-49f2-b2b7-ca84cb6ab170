package cn.lili.modules.goods.entity.dos;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.ValidateParamsUtil;
import cn.lili.modules.goods.entity.dto.GoodsOperationDTO;
import cn.lili.modules.goods.entity.dto.GoodsSkuOperationDTO;
import cn.lili.modules.goods.entity.dto.Wholesale;
import cn.lili.modules.goods.entity.enums.GoodsAuthEnum;
import cn.lili.modules.goods.entity.enums.GoodsMarketEnum;
import cn.lili.modules.goods.entity.enums.SalesModeEnum;
import cn.lili.modules.search.entity.dos.EsGoodsIndex;
import cn.lili.modules.search.entity.dos.EsSupplierGoodsIndex;
import cn.lili.mybatis.model.BaseSceneEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xkcoding.http.util.StringUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.BeanUtils;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serial;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 商品
 *
 * <AUTHOR>
 * @since 2020-02-23 9:14:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("li_goods")
@Schema(title = "商品")
@Accessors(chain = true)
@NoArgsConstructor
public class Goods extends BaseSceneEntity {

    @Serial
    private static final long serialVersionUID = 370683495251252601L;

    @Schema(title = "商品名称")
    @NotEmpty(message = "商品名称不能为空")
    @Length(max = 100, message = "商品名称太长，不能超过100个字符")
    private String goodsName;

    @Schema(title = "商品价格")
    @NotNull(message = "商品价格不能为空")
    @Min(value = 0, message = "商品价格不能为负数")
    @Max(value = 99999999, message = "商品价格不能超过99999999")
    private Double price;

    @Schema(title = "品牌id")
    private String brandId;

    @Schema(title = "分类path")
    private String categoryPath;

    @Schema(title = "计量单位")
    private String goodsUnit;

    @Length(max = 60, message = "商品卖点太长，不能超过60个字符")
    @Schema(title = "卖点")
    private String sellingPoint;

    /**
     * @see GoodsMarketEnum
     */
    @Schema(title = "上架状态")
    private String marketEnable;

    @Schema(title = "详情")
    private String intro;

    @Schema(title = "购买数量")
    private Integer buyCount;

    @Max(value = 99999999, message = "库存不能超过99999999")
    @Schema(title = "库存")
    private Integer quantity;

    @Schema(title = "商品好评率")
    private Double grade;

    @Schema(title = "缩略图路径")
    private String thumbnail;

    @Schema(title = "小图路径")
    private String small;

    @Schema(title = "原图路径")
    private String original;

    @Schema(title = "店铺分类id")
    private String storeCategoryPath;

    @Schema(title = "评论数量")
    private Integer commentNum;

    @Schema(title = "卖家id")
    private String storeId;

    @Schema(title = "卖家名字")
    private String storeName;

    @Schema(title = "运费模板id")
    private String templateId;

    /**
     * @see GoodsAuthEnum
     */
    @Schema(title = "审核状态")
    private String authFlag;

    @Schema(title = "审核信息")
    private String authMessage;

    @Schema(title = "下架原因")
    private String underMessage;

    @Schema(title = "是否自营")
    private Boolean selfOperated;

    @Schema(title = "商品移动端详情")
    private String mobileIntro;

    @Schema(title = "商品视频")
    private String goodsVideo;

    @Schema(title = "是否为推荐商品")
    private Boolean recommend;

    /**
     * @see SalesModeEnum
     */
    @Schema(title = "销售模式")
    private String salesModel;

    /**
     * @see cn.lili.modules.goods.entity.enums.GoodsTypeEnum
     */
    @Schema(title = "商品类型")
    private String goodsType;

    @Schema(title = "商品参数json", hidden = true)
    private String params;

    @Schema(title = "存入索引目标，为空则应该是默认索引", hidden = true)
    private String esIndex;

    @Schema(title = "供应商ID")
    private String supplierId;

    @Schema(title = "供应商名称")
    private String supplierName;

    @Schema(title = "供应商商品ID")
    private String supplierGoodsId;

    @Schema(title = "支持代发")
    private Boolean supportProxy;

    @Schema(title = "支持采购")
    private Boolean supportPurchase;

    /**
     * @see cn.lili.modules.goods.entity.enums.PurchaseRuleEnum
     */
    @Schema(title = "采购规则")
    private String purchaseRule;

    @Schema(title = "起售数量")
    private Integer minimum;

    /**
     * @see cn.lili.modules.goods.entity.dto.Wholesale
     */
    @Schema(title = "批发商品消费规则列表 List<Wholesale>")
    private String wholesale;
    /**
     * @see cn.lili.modules.goods.entity.enums.SupplierEnum
     */
    @Schema(title = "供应商类型")
    private String supplierEnum;

    @Schema(title = "保留字段1", hidden = true)
    private String field1;

    @Schema(title = "保留字段2", hidden = true)
    private String field2;

    @Schema(title = "保留字段3", hidden = true)
    private String field3;

    @Schema(title = "保留字段4", hidden = true)
    private String field4;

    @Schema(title = "保留字段5", hidden = true)
    private String field5;

    @Schema(title = "扩展字段，可自由存储，数据库为text格式", hidden = true)
    private String ext;

    @Schema(title = "是否是代理商品")
    private Boolean isProxyGoods = false;

    @Schema(title = "是否商品会员")
    private Boolean isMemberGoods = false;

    @Schema(title = "商品规格")
    private String specs;

    /**
     * 图片资源包
     */
    @Schema(title = "图片资源包")
    private String imageZipFile;

    @Schema(title = "图片下载次数")
    private Integer imageDownloadCount;

    @Schema(title = "访客统计数量")
    private Integer visitorCount;

    /**
     * 商品收藏数
     */
    @Schema(title = "商品收藏数")
    @TableField(exist = false)
    private Long collectNum;

    @Schema(title = "图片下载率")
    @TableField(exist = false)
    private Double imageDownloadRate;

    @Schema(title = "是否是实拍商品")
    private Boolean isRealShoot;

    @Schema(title = "是否现货商品")
    private Boolean isInStock;

    @Schema(title = "是否支持退现")
    private Boolean isBackMoney = false;

    @Schema(title = "搜同款商品ID")
    private String productId;

    @Schema(title = "店铺logo")
    private String storeLogo;

    @Schema(title = "预售截止时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Field(type = FieldType.Date, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date preSaleDeadline;

    @Schema(title = "货号")
    private String itemCode;

    public Goods(GoodsOperationDTO goodsOperationDTO) {
        BeanUtils.copyProperties(goodsOperationDTO, this);

        if (CharSequenceUtil.isEmpty(goodsOperationDTO.getGoodsUnit())) {
            this.goodsUnit = "个";
        }

        if (goodsOperationDTO.getGoodsParamsDTOList() != null && !goodsOperationDTO.getGoodsParamsDTOList().isEmpty()) {
            this.params = JSONUtil.toJsonStr(goodsOperationDTO.getGoodsParamsDTOList());
        }
        //如果立即上架则
        this.marketEnable = Boolean.TRUE.equals(goodsOperationDTO.getRelease()) ? GoodsMarketEnum.UPPER.name()
                : GoodsMarketEnum.DOWN.name();

        if (StringUtil.isNotEmpty(goodsOperationDTO.getGoodsId())) {
            this.setId(goodsOperationDTO.getGoodsId());
        }
        //循环sku，判定sku是否有效
        if (goodsOperationDTO.getSkuList() == null) {
            ValidateParamsUtil.throwInvalidParamError(ResultCode.MUST_HAVE_GOODS_SKU_VALUE.message());
        }
        for (GoodsSkuOperationDTO goodsSkuOperationDTO : goodsOperationDTO.getSkuList()) {
            goodsSkuOperationDTO.checkSkuAvailable(salesModel, goodsType);
        }
        // 如果是新增商品 评论数量为0，好评率为100，购买数量为0
        if (CharSequenceUtil.isEmpty(goodsOperationDTO.getGoodsId())) {
            commentNum = 0;
            grade = 100D;
            buyCount = 0;
        }
    }

    public Double getGrade() {
        //如果没有评分，也没有评论，那么评分为100
        if ((grade == null || grade <= 0) && (commentNum == null || commentNum == 0)) {
            return 100D;
        }
        return grade;
    }

    public Boolean getSupportProxy() {
        if (supportProxy == null) {
            return false;
        }
        return supportProxy;
    }

    public Boolean getSupportPurchase() {
        if (supportPurchase == null) {
            return false;
        }
        return supportPurchase;
    }

    public Boolean getIsProxyGoods() {
        if (isProxyGoods == null) {
            return false;
        }
        return isProxyGoods;
    }

    public Boolean getIsMemberGoods() {
        if (isMemberGoods == null) {
            return false;
        }
        return isMemberGoods;
    }

    public String getTemplateId() {
        if (CharSequenceUtil.isEmpty(templateId)) {
            return "0";
        }
        return templateId;
    }

    public String getIntro() {
        if (CharSequenceUtil.isNotEmpty(intro)) {
            return HtmlUtil.unescape(intro);
        }
        return "";
    }

    public String getMobileIntro() {
        if (CharSequenceUtil.isNotEmpty(mobileIntro)) {
            return HtmlUtil.unescape(mobileIntro);
        }
        return "";
    }

    public List<Wholesale> getWholesaleList() {
        if (StringUtil.isNotEmpty(wholesale)) {
            return JSONUtil.toList(JSONUtil.parseArray(wholesale), Wholesale.class);
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 获取商品参数
     */
    public void setWholesaleList(List<Wholesale> wholesaleList) {
        if (wholesaleList != null && !wholesaleList.isEmpty()) {
            this.wholesale = JSONUtil.toJsonStr(wholesaleList);
        }
    }

    /**
     * 供应商商品只可以修改部分字段
     *
     * @param goodsOperationDTO 商品
     */
    public Goods supplierGoods(GoodsOperationDTO goodsOperationDTO) {
        this.goodsName = goodsOperationDTO.getGoodsName();
        this.sellingPoint = goodsOperationDTO.getSellingPoint();
        this.intro = goodsOperationDTO.getIntro();
        this.mobileIntro = goodsOperationDTO.getMobileIntro();
        this.storeCategoryPath = goodsOperationDTO.getStoreCategoryPath();
        return this;
    }

    public Class<? extends EsGoodsIndex> getEsIndexClass() {
        return SceneEnums.SUPPLIER.name().equals(this.getScene()) ? EsSupplierGoodsIndex.class : EsGoodsIndex.class;
    }
}