package cn.lili.modules.store.fallback;

import cn.lili.common.exception.ServiceException;
import cn.lili.modules.store.client.FreightTemplateClient;
import cn.lili.modules.store.entity.dos.FreightTemplate;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: ftyy
 * @date: 2022-01-13 14:15
 * @description: 运费模板Fallback
 */

public class FreightTemplateFallback implements FreightTemplateClient {
    @Override
    public List<FreightTemplateVO> getFreightTemplateList(String storeId) {
        throw new ServiceException();
    }


    @Override
    public List<FreightTemplateVO> getFreightTemplateList() {
        throw new ServiceException();
    }

    @Override
    public FreightTemplateVO getFreightTemplate(String id) {
        throw new ServiceException();
    }

    @Override
    public FreightTemplateVO addFreightTemplate(FreightTemplateVO freightTemplateVO) {
        throw new ServiceException();
    }

    @Override
    public FreightTemplateVO editFreightTemplate(FreightTemplateVO freightTemplateVO) {
        throw new ServiceException();
    }

    @Override
    public boolean removeFreightTemplate(String id) {
        throw new ServiceException();
    }

    @Override
    public FreightTemplate getById(String id) {
        throw new ServiceException();
    }
}
