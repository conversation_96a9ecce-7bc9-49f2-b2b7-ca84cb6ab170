package cn.lili.modules.order.order.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class OrderFeeDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = -6293102172184734928L;

    @Schema(title = "订单编号")
    private String orderSn;

    @Schema(title = "订单状态")
    private String orderStatus;

    @Schema(title = "订单金额")
    private Double orderPrice;

    @Schema(title = "商品价格")
    private Double goodsPrice;

    @Schema(title = "运费")
    private Double freightPrice;

    @Schema(title = "退款费用")
    private Double refundPrice;

    @Schema(title = "支付状态")
    private String payStatus;

    @Schema(title = "发货状态")
    private String deliverStatus;

    @Schema(title = "店铺ID")
    private String storeId;

    @Schema(title = "店铺名称")
    private String storeName;

    @Schema(title = "商品ID")
    private String goodsId;

    @Schema(title = "SKU ID")
    private String skuId;

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "商品图片")
    private String goodsImage;
}
