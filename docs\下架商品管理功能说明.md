# 下架商品管理功能说明

## 功能概述

本功能为电商系统提供了完整的下架商品管理能力，包括下架商品的查询、统计、排序和自动清理等功能。

## 主要特性

### 1. 下架时间记录
- 商品下架时自动记录下架时间
- 商品重新上架时清空下架时间
- 支持按下架时间范围查询

### 2. 多维度查询
- 支持按商品名称、分类、价格范围查询
- 支持按下架时间范围筛选
- 提供多种排序方式：下架时间、价格、综合排序

### 3. 分类统计
- 按商品分类统计下架商品数量
- 支持多级分类统计展示
- 实时更新统计数据

### 4. 自动清理
- 定时任务自动清理超过一年的下架商品
- 支持手动触发清理操作
- 清理操作采用软删除方式

## 数据库变更

### 新增字段
```sql
-- 商品表新增下架时间字段
ALTER TABLE li_goods ADD COLUMN off_shelf_time DATETIME NULL COMMENT '下架时间';

-- 添加索引
CREATE INDEX idx_goods_off_shelf_time ON li_goods(off_shelf_time);
CREATE INDEX idx_goods_market_enable_off_shelf_time ON li_goods(market_enable, off_shelf_time);
CREATE INDEX idx_goods_store_market_off_shelf ON li_goods(store_id, market_enable, off_shelf_time);
```

## API接口

### 1. 分页查询下架商品
```
POST /goods/offShelf/page
```

**请求参数：**
```json
{
  "storeId": "商家ID",
  "goodsName": "商品名称",
  "categoryPath": "分类路径",
  "price": "价格范围，如10_1000",
  "offShelfTimeStart": "下架时间开始",
  "offShelfTimeEnd": "下架时间结束",
  "sortType": "排序类型：offShelfTime|price|comprehensive",
  "sortDirection": "排序方向：asc|desc",
  "pageNumber": 1,
  "pageSize": 10
}
```

### 2. 获取下架商品分类统计
```
GET /goods/offShelf/category/count?storeId={storeId}
```

### 3. 自动删除超期商品
```
POST /goods/offShelf/auto/delete
```

## 核心类说明

### 1. OffShelfGoodsSearchParams
下架商品查询参数类，继承自PageVO，提供丰富的查询条件。

### 2. OffShelfGoodsCategoryCountVO
下架商品分类统计VO，包含分类信息和商品数量。

### 3. OffShelfGoodsController
下架商品控制器，提供REST API接口。

### 4. OffShelfGoodsCleanupTask
定时任务类，负责自动清理超期下架商品。

## 前端页面

### 功能特性
- 响应式搜索表单
- 实时分类统计展示
- 可配置的表格列
- 批量操作支持
- 友好的用户交互

### 主要组件
- 搜索条件表单
- 分类统计卡片
- 商品列表表格
- 批量操作按钮

## 定时任务

### 自动清理任务
- **执行时间：** 每天凌晨2点
- **清理规则：** 删除下架时间超过365天的商品
- **删除方式：** 软删除（设置delete_flag=true）
- **日志记录：** 记录清理数量和执行状态

## 使用说明

### 1. 商家端使用
1. 进入商品管理 -> 下架商品管理
2. 使用搜索条件筛选目标商品
3. 查看分类统计了解下架商品分布
4. 对商品进行恢复上架或删除操作

### 2. 管理员使用
1. 可查看所有商家的下架商品
2. 执行批量清理操作
3. 监控系统自动清理任务执行情况

### 3. 系统维护
1. 定期检查定时任务执行日志
2. 监控数据库存储空间使用情况
3. 根据业务需要调整清理策略

## 注意事项

1. **数据安全：** 清理操作采用软删除，可在必要时恢复数据
2. **性能优化：** 已添加必要的数据库索引，确保查询性能
3. **权限控制：** 商家只能查看自己的下架商品
4. **日志监控：** 重要操作都有日志记录，便于问题排查

## 扩展建议

1. **数据导出：** 支持下架商品数据导出功能
2. **统计报表：** 增加下架商品趋势分析报表
3. **通知提醒：** 商品下架时间过长时发送提醒
4. **批量操作：** 支持批量恢复上架和删除操作
