package cn.lili.modules.store.entity.dto;

import cn.lili.common.security.context.UserContext;
import cn.lili.common.utils.ValidateParamsUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 店铺修改DTO
 *
 * <AUTHOR>
 * @since 2020-08-22 15:10:51
 */
@Data
public class StoreEditDTO {

    //    @NotBlank(message = "店铺不能为空")
    @Schema(title = "店铺id")
    private String storeId;

    @Schema(title = "店铺名称")
    private String storeName;

    @Schema(title = "店铺logo")
    private String storeLogo;

    @Schema(title = "店铺简介")
    private String storeDesc;

    @Schema(title = "经纬度")
    private String storeCenter;

    @Schema(title = "店铺经营类目")
    private String[] businessCategory;

    @Schema(title = "是否自营")
    private Boolean selfOperated;

    @Schema(title = "是否开启自提")
    private Boolean enablePickup;

    @Schema(title = "是否允许购买商品")
    private Boolean isBuyGoods;

    @Schema(title = "是否支持退现")
    private Boolean isBackMoney;

    @Schema(title = "是否推荐")
    private Boolean isRecommend;

    public boolean validateParams() {
        if (!ValidateParamsUtil.isValidString(storeName, 2, 50)) {
            ValidateParamsUtil.throwInvalidParamError("店铺名称长度为2-200个字符");
        }
        if (!ValidateParamsUtil.isValidString(storeDesc, 6, 200)) {
            ValidateParamsUtil.throwInvalidParamError("店铺简介需在6-200字符之间");
        }
        // 如果不是管理员，不允许修改是否自营
        if (!UserContext.isManager()) {
            selfOperated = null;
            businessCategory = null;
        }
        return true;
    }
}
