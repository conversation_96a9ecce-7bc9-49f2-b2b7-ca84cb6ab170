package cn.lili.modules.order.order.mapper;

import cn.lili.modules.order.order.entity.dos.OrderFlow;
import cn.lili.modules.order.order.entity.dos.OrderItemFlow;
import cn.lili.modules.order.order.entity.vo.OrderFlowStatisticsVO;
import cn.lili.modules.statistics.entity.vo.CategoryStatisticsDataVO;
import cn.lili.modules.statistics.entity.vo.GoodsStatisticsDataVO;
import cn.lili.modules.statistics.entity.vo.StoreStatisticsDataVO;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 商品统计数据处理层
 *
 * <AUTHOR>
 * @since 2020/11/17 7:34 下午
 */
public interface OrderFlowStatisticsMapper extends BaseMapper<OrderItemFlow> {

    /**
     * 商品统计
     *
     * @param page         分页
     * @param queryWrapper 查询条件
     * @return 商品统计列表
     */
    @Select("SELECT goods_id,goods_name,SUM(flow_price) AS price,SUM(num) AS num FROM li_order_item_flow ${ew.customSqlSegment}")
    List<GoodsStatisticsDataVO> getGoodsStatisticsData(IPage<GoodsStatisticsDataVO> page,
                                                       @Param(Constants.WRAPPER) Wrapper<GoodsStatisticsDataVO> queryWrapper);
    /**
     * 供应商统计
     *
     * @param page         分页
     * @param queryWrapper 查询条件
     * @return 商品统计列表
     */
    @Select("SELECT goods_id,goods_name,SUM(supplier_settlement_price) AS price,SUM(num) AS num FROM li_order_item_flow ${ew.customSqlSegment}")
    List<GoodsStatisticsDataVO> getGoodsStatisticsDataSupplier(IPage<GoodsStatisticsDataVO> page,
                                                        @Param(Constants.WRAPPER) Wrapper<GoodsStatisticsDataVO> queryWrapper);

    /**
     * 分类统计
     *
     * @param queryWrapper 查询条件
     * @return 分类统计列表
     */
    @Select("SELECT category_id,category_name,SUM(price) AS price,SUM(num) AS num FROM li_order_item_flow ${ew.customSqlSegment}")
    List<CategoryStatisticsDataVO> getCateGoryStatisticsData(@Param(Constants.WRAPPER) Wrapper<CategoryStatisticsDataVO> queryWrapper);


    /**
     * 店铺统计列表
     *
     * @param page         分页
     * @param queryWrapper 查询参数
     * @return 店铺统计列表
     */
    @Select("SELECT store_id AS storeId,store_name AS storeName,SUM(flow_price) AS price,SUM(num) AS num FROM li_order_item_flow ${ew" +
            ".customSqlSegment}")
    List<StoreStatisticsDataVO> getStoreStatisticsData(IPage<GoodsStatisticsDataVO> page,
                                                       @Param(Constants.WRAPPER) Wrapper<GoodsStatisticsDataVO> queryWrapper);

    /**
     * 店铺统计付款人数
     *
     * @param storeId   店铺id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 付款人数
     */
    @Select("SELECT count(0) AS num FROM (SELECT count(0) FROM li_order_item_flow " +
            " where store_id = #{storeId} and is_pay=true and create_time >=#{startTime} and create_time < " +
            "#{endTime}" +
            " GROUP BY user_id) t")
    Long countPayersByStore(String storeId, Date startTime, Date endTime);

    /**
     * 统计付款人数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 付款人数
     */
    @Select("SELECT count(0) AS num FROM (SELECT count(0) FROM li_order_item_flow " +
            " where  is_pay=true and create_time >=#{startTime} and create_time < #{endTime}" +
            " GROUP BY user_id) t")
    Long countPayers(Date startTime, Date endTime);

    @Select("SELECT \n" +
            "sum(flow_price) as flowPrice,\n" +
            "sum(refund_price) as refundPrice,\n" +
            "sum(seller_settlement_price) as sellerSettlementPrice,\n" +
            "sum(supplier_settlement_price) as supplierSettlementPrice,\n" +
            "sum(distributor_settlement_price) as distributorSettlementPrice,\n" +
            "sum(platform_settlement_price) as platformSettlementPrice\n" +
            "from  li_order_flow \n" +
            "${ew.customSqlSegment}")
    OrderFlowStatisticsVO statistics(@Param(Constants.WRAPPER) LambdaQueryWrapper<OrderFlow> orderFlowLambdaQueryWrapper);

    /**
     * 根据店铺获取销量
     *
     * @return 商品统计列表
     */
    @Select("SELECT store_id,,SUM(num) AS num FROM li_order_item_flow WHERE store_id = #{storeId} GROUP BY store_id")
    GoodsStatisticsDataVO getSaleNumByStoreId(String storeId);
}