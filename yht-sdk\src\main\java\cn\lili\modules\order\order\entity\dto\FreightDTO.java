package cn.lili.modules.order.order.entity.dto;

import cn.lili.modules.store.entity.dos.FreightTemplateChild;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class FreightDTO {

    @Schema(title = "运费模板ID")
    private String id;

    @Schema(title = "运费模板名称")
    private String name;

    @Schema(title = "计价方式：按件、按重量", allowableValues = "WEIGHT,NUM,FREE")
    private String pricingMethod;

    @Schema(title = "运费详细规则")
    private List<FreightTemplateChild> freightTemplateChildList;

    @Schema(title = "运费")
    private Double freightPrice;


}
