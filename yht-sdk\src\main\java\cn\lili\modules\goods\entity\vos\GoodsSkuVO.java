package cn.lili.modules.goods.entity.vos;

import cn.hutool.core.bean.BeanUtil;
import cn.lili.modules.goods.entity.dos.GoodsSku;
import cn.lili.modules.search.entity.dos.EsGoodsIndex;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 商品规格VO
 *
 * <AUTHOR>
 * @since 2020-02-26 23:24:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsSkuVO extends GoodsSku {

    @Serial
    private static final long serialVersionUID = -7651149660489332344L;

    @Schema(title = "分类名称")
    private String categoryNamePath;

    @Schema(title = "规格列表")
    private List<SpecValueVO> specList;

    @Schema(title = "商品图片")
    private List<String> goodsGalleryList;

    @Schema(title = "活动库存")
    private Boolean promotionBuy;

    @Schema(title = "活动库存数量")
    private Integer promotionQuantity;

    @Schema(title = "商品发布时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Field(type = FieldType.Date, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date releaseTime;

    public GoodsSkuVO(GoodsSku goodsSku) {
        BeanUtil.copyProperties(goodsSku, this);
    }

    public GoodsSkuVO(EsGoodsIndex esGoodsIndex) {
        BeanUtil.copyProperties(esGoodsIndex, this);
    }
}

