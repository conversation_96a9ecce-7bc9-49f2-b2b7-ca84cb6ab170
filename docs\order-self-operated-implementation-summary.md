# 订单按店铺类型筛选功能实现总结

## 实现方案对比

### 方案一：JOIN 查询（已废弃）
- **思路**: 在订单查询 SQL 中直接 LEFT JOIN `li_store` 表
- **优点**: 一次查询完成，SQL 相对简单
- **缺点**: 
  - 影响现有查询性能
  - 增加 SQL 复杂度
  - 可能影响分页准确性

### 方案二：分步查询（最终采用）
- **思路**: 先查询店铺ID列表，再使用 IN 查询订单
- **优点**: 
  - 不影响现有查询结构
  - 性能可控，可以缓存店铺ID
  - 逻辑清晰，易于维护
- **缺点**: 需要两次数据库查询

## 最终实现架构

```
用户请求 (selfOperated=true)
    ↓
OrderController.queryOrder()
    ↓
OrderService.queryByParams()
    ↓
1. 调用 getStoreIdsBySelfOperated(true)
   ↓
   StoreClient.list(StoreSearchParams{selfOperated=true})
   ↓
   返回直营店铺ID列表: ["store1", "store2", "store3"]
    ↓
2. 在订单查询中添加条件: WHERE o.store_id IN ("store1", "store2", "store3")
    ↓
返回筛选后的订单列表
```

## 核心代码实现

### 1. 订单查询参数扩展
```java
// OrderSearchParams.java
@Schema(title = "店铺类型筛选", description = "true:直营店铺订单, false:非直营店铺订单, null:不筛选")
private Boolean selfOperated;
```

### 2. 店铺查询参数扩展
```java
// StoreSearchParams.java
@Schema(title = "是否自营", description = "true:直营店铺, false:非直营店铺, null:不筛选")
private Boolean selfOperated;

// 在 queryWrapper() 中添加
if (selfOperated != null) {
    wrapper.eq("self_operated", selfOperated);
}
```

### 3. 订单服务实现
```java
// OrderServiceImpl.java
@Override
public Page<OrderSimpleVO> queryByParams(OrderSearchParams orderSearchParams) {
    QueryWrapper queryWrapper = orderSearchParams.queryWrapper();
    
    // 处理店铺类型筛选
    if (orderSearchParams.getSelfOperated() != null) {
        List<String> storeIds = getStoreIdsBySelfOperated(orderSearchParams.getSelfOperated());
        if (CollectionUtils.isEmpty(storeIds)) {
            return new Page<>(orderSearchParams.getPageNumber(), orderSearchParams.getPageSize());
        }
        queryWrapper.in("o.store_id", storeIds);
    }
    
    queryWrapper.groupBy("o.id");
    queryWrapper.orderByDesc("o.id");
    return this.baseMapper.queryByParams(PageUtil.initPage(orderSearchParams), queryWrapper);
}

private List<String> getStoreIdsBySelfOperated(Boolean selfOperated) {
    try {
        StoreSearchParams storeSearchParams = new StoreSearchParams();
        storeSearchParams.setSelfOperated(selfOperated);
        
        List<Store> stores = storeClient.list(storeSearchParams);
        
        return stores.stream()
                .map(Store::getId)
                .filter(Objects::nonNull)
                .toList();
    } catch (Exception e) {
        log.error("根据店铺类型查询店铺ID失败，selfOperated: {}", selfOperated, e);
        return new ArrayList<>();
    }
}
```

## 修改的文件列表

### SDK 层
- `yht-sdk/src/main/java/cn/lili/modules/order/order/entity/dto/OrderSearchParams.java`
- `yht-sdk/src/main/java/cn/lili/modules/store/entity/dto/StoreSearchParams.java`

### Order Service
- `service/order-service/src/main/java/cn/lili/modules/order/order/serviceimpl/OrderServiceImpl.java`

### 测试和文档
- `service/order-service/src/test/java/cn/lili/modules/order/OrderSelfOperatedTest.java`
- `docs/order-self-operated-filter.md`
- `docs/order-api-examples.md`

## 使用示例

### API 调用
```http
# 查询直营店铺订单
GET /order?selfOperated=true&pageNumber=1&pageSize=10

# 查询非直营店铺订单
GET /order?selfOperated=false&pageNumber=1&pageSize=10

# 查询所有订单
GET /order?pageNumber=1&pageSize=10
```

### Java 代码
```java
// 查询直营店铺订单
OrderSearchParams params = new OrderSearchParams();
params.setSelfOperated(true);
params.setPageNumber(1);
params.setPageSize(10);

Page<OrderSimpleVO> result = orderService.queryByParams(params);
```

## 性能优化建议

### 1. 数据库索引
```sql
-- 在店铺表上创建索引
CREATE INDEX idx_store_self_operated ON li_store(self_operated);

-- 在订单表上创建复合索引
CREATE INDEX idx_order_store_id_create_time ON li_order(store_id, create_time);
```

### 2. 缓存策略
```java
// 可以考虑缓存店铺ID列表
@Cacheable(value = "store_ids", key = "#selfOperated")
private List<String> getStoreIdsBySelfOperated(Boolean selfOperated) {
    // ... 实现逻辑
}
```

### 3. 批量查询优化
- 当店铺数量较多时，可以考虑分批查询
- 设置合理的查询超时时间
- 监控查询性能，必要时进行优化

## 测试验证

### 1. 功能测试
- ✅ 查询直营店铺订单
- ✅ 查询非直营店铺订单
- ✅ 查询所有订单（不筛选）
- ✅ 与其他查询条件组合使用

### 2. 边界测试
- ✅ 没有直营店铺时的处理
- ✅ 没有非直营店铺时的处理
- ✅ 店铺服务异常时的降级处理

### 3. 性能测试
- 建议在生产环境中监控查询性能
- 对比筛选前后的查询时间
- 监控店铺查询的频率和耗时

## 扩展性考虑

### 1. 支持更多店铺属性筛选
```java
// 可以轻松扩展其他店铺属性
private Boolean verified;      // 是否认证
private String storeStatus;    // 店铺状态
private String storeTag;       // 店铺标签
```

### 2. 支持复杂的店铺筛选逻辑
```java
// 支持多条件组合
StoreSearchParams storeParams = new StoreSearchParams();
storeParams.setSelfOperated(true);
storeParams.setVerify(true);
storeParams.setStoreStatus("OPEN");
```

### 3. 支持其他业务场景
- 商品按店铺类型筛选
- 售后按店铺类型筛选
- 统计按店铺类型分组

## 总结

通过采用分步查询的方式，成功实现了订单按店铺类型筛选的功能：

1. **架构清晰**: 职责分离，订单服务专注订单查询，店铺查询通过 StoreClient 完成
2. **性能可控**: 避免了复杂的 JOIN 查询，可以针对性优化
3. **扩展性好**: 可以轻松添加更多店铺属性的筛选
4. **向后兼容**: 不影响现有功能，新参数为可选
5. **错误处理**: 完善的异常处理和降级机制

这个实现为平台提供了精细化的订单管理能力，有助于区分直营业务和第三方商家业务的数据分析。
