package cn.lili.modules.statistics.serviceimpl;

import cn.hutool.core.convert.Convert;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.modules.goods.client.GoodsCollectionClient;
import cn.lili.modules.goods.client.GoodsStatisticsClient;
import cn.lili.modules.goods.entity.dto.GoodsNumSearchParams;
import cn.lili.modules.goods.entity.enums.GoodsAuthEnum;
import cn.lili.modules.goods.entity.enums.GoodsMarketEnum;
import cn.lili.modules.member.client.FootPrintClient;
import cn.lili.modules.member.client.MemberEvaluationStatisticsClient;
import cn.lili.modules.member.client.MemberStatisticsClient;
import cn.lili.modules.order.order.client.AfterSaleStatisticsClient;
import cn.lili.modules.order.order.client.OrderComplaintStatisticsClient;
import cn.lili.modules.order.order.client.OrderFlowStatisticsClient;
import cn.lili.modules.order.order.client.OrderStatisticsClient;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderTypeEnum;
import cn.lili.modules.order.trade.entity.enums.AfterSaleTypeEnum;
import cn.lili.modules.promotion.client.SeckillStatisticsClient;
import cn.lili.modules.statistics.entity.dto.GoodsStatisticsQueryParam;
import cn.lili.modules.statistics.entity.dto.StatisticsCountParam;
import cn.lili.modules.statistics.entity.dto.StatisticsQueryParam;
import cn.lili.modules.statistics.entity.enums.SearchTypeEnum;
import cn.lili.modules.statistics.entity.vo.GoodsStatisticsDataVO;
import cn.lili.modules.statistics.entity.vo.IndexNoticeVO;
import cn.lili.modules.statistics.entity.vo.IndexStatisticsVO;
import cn.lili.modules.statistics.entity.vo.PlatformViewVO;
import cn.lili.modules.statistics.entity.vo.StoreIndexStatisticsVO;
import cn.lili.modules.statistics.entity.vo.StoreStatisticsDataVO;
import cn.lili.modules.statistics.service.IndexStatisticsService;
import cn.lili.modules.statistics.service.PlatformViewService;
import cn.lili.modules.store.client.StoreClient;
import cn.lili.modules.store.client.StoreStatisticsClient;
import cn.lili.modules.store.entity.dto.StoreStatisticsSearchParams;

import java.text.NumberFormat;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 首页统计数据业务层实现
 *
 * <AUTHOR>
 * @since 2020/12/15 17:57
 */
@Service
@RequiredArgsConstructor
public class IndexStatisticsServiceImpl implements IndexStatisticsService {

    /**
     * 订单统计
     */
    private final OrderStatisticsClient orderStatisticsClient;
    /**
     * 会员统计
     */
    private final MemberStatisticsClient memberStatisticsClient;
    /**
     * 商品统计
     */
    private final GoodsStatisticsClient goodsStatisticsClient;
    /**
     * 商品统计
     */
    private final OrderFlowStatisticsClient orderFlowStatisticsClient;
    /**
     * 店铺
     */
    private final StoreStatisticsClient storeStatisticsClient;
    /**
     * 店铺
     */
    private final MemberEvaluationStatisticsClient memberEvaluationStatisticsClient;
    /**
     * 售后
     */
    private final AfterSaleStatisticsClient afterSaleStatisticsClient;
    /**
     * 投诉
     */
    private final OrderComplaintStatisticsClient orderComplaintStatisticsClient;
    /**
     * 平台PV统计
     */
    private final PlatformViewService platformViewService;
    /**
     * 秒杀活动
     */
    private final SeckillStatisticsClient seckillStatisticsClient;

    private final GoodsCollectionClient goodsCollectionClient;

    private final FootPrintClient footPrintClient;

    @Override
    public IndexNoticeVO indexNotice(String storeId) {

        IndexNoticeVO indexNoticeVO = new IndexNoticeVO();
        //商品审核
        indexNoticeVO.setGoods(goodsStatisticsClient.goodsNum(new GoodsNumSearchParams(null, GoodsAuthEnum.TOBEAUDITED, storeId)));
        //店铺入驻审核
        indexNoticeVO.setStore(storeStatisticsClient.storeVerifyNum());
        //售后申请
        indexNoticeVO.setRefund(afterSaleStatisticsClient.applyNum(null, storeId));
        //投诉审核
        indexNoticeVO.setComplain(orderComplaintStatisticsClient.waitComplainNum(storeId));
        return indexNoticeVO;
    }

    @Override
    public IndexStatisticsVO indexStatistics() {

        AuthUser authUser = UserContext.getCurrentExistUser();
        if (!SceneEnums.MANAGER.equals(authUser.getScene())) {
            return null;
        }
        //首页统计内容
        IndexStatisticsVO indexStatisticsVO = new IndexStatisticsVO();

        //获取总订单数量
        StatisticsCountParam countParam = new StatisticsCountParam();
        countParam.setOrderType(OrderTypeEnum.NORMAL.name());
        indexStatisticsVO.setOrderNum(orderStatisticsClient.orderNum(countParam));
        //获取总会员数量
        indexStatisticsVO.setMemberNum(memberStatisticsClient.getMemberCount());
        //获取总上架商品数量
        indexStatisticsVO.setGoodsNum(goodsStatisticsClient.goodsNum(new GoodsNumSearchParams(GoodsMarketEnum.UPPER, GoodsAuthEnum.PASS, null)));
        //获取总店铺数量
        indexStatisticsVO.setStoreNum(storeStatisticsClient.storeNum());

        //下单统计
        Map<String, Object> map = orderFlowStatisticsClient.getOrderStatisticsPrice(null);
        //今日下单数
        indexStatisticsVO.setTodayOrderNum(map.get("num") == null ? 0L : Long.parseLong(map.get("num").toString()));
        //今日下单金额
        indexStatisticsVO.setTodayOrderPrice(map.get("price") == null ? 0D : Double.parseDouble(map.get("price").toString()));

        //今日新增会员数量
        indexStatisticsVO.setTodayMemberNum(memberStatisticsClient.todayMemberNum());
        //今日新增商品数量
        indexStatisticsVO.setTodayGoodsNum(goodsStatisticsClient.todayUpperNum());
        //今日新增店铺数量
        indexStatisticsVO.setTodayStoreNum(storeStatisticsClient.todayStoreNum());
        //今日新增评论数量
        indexStatisticsVO.setTodayMemberEvaluation(memberEvaluationStatisticsClient.todayMemberEvaluation());
        //当前在线人数
        indexStatisticsVO.setCurrentNumberPeopleOnline(platformViewService.online());


        //流量统计
        StatisticsQueryParam queryParam = new StatisticsQueryParam();

        //今日uv
        queryParam.setSearchType(SearchTypeEnum.TODAY.name());
        indexStatisticsVO.setTodayUV(platformViewService.countUv(queryParam));

//       昨日访问数UV
        queryParam.setSearchType(SearchTypeEnum.YESTERDAY.name());
        indexStatisticsVO.setYesterdayUV(platformViewService.countUv(queryParam));

//       前七日访问数UV
        queryParam.setSearchType(SearchTypeEnum.LAST_SEVEN.name());
        indexStatisticsVO.setLastSevenUV(platformViewService.countUv(queryParam));

//       三十日访问数UV
        queryParam.setSearchType(SearchTypeEnum.LAST_THIRTY.name());
        indexStatisticsVO.setLastThirtyUV(platformViewService.countUv(queryParam));


        return indexStatisticsVO;
    }

    @Override
    public StoreIndexStatisticsVO storeIndexStatistics() {

        String storeId = UserContext.getCurrentExistUser().getExtendId();
        StoreIndexStatisticsVO storeIndexStatisticsVO = new StoreIndexStatisticsVO();

        //商品总数量
        storeIndexStatisticsVO.setGoodsNum(goodsStatisticsClient.goodsNum(new GoodsNumSearchParams(GoodsMarketEnum.UPPER, GoodsAuthEnum.PASS, storeId)));


        //访问量
        StatisticsQueryParam queryParam = new StatisticsQueryParam();
        queryParam.setSearchType(SearchTypeEnum.TODAY.name());
        queryParam.setStoreId(storeId);
        PlatformViewVO platformViewVO = platformViewService.list(queryParam).getFirst();
        storeIndexStatisticsVO.setStoreUV(platformViewVO.getUvNum().intValue());

        //订单总数量、订单总金额
        Map<String, Object> map = orderFlowStatisticsClient.getOrderStatisticsPrice(storeId);
        storeIndexStatisticsVO.setOrderNum(Convert.toInt(map.get("num").toString()));
        storeIndexStatisticsVO.setOrderPrice(
            map.get("price") != null ? Double.parseDouble(map.get("price").toString()) : 0.0);

        // 统计参数
        StatisticsCountParam countParam = new StatisticsCountParam();
        countParam.setStoreId(storeId);
        countParam.setOrderType("NORMAL");

        //待付款订单数量
        countParam.setOrderStatus(OrderStatusEnum.UNPAID.name());
        storeIndexStatisticsVO.setUnPaidOrder(orderStatisticsClient.orderNum(countParam));
        //待发货订单数量
        countParam.setOrderStatus(OrderStatusEnum.UNDELIVERED.name());
        storeIndexStatisticsVO.setUnDeliveredOrder(orderStatisticsClient.orderNum(countParam));
        //待收货订单数量
        countParam.setOrderStatus(OrderStatusEnum.DELIVERED.name());
        storeIndexStatisticsVO.setDeliveredOrder(orderStatisticsClient.orderNum(countParam));
        //待支付供应商订单数量
        countParam.setOrderStatus(OrderStatusEnum.PROCURE_UNPAID.name());
        storeIndexStatisticsVO.setProcureUnpaid(orderStatisticsClient.orderNum(countParam));
        //待处理退货数量
        storeIndexStatisticsVO.setReturnGoods(afterSaleStatisticsClient.applyNum(AfterSaleTypeEnum.RETURN_GOODS.name(), storeId));
        //待处理退款数量
        storeIndexStatisticsVO.setReturnMoney(afterSaleStatisticsClient.applyNum(AfterSaleTypeEnum.RETURN_MONEY.name(), storeId));
        //待回复评价数量
        storeIndexStatisticsVO.setMemberEvaluation(memberEvaluationStatisticsClient.getWaitReplyNum(storeId));
        //待处理投诉数量
        storeIndexStatisticsVO.setComplaint(orderComplaintStatisticsClient.waitComplainNum(storeId));

        //待上架商品数量
        storeIndexStatisticsVO.setWaitUpper(goodsStatisticsClient.goodsNum(new GoodsNumSearchParams(GoodsMarketEnum.DOWN, null, storeId)));
        //待审核商品数量
        storeIndexStatisticsVO.setWaitAuth(goodsStatisticsClient.goodsNum(new GoodsNumSearchParams(null, GoodsAuthEnum.TOBEAUDITED, storeId)));

        //可参与秒杀活动数量
        storeIndexStatisticsVO.setSeckillNum(seckillStatisticsClient.getApplyNum());

        //图片下载率统计
        //图片访问数
        long visitNum = footPrintClient.getVisitNumByStore(storeId);
        storeIndexStatisticsVO.setImageDownloadRate(0d);
        long downloadCount =  goodsStatisticsClient.getImageDownloadCount(storeId);

        storeIndexStatisticsVO.setImageDownloadCount(downloadCount);
//         if (downloadCount > 0 ) {
// //            Long sun = downloadCount + visitNum;
//             NumberFormat numberFormat = NumberFormat.getInstance();
//             numberFormat.setMaximumFractionDigits(2);
//             String result1 = numberFormat.format((float) downloadCount / (float) visitNum * 100);
//             storeIndexStatisticsVO.setImageDownloadRate(Double.valueOf(result1));
//         }

        //店铺关注数
        storeIndexStatisticsVO.setStoreCollectionNum(storeStatisticsClient.storeCollectionNum(storeId));

        //商品收藏数
        storeIndexStatisticsVO.setGoodsCollectionNum(goodsCollectionClient.countGoodsCollectNum(storeId));
        return storeIndexStatisticsVO;
    }

    @Override
    public List<GoodsStatisticsDataVO> goodsStatistics(GoodsStatisticsQueryParam statisticsQueryParam) {
        //查询商品
        return orderFlowStatisticsClient.getGoodsStatisticsData(statisticsQueryParam, 10);
    }

    @Override
    public List<StoreStatisticsDataVO> storeStatistics(StatisticsQueryParam statisticsQueryParam) {

        StoreStatisticsSearchParams searchParams = new StoreStatisticsSearchParams(statisticsQueryParam);

        return orderFlowStatisticsClient.getStoreStatisticsData(searchParams);
    }
}
