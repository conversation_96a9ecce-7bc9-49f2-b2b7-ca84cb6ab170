package cn.lili.controller.feign;

import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.CurrencyUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.goods.client.GoodsClient;
import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.goods.entity.dos.GoodsSku;
import cn.lili.modules.goods.entity.dto.BatchUpdateGoodsDTO;
import cn.lili.modules.goods.entity.dto.GoodsCompleteMessage;
import cn.lili.modules.goods.entity.dto.GoodsSearchParams;
import cn.lili.modules.goods.entity.dto.ProxyGoodsOperationDTO;
import cn.lili.modules.goods.entity.enums.SalesModeEnum;
import cn.lili.modules.goods.integration.GoodsIntegrationService;
import cn.lili.modules.goods.service.GoodsGalleryService;
import cn.lili.modules.goods.service.GoodsService;
import cn.lili.modules.goods.service.GoodsSkuService;
import cn.lili.modules.store.client.FreightTemplateClient;
import cn.lili.modules.store.client.StoreClient;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.params.StoreRankStatisticsParams;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import cn.lili.modules.supplier.entity.dto.SupplierGoodsOperationDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商品分类 feign client
 *
 * <AUTHOR>
 * @version v1.0
 * 2021-12-07 06:08
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class GoodsFeignController implements GoodsClient {

    private final GoodsService goodsService;

    private final GoodsSkuService goodsSkuService;

    private final GoodsIntegrationService goodsIntegrationService;

    private final FreightTemplateClient freightTemplateClient;

    @Override
    public void addSupplierGoods(SupplierGoodsOperationDTO supplierGoodsOperationDTO) {
        goodsIntegrationService.saveSupplierGoods(supplierGoodsOperationDTO);
    }

    @Override
    public void editSupplierGoods(SupplierGoodsOperationDTO supplierGoodsOperationDTO) {
        goodsIntegrationService.updateSupplierGoods(
                supplierGoodsOperationDTO);
    }
    @Override
    public GoodsSku getGoodsSkuByIdFromCache(String skuId) {
        return goodsIntegrationService.getGoodsSkuByIdFromCache(skuId);
    }

    @Override
    public GoodsSku getGoodsSkuById(String skuId) {
        return goodsSkuService.getById(skuId);
    }

    @Override
    public GoodsSku getCanPromotionGoodsSkuByIdFromEs(String skuId) {
        GoodsSku goodsSku = goodsIntegrationService.getGoodsSkuByIdFromCache(skuId);
        // 如果商品不是零售商品，则不允许参与促销
        if (goodsSku != null && !SalesModeEnum.RETAIL.name().equals(goodsSku.getSalesModel())) {
            throw new ServiceException(ResultCode.PROMOTION_GOODS_CAN_NOT_JOIN, goodsSku.getGoodsName());
        }
        return goodsSku;
    }

    @Override
    public void updateStoreDetail(Store store) {
        goodsIntegrationService.updateStoreDetail(store);
    }

    @Override
    public void batchUpdateGoods(BatchUpdateGoodsDTO batchUpdateGoodsDTO) {
        goodsIntegrationService.batchUpdateGoods(batchUpdateGoodsDTO);
    }


    @Override
    public Long count(String storeId) {
        return goodsService.countStoreGoodsNum(storeId);
    }

    @Override
    public Integer getStock(String skuId) {
        return goodsSkuService.getStock(skuId);
    }

    /**
     * 添加商品评价数量
     *
     * @param commentNum 评价数量
     * @param goodsId    商品ID
     */
    @Override
    public void addGoodsCommentNum(Integer commentNum, String goodsId) {
        goodsService.addGoodsCommentNum(commentNum, goodsId);
    }

    @Override
    public Goods getById(String goodsId) {
        return goodsService.getById(goodsId);
    }


    @Override
    public List<Goods> queryListByParams(GoodsSearchParams searchParams) {
        return goodsService.queryListByParams(searchParams);
    }


    @Override
    public List<GoodsSku> getGoodsSkuByList(GoodsSearchParams searchParams) {
        return goodsSkuService.getGoodsSkuByList(searchParams);
    }

    @Override
    public void updateGoodsBuyCount(List<GoodsCompleteMessage> goodsCompleteMessageList) {
        goodsService.updateGoodsBuyCount(goodsCompleteMessageList);
    }


    @Override
    public void updateGoodsSku(GoodsSku goodsSku) {
        goodsSkuService.update(goodsSku);
    }

    @Override
    public Goods getGoodsByParams(GoodsSearchParams searchParams) {
        return goodsService.getGoodsByParams(searchParams);
    }


    @Override
    public Long countSkuNum(String storeId) {
        return goodsSkuService.countSkuNum(storeId);
    }

    @Override
    public void deleteGoods(List<String> goodsIds) {
        goodsIntegrationService.deleteGoods(goodsIds);
    }

    @Override
    public void deleteSupplierGoods(List<String> goodsIds) {
        goodsIntegrationService.deleteSupplierGoods(goodsIds);
    }

    @Override
    @Transactional
    public void addSupplierGoods(String goodsId) {
        goodsIntegrationService.saveProxyGoods(goodsId);
    }

    @Override
    public void syncStock(List<String> goodsId) {
        goodsId.forEach(goodsService::syncStock);
    }

    @Override
    public void editProxyGoods(ProxyGoodsOperationDTO proxyGoodsOperationDTO) {
        goodsIntegrationService.editProxyGoods(proxyGoodsOperationDTO);
    }

    @Override
    public void syncGoodsSkuCommentCount(String skuId) {
        goodsIntegrationService.syncGoodsSkuCommentCount(skuId);
    }

    @Override
    public Page<GoodsSku> getGoodsSkuByPage(GoodsSearchParams searchParams) {
        return goodsSkuService.getGoodsSkuByPage(searchParams);
    }

    @Override
    public FreightTemplateVO getGoodsTemplate(String goodsId) {
        Goods supplierGoods = goodsService.getById(goodsId);
        return freightTemplateClient.getFreightTemplate(supplierGoods.getTemplateId());
    }




    //代理价格计算
    private double agentPriceCalculation(Double costPrice, Integer agencyRate){
        //使用BigDecimal计算防止第一步精度丢失
        BigDecimal molecular  = new BigDecimal(costPrice);
        BigDecimal percentage = new BigDecimal(agencyRate);
        BigDecimal hundred = new BigDecimal("100");
        BigDecimal result = molecular .multiply(percentage).divide(hundred, 2, RoundingMode.HALF_UP);
        Double price = CurrencyUtil.add(costPrice, result.doubleValue());
        //计算的金额
        Double amounts;
        //小于10不进行处理。
        if(price <= 10D){
            amounts = price;
        }else{
            //先进行第一次取整(去除小数)处理
            amounts = Math.ceil(price);
            //数据取整之后，计算需要向上取整的位数
            double denominator = Math.pow(10, amounts.toString().length() - 4);
            //金额向上取整
            amounts = CurrencyUtil.mul(Math.ceil(CurrencyUtil.div(amounts, denominator)), denominator);

        }
        return amounts;
    }

    /**
     * 批量从redis中获取商品SKU信息
     *
     * @param ids SkuId集合
     * @return 商品SKU信息集合
     */
    @Override
    public List<GoodsSku> getGoodsSkuByIdFromCache(List<String> ids) {
        return goodsIntegrationService.getGoodsSkuByIdFromCache(ids);
    }

    @Override
    public List<String> getAllSkuIds() {
        return goodsSkuService.getGoodsSkuIdListByGoodsId(new GoodsSearchParams());
    }
    
    /**
     * 获取商品SKU的成本价
     *
     * @param skuId 商品SKU ID
     * @return 商品成本价
     */
    @Override
    public Double getSkuCostPrice(String skuId) {
        GoodsSku goodsSku = goodsSkuService.getById(skuId);
        return goodsSku != null ? goodsSku.getCost() : null;
    }

    @Override
    public Page<StoreRankStatisticsVO> getGoodsImageDownloadCountByStore (StoreRankStatisticsParams params) {
        return goodsService.getGoodsImageDownloadCountByStore(params);
    }

    @Override
    public List<Goods> countGoodsNum(String storeId) {
        return goodsService.countGoodsNum(storeId);
    }

    @Override
    public Double getRealShootRate(String storeId) {
        return goodsService.getRealShootRate(storeId);
    }

    @Override
    public Double getQualityPassRate(String storeId) {
        return goodsService.getQualityPassRate(storeId);
    }

    @Override
    public void incrementVisitorCount(String goodsId) {
        goodsService.incrementVisitorCount(goodsId);
    }

    @Override
    public Page<Goods> getGoodsVisitorRanking(GoodsSearchParams params) {
        return goodsService.getGoodsVisitorRanking(params);
    }

    @Override
    public Page<Goods> getGoodsImageDownloadRanking(GoodsSearchParams params) {
        return goodsService.getGoodsImageDownloadRanking(params);
    }

}
