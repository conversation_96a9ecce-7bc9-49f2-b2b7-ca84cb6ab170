-- 为商品表添加下架时间字段
ALTER TABLE li_goods ADD COLUMN off_shelf_time DATETIME NULL COMMENT '下架时间';

-- 为现有的下架商品设置下架时间（使用更新时间作为下架时间）
UPDATE li_goods 
SET off_shelf_time = update_time 
WHERE market_enable = 'DOWN' 
  AND delete_flag = 0 
  AND off_shelf_time IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX idx_goods_off_shelf_time ON li_goods(off_shelf_time);
CREATE INDEX idx_goods_market_enable_off_shelf_time ON li_goods(market_enable, off_shelf_time);
CREATE INDEX idx_goods_store_market_off_shelf ON li_goods(store_id, market_enable, off_shelf_time);
