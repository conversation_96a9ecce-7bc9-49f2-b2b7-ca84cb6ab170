package cn.lili.controller.user;

import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.ResultUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.dto.StoreApplyDTO;
import cn.lili.modules.store.entity.dto.StoreEditDTO;
import cn.lili.modules.store.entity.dto.StoreOwnerEditDTO;
import cn.lili.modules.store.entity.dto.StoreSearchParams;
import cn.lili.modules.store.entity.enums.StoreStatusEnum;
import cn.lili.modules.store.entity.vos.StoreLicenceVO;
import cn.lili.modules.store.entity.vos.StoreManagementCategoryVO;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import cn.lili.modules.store.entity.vos.StoreVO;
import cn.lili.modules.store.service.StoreService;
import cn.lili.modules.system.entity.enums.SettingEnum;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 店铺管理接口
 *
 * <AUTHOR>
 * @since 2020/12/6 16:09
 */
@Tag(name = "店铺管理接口")
@RestController
@RequestMapping("/user/store")
@RequiredArgsConstructor
@Validated
public class StoreController {

    /**
     * 店铺
     */
    private final StoreService storeService;


    @Operation(summary = "添加店铺")
    @PostMapping(value = "/add")
    public ResultMessage<Store> add(@RequestBody StoreApplyDTO storeApplyDTO) {
        return ResultUtil.data(storeService.add(storeApplyDTO));
    }

    @Operation(summary = "编辑店铺")
    @PutMapping(value = "/{id}")
    public ResultMessage<Store> edit(@PathVariable String id, StoreEditDTO storeEditDTO) {
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        if (SceneEnums.MANAGER.equals(currentUser.getScene())) {
            storeEditDTO.setStoreId(id);
        } else {
            storeEditDTO.setStoreId(currentUser.getExtendId());
        }
        return ResultUtil.data(storeService.edit(storeEditDTO));
    }


    @Operation(summary = "获取自己店铺信息")
    @GetMapping(value = "/owner")
    public ResultMessage<StoreVO> get() {
        StoreVO storeDetail = storeService.getOwnerStoreDetail();
        //获取当前登录商家内容
        return ResultUtil.data(storeDetail);
    }

    @Operation(summary = "店铺自行编辑店铺")
    @PutMapping(value = "/owner/edit")
    public ResultMessage<Store> ownerEdit(@RequestBody StoreOwnerEditDTO storeEditDTO) {
        storeService.ownerEdit(storeEditDTO);
        return ResultUtil.success();
    }

    @Operation(summary = "获取店铺分页列表")
    @GetMapping
    public ResultMessage<Page<StoreVO>> getByPage(StoreSearchParams searchParams, PageVO page) {
        return ResultUtil.data(storeService.findByConditionPage(searchParams, page));
    }

    @Operation(summary = "根据查询类型获取店铺列表", description = "1-推荐, 2-上新, 3-实拍, 4-发货快, 5-热销")
    @GetMapping("/type/{queryType}")
    public ResultMessage<Page<StoreVO>> getByQueryType(
            @PathVariable Integer queryType,
            PageVO page,
            @RequestParam(required = false) String storeName,
            @RequestParam(required = false) String storeStatus) {

        StoreSearchParams searchParams = new StoreSearchParams();
        searchParams.setQueryType(queryType);
        searchParams.setStoreName(storeName);
        searchParams.setStoreStatus(storeStatus);

        return ResultUtil.data(storeService.findByConditionPage(searchParams, page));
    }

    @Operation(summary = "获取店铺详情")
    @GetMapping(value = "/{storeId}")
    public ResultMessage<StoreVO> detail(@PathVariable String storeId) {
        return ResultUtil.data(storeService.getStoreDetailVO(storeId));
    }

    @Operation(summary = "店铺编辑设置")
    @PutMapping(value = "/owner/edit/{key}")
    public ResultMessage<Store> ownerEditPointSetting(@PathVariable String key, @RequestBody String configValue) {
        storeService.editStoreSetting(key, configValue);
        return ResultUtil.success();
    }

    @Operation(summary = "编辑店铺状态")
    @PutMapping(value = "/status/{scene}/{id}")
    public ResultMessage<Boolean> updateStoreStatus(@PathVariable String id, @PathVariable String scene, @RequestParam String status) {
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        if (SceneEnums.MANAGER.equals(currentUser.getScene())) {
            boolean result = this.storeService.updateStoreStatus(Collections.singletonList(id), StoreStatusEnum.valueOf(status), SceneEnums.valueOf(scene));
            return ResultUtil.data(result);
        }
        return ResultUtil.success();
    }

    @Operation(summary = "查询一级分类列表")
    @GetMapping(value = "/managementCategory/{storeId}")
    public ResultMessage<List<StoreManagementCategoryVO>> firstCategory(@PathVariable String storeId) {
        return ResultUtil.data(this.storeService.goodsManagementCategory(storeId));
    }


    @Operation(summary = "通过id获取店铺详细信息-营业执照")
    @GetMapping(value = "/{id}/licence")
    public ResultMessage<StoreLicenceVO> licencePhoto(@NotNull @PathVariable String id) {
        return ResultUtil.data(storeService.getStoreLicenceVO(id));
    }


    @Operation(summary = "通过域名获取店铺信息")
    @GetMapping("/domain/info")
    public ResultMessage<String> getStoreInfo() {
        return ResultUtil.data(storeService.getDomainStore());
    }

    @Operation(summary = "编辑店铺域名信息")
    @PutMapping("/domain")
    public ResultMessage<Object> editStoreDomain(String storeId, String pcDomain, String moveDomain) {
        storeService.editDomain(storeId, pcDomain, moveDomain);
        return ResultUtil.success();
    }

    @Operation(summary = "设置店铺标签")
    @PutMapping("/{storeId}/tag")
    public ResultMessage<Object> setStoreTag(@PathVariable String storeId, @RequestParam String storeTag) {
        storeService.setStoreTag(storeId, storeTag);
        return ResultUtil.success();
    }

    @Operation(summary = "设置店铺实拍状态")
    @PutMapping("/{storeId}/realPhoto")
    public ResultMessage<Object> setStoreRealPhoto(@PathVariable String storeId, @RequestParam Boolean realPhoto) {
        storeService.setStoreRealPhoto(storeId, realPhoto);
        return ResultUtil.success();
    }

    @Operation(summary = "设置店铺发货快状态")
    @PutMapping("/{storeId}/fastDelivery")
    public ResultMessage<Object> setStoreFastDelivery(@PathVariable String storeId, @RequestParam Boolean fastDelivery) {
        storeService.setStoreFastDelivery(storeId, fastDelivery);
        return ResultUtil.success();
    }

    @Operation(summary = "店铺排行统计")
    @GetMapping("/ranking/rankStatistics")
    public ResultMessage<Page<StoreRankStatisticsVO>> getStoreRankStatistics(PageVO page,
                                                                         @RequestParam(value = "storeName", required = false) String storeName,
                                                                         @RequestParam(value = "sortField", defaultValue = "pv") String sortField) {

        return ResultUtil.data(storeService.getStoreRankStatistics(page, storeName, sortField));
    }

    @Operation(summary = "获取店铺详情")
    @GetMapping("/getStoreById/{storeId}")
    public ResultMessage<StoreVO> getStoreDetailById(@PathVariable String storeId) {
        return ResultUtil.data(storeService.getStoreDetailById(storeId));
    }

}
