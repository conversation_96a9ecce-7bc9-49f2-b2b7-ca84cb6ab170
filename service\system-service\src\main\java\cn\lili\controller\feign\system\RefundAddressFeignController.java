package cn.lili.controller.feign.system;

import cn.lili.modules.system.client.RefundAddressClient;
import cn.lili.modules.system.entity.dos.RefundAddress;
import cn.lili.modules.system.service.RefundAddressService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/1/13
 **/
@RestController
@RequiredArgsConstructor
public class RefundAddressFeignController implements RefundAddressClient {

    private final RefundAddressService refundAddressService;

    @Override
    public RefundAddress getRefundAddress(String refundAddressId) {
        return this.refundAddressService.getRefundAddress(refundAddressId);
    }

    @Override
    public RefundAddress getRefundAddressDefault() {
        return this.refundAddressService.getDefault();
    }
}
