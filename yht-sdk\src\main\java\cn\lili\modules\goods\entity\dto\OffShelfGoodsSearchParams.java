package cn.lili.modules.goods.entity.dto;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.goods.entity.enums.GoodsMarketEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.util.Date;

/**
 * 下架商品查询参数
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OffShelfGoodsSearchParams extends PageVO {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(title = "商家ID")
    private String storeId;

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "分类path")
    private String categoryPath;

    @Schema(title = "价格范围，如10_1000")
    private String price;

    @Schema(title = "下架时间开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date offShelfTimeStart;

    @Schema(title = "下架时间结束")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date offShelfTimeEnd;

    @Schema(title = "排序类型：offShelfTime-下架时间，price-价格，comprehensive-综合排序")
    private String sortType;

    @Schema(title = "排序方向：asc-升序，desc-降序")
    private String sortDirection;

    public <T> QueryWrapper<T> queryWrapper() {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        
        // 只查询下架商品
        queryWrapper.eq("market_enable", GoodsMarketEnum.DOWN.name());
        
        // 只查询未删除的商品
        queryWrapper.eq("delete_flag", false);
        
        // 商家ID
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(storeId), "store_id", storeId);
        
        // 商品名称模糊查询
        queryWrapper.like(CharSequenceUtil.isNotEmpty(goodsName), "goods_name", goodsName);
        
        // 分类查询
        queryWrapper.like(CharSequenceUtil.isNotEmpty(categoryPath), "category_path", categoryPath);
        
        // 价格范围查询
        this.priceRangeWrapper(queryWrapper);
        
        // 下架时间范围查询
        this.dateRangeWrapper(queryWrapper);
        
        // 排序
        this.orderByWrapper(queryWrapper);
        
        return queryWrapper;
    }

    /**
     * 价格范围查询条件
     */
    private <T> void priceRangeWrapper(QueryWrapper<T> queryWrapper) {
        if (CharSequenceUtil.isNotEmpty(price)) {
            String[] s = price.split("_");
            if (s.length > 1) {
                queryWrapper.between("price", s[0], s[1]);
            } else {
                queryWrapper.ge("price", s[0]);
            }
        }
    }

    /**
     * 日期范围查询条件
     */
    private <T> void dateRangeWrapper(QueryWrapper<T> queryWrapper) {
        if (offShelfTimeStart != null) {
            queryWrapper.ge("off_shelf_time", offShelfTimeStart);
        }
        if (offShelfTimeEnd != null) {
            queryWrapper.le("off_shelf_time", offShelfTimeEnd);
        }
    }

    /**
     * 排序条件
     */
    private <T> void orderByWrapper(QueryWrapper<T> queryWrapper) {
        boolean isAsc = "asc".equalsIgnoreCase(sortDirection);
        
        if (CharSequenceUtil.isNotEmpty(sortType)) {
            switch (sortType) {
                case "offShelfTime":
                    queryWrapper.orderBy(true, isAsc, "off_shelf_time");
                    break;
                case "price":
                    queryWrapper.orderBy(true, isAsc, "price");
                    break;
                case "comprehensive":
                    // 综合排序：按评分、销量、下架时间排序
                    queryWrapper.orderBy(true, false, "grade");
                    queryWrapper.orderBy(true, false, "buy_count");
                    queryWrapper.orderBy(true, false, "off_shelf_time");
                    break;
                default:
                    queryWrapper.orderBy(true, false, "off_shelf_time");
                    break;
            }
        } else {
            queryWrapper.orderBy(true, false, "off_shelf_time");
        }
    }
}
