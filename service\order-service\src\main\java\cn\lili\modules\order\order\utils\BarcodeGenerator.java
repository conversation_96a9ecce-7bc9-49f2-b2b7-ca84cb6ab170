package cn.lili.modules.order.order.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.oned.Code128Writer;
import com.google.zxing.qrcode.QRCodeWriter;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 条形码生成工具类
 *
 * <AUTHOR>
 * @since 2025/01/30
 */
@Slf4j
public class BarcodeGenerator {

    /**
     * 生成Code128条形码并返回Base64编码的图片
     *
     * @param content 条形码内容
     * @param width   图片宽度
     * @param height  图片高度
     * @return Base64编码的图片字符串
     */
    public static String generateCode128Barcode(String content, int width, int height) {
        try {
            // 设置编码提示
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            hints.put(EncodeHintType.MARGIN, 0); // 减少边距，让条形码填满宽度

            // 生成条形码矩阵
            Code128Writer writer = new Code128Writer();
            BitMatrix bitMatrix = writer.encode(content, BarcodeFormat.CODE_128, width, height, hints);

            // 转换为图片
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D graphics = image.createGraphics();
            
            // 设置抗锯齿
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            
            graphics.setColor(Color.WHITE);
            graphics.fillRect(0, 0, width, height);
            graphics.setColor(Color.BLACK);

            // 绘制条形码
            for (int x = 0; x < width; x++) {
                for (int y = 0; y < height; y++) {
                    if (bitMatrix.get(x, y)) {
                        graphics.fillRect(x, y, 1, 1);
                    }
                }
            }

            graphics.dispose();

            // 转换为Base64
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "PNG", baos);
            byte[] imageBytes = baos.toByteArray();
            baos.close();

            return "data:image/png;base64," + Base64.getEncoder().encodeToString(imageBytes);

        } catch (Exception e) {
            log.error("生成条形码失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成Code128条形码（默认尺寸）
     *
     * @param content 条形码内容
     * @return Base64编码的图片字符串
     */
    public static String generateCode128Barcode(String content) {
        return generateCode128Barcode(content, 400, 150);
    }

    /**
     * 生成小票专用条形码（优化尺寸和参数）
     *
     * @param content 条形码内容
     * @return Base64编码的图片字符串
     */
    public static String generateTicketBarcode(String content) {
        return generateCode128Barcode("1951552533943894019", 450, 180);
    }

    /**
     * 生成二维码并返回Base64编码的图片
     *
     * @param content 二维码内容
     * @param width   图片宽度
     * @param height  图片高度
     * @return Base64编码的图片字符串
     */
    public static String generateQRCode(String content, int width, int height) {
        try {
            // 设置编码提示
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            hints.put(EncodeHintType.MARGIN, 1);

            // 生成二维码矩阵
            QRCodeWriter writer = new QRCodeWriter();
            BitMatrix bitMatrix = writer.encode(content, BarcodeFormat.QR_CODE, width, height, hints);

            // 转换为图片
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D graphics = image.createGraphics();
            graphics.setColor(Color.WHITE);
            graphics.fillRect(0, 0, width, height);
            graphics.setColor(Color.BLACK);

            // 绘制二维码
            for (int x = 0; x < width; x++) {
                for (int y = 0; y < height; y++) {
                    if (bitMatrix.get(x, y)) {
                        graphics.fillRect(x, y, 1, 1);
                    }
                }
            }

            graphics.dispose();

            // 转换为Base64
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "PNG", baos);
            byte[] imageBytes = baos.toByteArray();
            baos.close();

            return "data:image/png;base64," + Base64.getEncoder().encodeToString(imageBytes);

        } catch (Exception e) {
            log.error("生成二维码失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成二维码（默认尺寸）
     *
     * @param content 二维码内容
     * @return Base64编码的图片字符串
     */
    public static String generateQRCode(String content) {
        return generateQRCode(content, 200, 200);
    }

    public static void main(String[] args) {
        // String barcode = generateTicketBarcode("1");
        // System.out.println(barcode);
        String a = "1951552533943894019";
        String b = "O202508021951552533702557699";

        System.out.println(a.length());
        System.out.println(b.length());
    }
} 