package cn.lili.modules.store.entity.dos;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.utils.BeanUtil;
import cn.lili.common.utils.SnowFlake;
import cn.lili.modules.member.entity.dos.User;
import cn.lili.modules.store.entity.dto.StoreApplyDTO;
import cn.lili.modules.store.entity.enums.StoreStatusEnum;
import cn.lili.mybatis.model.BaseSceneEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serial;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 店铺
 *
 * <AUTHOR>
 * @since 2020-02-18 15:18:56
 */
@Data
@TableName("li_store")
@Schema(title = "店铺")
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class Store extends BaseSceneEntity {

    @Serial
    private static final long serialVersionUID = -5861767726387892272L;


    @Schema(title = "店铺名称")
    private String storeName;

    /**
     * @see StoreStatusEnum
     */
    @Schema(title = "店铺状态")
    private String storeStatus;

    @Schema(title = "是否自营")
    private Boolean selfOperated;

    @Schema(title = "店长id")
    private String managerId;

    @Schema(title = "店铺logo")
    private String storeLogo;

    @Schema(title = "经纬度")
    private String storeCenter;

    @Size(min = 6, max = 200, message = "店铺简介需在6-200字符之间")
    @NotBlank(message = "店铺简介不能为空")
    @Schema(title = "店铺简介")
    private String storeDesc;

    @Schema(title = "描述评分")
    private Double descriptionScore;

    @Schema(title = "服务评分")
    private Double serviceScore;

    @Schema(title = "物流描述")
    private Double deliveryScore;

    @Schema(title = "商品数量")
    private Integer goodsNum;

    @Schema(title = "收藏数量")
    private Integer collectionNum;

    @NotBlank(message = "店铺经营类目不能为空")
    @Schema(title = "店铺经营类目")
    private String businessCategory;

    @Schema(title = "库存预警数量")
    private Integer stockWarning;

    @Schema(title = "开启楼层装修")
    private Boolean enableDecoration = false;

    @Schema(title = "店铺认证状态")
    private Boolean verify = false;

    @Schema(title = "开启自提")
    private Boolean enablePickup = false;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "店铺关闭时间")
    private Date storeEndTime;

    @Schema(title = "icp")
    private String icp;

    @Schema(title = "公司名称")
    private String companyName;

    @Schema(title = "代理利率")
    private Integer agencyRate;

    @Schema(title = "PC端域名")
    private String pcDomain;

    @Schema(title = "移动端域名")
    private String moveDomain;

    @Schema(title = "积分设置")
    private String pointSetting;

    @Schema(title = "经验值设置")
    private String experienceSetting;

    @Schema(title = "会员设置")
    private String premiumMemberSetting;

    @Schema(title = "省")
    private String province;

    @Schema(title = "市")
    private String city;

    @Schema(title = "区")
    private String district;

    @Schema(title = "市场")
    private String market;

    @Schema(title = "市场详细地址")
    private String marketDetail;

    @Schema(title = "店铺标签")
    private String storeTag;

    @Schema(title = "是否实拍商品")
    private Boolean realPhoto = false;

    @Schema(title = "是否发货快")
    private Boolean fastDelivery = false;

    @Schema(title = "直营店铺-是否允许购买商品")
    private Boolean isBuyGoods;

    @Schema(title = "是否支持退现")
    private Boolean isBackMoney;

    @Schema(title = "联系电话，多个之间用英文逗号分隔")
    private String storePhone;

    @Schema(title = "微信号，多个之间用英文逗号分隔")
    private String storeWx;

    @Schema(title = "qq号，多个之间用英文逗号分隔")
    private String storeQq;

    @Schema(title = "旺旺号，多个之间用英文逗号分隔")
    private String storeWw;

    @Schema(title = "商品实拍率")
    private Double goodsRealRate = 100D;

    @Schema(title = "准时发货率")
    private Double onTimeDeliveryRate = 100D;

    @Schema(title = "退货成功率")
    private Double returnSuccessRate = 100D;

    @Schema(title = "质量合格率")
    private Double qualityConformanceRate = 100D;

    @Schema(title = "店铺销量")
    private Integer saleNum = 0;

    public Integer getAgencyRate() {
        if (agencyRate == null) {
            return 10;
        }
        return agencyRate;
    }


    public Store(StoreApplyDTO storeApplyDTO) {
        BeanUtil.copyProperties(storeApplyDTO, this);

        setBusinessCategoryArray(storeApplyDTO.getBusinessCategory());

        this.stockWarning = 10;
        storeStatus = StoreStatusEnum.OPEN.value();
        selfOperated = true;
        deliveryScore = 5.0;
        serviceScore = 5.0;
        descriptionScore = 5.0;
        goodsNum = 0;
        collectionNum = 0;
        storeDesc = CharSequenceUtil.isBlank(storeApplyDTO.getStoreDesc()) ? "这个人很懒，什么都没有留下" : storeApplyDTO.getStoreDesc();
        if (storeName == null) {
            storeName = "小店" + SnowFlake.getIdStr();
        }

        if (CharSequenceUtil.isEmpty(getId())) {
            this.setId(SnowFlake.getIdStr());
            this.setExtendId(this.getId());
        }
    }

    public Store(User user) {
        setBusinessCategoryArray(new String[]{});

        this.stockWarning = 10;
        storeStatus = StoreStatusEnum.OPEN.value();
        selfOperated = false;
        deliveryScore = 5.0;
        serviceScore = 5.0;
        descriptionScore = 5.0;
        goodsNum = 0;
        collectionNum = 0;
        storeName = user.getId() + "的小店";
        storeLogo = "https://huiyifang-test.oss-cn-shenzhen.aliyuncs.com/yht.png";

        storeDesc = "这个人很懒，什么都没有留下";

        this.setScene(user.getScene());
        managerId = user.getId();

        if (CharSequenceUtil.isEmpty(getId())) {
            this.setId(SnowFlake.getIdStr());
            this.setExtendId(this.getId());
        }
    }

    public void setBusinessCategoryArray(String[] businessCategory) {
        if (businessCategory != null && businessCategory.length > 0) {
            this.businessCategory = CharSequenceUtil.join(",", businessCategory);
        }
    }

}