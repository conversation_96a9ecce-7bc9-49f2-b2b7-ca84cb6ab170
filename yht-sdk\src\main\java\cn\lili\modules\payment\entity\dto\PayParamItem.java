package cn.lili.modules.payment.entity.dto;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.io.Serializable;

/**
 * 支付子参数
 *
 * <AUTHOR>
 * @since 2020/12/19 11:46
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayParamItem implements Serializable {


    @NotNull
    @Schema(title = "支付单号")
    private String sn;

    @Schema(title = "收款人：可以是店铺id、供应商id、分销商id等")
    private String payeeId;

    @Schema(title = "描述")
    private String description;

    @Schema(title = "订单金额")
    private Double price;


    @Schema(title = "是否为特殊订单：特殊订单为某一端结算金额为负数需额外逻辑完善")
    private Boolean specialOrder;

    @Schema(title = "商户id:第三方支付发起交易号: 调用方无需传递，支付业务会进行填充")
    private String mchId;

    @Schema(title = "微信appid: 调用方无需传递，支付业务会进行填充")
    private String wxAppid;

    @Schema(title = "第三方支付发起交易号: 调用方无需传递，支付业务会进行填充")
    private String outTradeNo;

    @Schema(title = "补贴金额")
    private Double subsidyAmount;

    @Schema(title = "服务费")
    private Double serviceFee = 0D;

    public String getSn() {
        if (CharSequenceUtil.isEmpty(sn)) {
            throw new ServiceException(ResultCode.PARAMS_ERROR, "订单号不能为空");
        }
        return sn;
    }

    public Double getSubsidyAmount() {
        if (subsidyAmount == null) {
            return 0.0;
        }
        return subsidyAmount;
    }

    public Double getPrice() {
        if (price == null || price < 0) {
            throw new ServiceException(ResultCode.PARAMS_ERROR, "金额异常");
        }
        return price;
    }
}
