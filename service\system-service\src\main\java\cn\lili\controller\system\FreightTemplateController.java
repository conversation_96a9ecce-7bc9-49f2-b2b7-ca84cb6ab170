package cn.lili.controller.system;

import cn.lili.common.enums.ResultCode;
import cn.lili.common.security.OperationalJudgment;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.member.entity.dos.User;
import cn.lili.modules.store.entity.dos.FreightTemplate;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import cn.lili.modules.system.service.FreightTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 店铺端,运费模板接口
 *
 * <AUTHOR>
 * @since 2020/8/26
 **/
@RestController
@Tag(name = "店铺端,运费模板接口")
@RequestMapping("/system/template/freight")
@RequiredArgsConstructor
public class FreightTemplateController {

    private final FreightTemplateService freightTemplateService;

    @Operation(summary = "商家运费模板列表,区分管理端和商家端")
    @GetMapping
    public ResultMessage<List<FreightTemplateVO>> list() {
        String storeId = UserContext.getCurrentId();
        SceneEnums sceneEnums = UserContext.getScene();
        if(sceneEnums == SceneEnums.STORE){
            return ResultUtil.data(freightTemplateService.getFreightTemplateList(storeId,sceneEnums.value()));
        }else{
            return ResultUtil.data(freightTemplateService.getFreightTemplateList(null,sceneEnums.value()));
        }
    }

    @Operation(summary = "获取商家运费模板详情")
    @GetMapping("/{id}")
    public ResultMessage<FreightTemplateVO> detail(@PathVariable String id) {
        // 2024-06-14 修改获取模板详情代码，获取模板将不再需要判断是否是当前用户的模板 。用于店铺获取供应商运费模板
        FreightTemplateVO freightTemplate = freightTemplateService.getFreightTemplate(id);
        return ResultUtil.data(freightTemplate);
    }

    @Operation(summary = "添加商家运费模板,区分商家端和店铺端")
    @PostMapping
    public ResultMessage<FreightTemplateVO> add(@RequestBody FreightTemplateVO freightTemplateVO) {
        String storeId = UserContext.getCurrentId();
        SceneEnums sceneEnums = UserContext.getScene();
        if(sceneEnums == SceneEnums.STORE){
            freightTemplateVO.setStoreId(storeId);
        }else{
            //管理端增加运费模板
            freightTemplateVO.setStoreId("");
        }
        freightTemplateVO.setCreateFrom(sceneEnums.value());
        freightTemplateVO.validateParams();
        return ResultUtil.data(freightTemplateService.addFreightTemplate(freightTemplateVO));
    }

    @Operation(summary = "修改商家运费模板")
    @PutMapping("/{id}")
    public ResultMessage<FreightTemplateVO> edit(@PathVariable String id, @RequestBody FreightTemplateVO freightTemplateVO) {
        freightTemplateVO.setId(id);
        String storeId = UserContext.getCurrentId();
        SceneEnums sceneEnums = UserContext.getScene();
        if(sceneEnums == SceneEnums.STORE){
            freightTemplateVO.setStoreId(storeId);
        }else{
            //管理端增加运费模板
            freightTemplateVO.setStoreId("0");
        }
        freightTemplateVO.setCreateFrom(sceneEnums.value());
        freightTemplateVO.validateParams();
        return ResultUtil.data(freightTemplateService.editFreightTemplate(freightTemplateVO));
    }

    @Operation(summary = "删除商家运费模板,区分商家端和管理端")
    @DeleteMapping("/{id}")
    public ResultMessage<Object> edit(@PathVariable String id) {
        String storeId = UserContext.getCurrentId();
        SceneEnums sceneEnums = UserContext.getScene();
        FreightTemplate freightTemplate = freightTemplateService.getById(id);
        if(sceneEnums == SceneEnums.STORE){
            if (freightTemplate == null || !Objects.equals(storeId, freightTemplate.getStoreId())) {
                return ResultUtil.error(ResultCode.FREIGHT_TEMPLATE_NOT_EXIST);
            }
        }else{
            if(freightTemplate == null){
                return ResultUtil.error(ResultCode.FREIGHT_TEMPLATE_NOT_EXIST);
            }
        }

        freightTemplateService.removeFreightTemplate(id);
        return ResultUtil.success();
    }
}
