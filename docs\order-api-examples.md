# 订单查询 API 使用示例

## 接口信息

- **接口地址**: `GET /order`
- **接口描述**: 查询订单列表，支持按店铺类型筛选
- **返回格式**: `Page<OrderSimpleVO>`

## 新增参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| selfOperated | Boolean | 否 | 店铺类型筛选<br/>• `true`: 直营店铺订单<br/>• `false`: 非直营店铺订单<br/>• `null` 或不传: 不筛选 |

## 使用示例

### 1. 查询直营店铺订单

**请求示例**:
```http
GET /order?selfOperated=true&pageNumber=1&pageSize=10
```

**cURL 示例**:
```bash
curl -X GET "http://localhost:8080/order?selfOperated=true&pageNumber=1&pageSize=10" \
  -H "Authorization: Bearer your-token"
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "sn": "O202507260001",
        "tradeSn": "T202507260001",
        "storeName": "官方直营店",
        "storeId": "store001",
        "orderStatus": "COMPLETED",
        "payStatus": "PAID",
        "flowPrice": 299.00,
        "createTime": "2025-07-26 10:30:00",
        "paymentTime": "2025-07-26 10:35:00",
        "nickname": "张三",
        "clientType": "PC"
      }
    ],
    "total": 50,
    "current": 1,
    "size": 10,
    "pages": 5
  }
}
```

### 2. 查询非直营店铺订单

**请求示例**:
```http
GET /order?selfOperated=false&pageNumber=1&pageSize=10
```

**JavaScript 示例**:
```javascript
const response = await fetch('/order?selfOperated=false&pageNumber=1&pageSize=10', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-token',
    'Content-Type': 'application/json'
  }
});

const result = await response.json();
console.log('非直营店铺订单:', result.data.records);
```

### 3. 查询所有订单（不筛选店铺类型）

**请求示例**:
```http
GET /order?pageNumber=1&pageSize=10
```

或者明确设置为空：
```http
GET /order?selfOperated=&pageNumber=1&pageSize=10
```

### 4. 结合其他条件的复合查询

**查询直营店铺的已完成订单**:
```http
GET /order?selfOperated=true&orderStatus=COMPLETED&pageNumber=1&pageSize=10
```

**查询非直营店铺的待发货订单**:
```http
GET /order?selfOperated=false&orderStatus=UNDELIVERED&pageNumber=1&pageSize=10
```

**查询直营店铺的指定时间范围订单**:
```http
GET /order?selfOperated=true&startDate=2025-07-01 00:00:00&endDate=2025-07-31 23:59:59&pageNumber=1&pageSize=20
```

## 前端组件示例

### Vue.js 示例

```vue
<template>
  <div>
    <!-- 店铺类型筛选 -->
    <el-select v-model="searchParams.selfOperated" placeholder="选择店铺类型" clearable>
      <el-option label="全部" :value="null"></el-option>
      <el-option label="直营店铺" :value="true"></el-option>
      <el-option label="非直营店铺" :value="false"></el-option>
    </el-select>
    
    <!-- 其他筛选条件 -->
    <el-input v-model="searchParams.orderSn" placeholder="订单号"></el-input>
    
    <el-button @click="searchOrders">查询</el-button>
    
    <!-- 订单列表 -->
    <el-table :data="orders" v-loading="loading">
      <el-table-column prop="sn" label="订单号"></el-table-column>
      <el-table-column prop="storeName" label="店铺名称"></el-table-column>
      <el-table-column prop="orderStatus" label="订单状态"></el-table-column>
      <el-table-column prop="flowPrice" label="订单金额"></el-table-column>
      <el-table-column prop="createTime" label="下单时间"></el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <el-pagination
      v-model:current-page="searchParams.pageNumber"
      v-model:page-size="searchParams.pageSize"
      :total="total"
      @current-change="searchOrders"
      @size-change="searchOrders">
    </el-pagination>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { orderApi } from '@/api/order'

const loading = ref(false)
const orders = ref([])
const total = ref(0)

const searchParams = reactive({
  selfOperated: null,
  orderSn: '',
  pageNumber: 1,
  pageSize: 10
})

const searchOrders = async () => {
  loading.value = true
  try {
    const response = await orderApi.queryOrder(searchParams)
    orders.value = response.data.records
    total.value = response.data.total
  } catch (error) {
    console.error('查询订单失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  searchOrders()
})
</script>
```

### React 示例

```jsx
import React, { useState, useEffect } from 'react';
import { Table, Select, Input, Button, Pagination } from 'antd';
import { orderApi } from '../api/order';

const OrderList = () => {
  const [loading, setLoading] = useState(false);
  const [orders, setOrders] = useState([]);
  const [total, setTotal] = useState(0);
  const [searchParams, setSearchParams] = useState({
    selfOperated: null,
    orderSn: '',
    pageNumber: 1,
    pageSize: 10
  });

  const searchOrders = async () => {
    setLoading(true);
    try {
      const response = await orderApi.queryOrder(searchParams);
      setOrders(response.data.records);
      setTotal(response.data.total);
    } catch (error) {
      console.error('查询订单失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    searchOrders();
  }, [searchParams.pageNumber, searchParams.pageSize]);

  const columns = [
    { title: '订单号', dataIndex: 'sn', key: 'sn' },
    { title: '店铺名称', dataIndex: 'storeName', key: 'storeName' },
    { title: '订单状态', dataIndex: 'orderStatus', key: 'orderStatus' },
    { title: '订单金额', dataIndex: 'flowPrice', key: 'flowPrice' },
    { title: '下单时间', dataIndex: 'createTime', key: 'createTime' }
  ];

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Select
          placeholder="选择店铺类型"
          style={{ width: 150, marginRight: 8 }}
          value={searchParams.selfOperated}
          onChange={(value) => setSearchParams({...searchParams, selfOperated: value})}
          allowClear
        >
          <Select.Option value={true}>直营店铺</Select.Option>
          <Select.Option value={false}>非直营店铺</Select.Option>
        </Select>
        
        <Input
          placeholder="订单号"
          style={{ width: 200, marginRight: 8 }}
          value={searchParams.orderSn}
          onChange={(e) => setSearchParams({...searchParams, orderSn: e.target.value})}
        />
        
        <Button type="primary" onClick={searchOrders}>查询</Button>
      </div>
      
      <Table
        columns={columns}
        dataSource={orders}
        loading={loading}
        pagination={false}
        rowKey="sn"
      />
      
      <Pagination
        current={searchParams.pageNumber}
        pageSize={searchParams.pageSize}
        total={total}
        onChange={(page, size) => setSearchParams({...searchParams, pageNumber: page, pageSize: size})}
        style={{ marginTop: 16, textAlign: 'right' }}
      />
    </div>
  );
};

export default OrderList;
```

## 注意事项

1. **权限控制**: 根据用户角色可能需要限制查询范围
2. **性能优化**: 大数据量查询时建议添加时间范围限制
3. **错误处理**: 前端需要处理网络异常和业务异常
4. **数据缓存**: 可以考虑对查询结果进行适当缓存

## 常见问题

**Q: 如何同时查询直营和非直营订单？**
A: 不传 `selfOperated` 参数或传 `null` 即可查询所有订单。

**Q: 店铺类型筛选会影响其他查询条件吗？**
A: 不会，店铺类型筛选可以与所有现有查询条件组合使用。

**Q: 如何确认订单属于哪种店铺类型？**
A: 可以通过订单的 `storeId` 查询对应的店铺信息，查看 `selfOperated` 字段。
