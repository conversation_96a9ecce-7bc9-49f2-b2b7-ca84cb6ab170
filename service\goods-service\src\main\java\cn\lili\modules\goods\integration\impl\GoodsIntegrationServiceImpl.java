package cn.lili.modules.goods.integration.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.lili.cache.Cache;
import cn.lili.cache.CachePrefix;
import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.enums.SwitchEnum;
import cn.lili.common.event.TransactionCommitSendMQEvent;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.CurrencyUtil;
import cn.lili.common.utils.SnowFlake;
import cn.lili.exchange.AmqpExchangeProperties;
import cn.lili.modules.goods.client.GoodsCollectionClient;
import cn.lili.modules.goods.client.GoodsSkuClient;
import cn.lili.modules.goods.entity.dos.*;
import cn.lili.modules.goods.entity.dto.*;
import cn.lili.modules.goods.entity.enums.*;
import cn.lili.modules.goods.entity.vos.*;
import cn.lili.modules.goods.integration.GoodsIntegrationService;
import cn.lili.modules.goods.service.*;
import cn.lili.modules.goods.sku.GoodsSkuBuilder;
import cn.lili.modules.member.client.FootPrintClient;
import cn.lili.modules.member.client.MemberEvaluationClient;
import cn.lili.modules.member.client.UserClient;
import cn.lili.modules.member.entity.dos.FootPrint;
import cn.lili.modules.member.entity.dos.GoodsCollection;
import cn.lili.modules.member.entity.dto.EvaluationQueryParams;
import cn.lili.modules.member.entity.enums.EvaluationGradeEnum;
import cn.lili.modules.page.client.PageDataClient;
import cn.lili.modules.file.client.UploadClient;
import cn.lili.modules.promotion.client.MemberCouponClient;
import cn.lili.modules.promotion.client.PromotionGoodsClient;
import cn.lili.modules.promotion.client.PromotionsClient;
import cn.lili.modules.promotion.entity.dos.Coupon;
import cn.lili.modules.promotion.entity.dos.PromotionGoods;
import cn.lili.modules.promotion.entity.dto.search.PromotionGoodsSearchParams;
import cn.lili.modules.promotion.entity.enums.CouponGetEnum;
import cn.lili.modules.promotion.entity.enums.PromotionsScopeTypeEnum;
import cn.lili.modules.search.entity.dos.EsGoodsIndex;
import cn.lili.modules.search.entity.dos.EsSupplierGoodsIndex;
import cn.lili.modules.search.entity.dto.EsGoodsIndexUpdateDTO;
import cn.lili.modules.search.service.EsGoodsIndexService;
import cn.lili.modules.store.client.FreightTemplateClient;
import cn.lili.modules.store.client.StoreClient;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.dto.StoreSearchParams;
import cn.lili.modules.store.entity.enums.StoreStatusEnum;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import cn.lili.modules.store.entity.vos.StoreVO;
import cn.lili.modules.supplier.entity.dto.SupplierGoodsOperationDTO;
import cn.lili.modules.system.client.SettingClient;
import cn.lili.modules.system.entity.dos.Setting;
import cn.lili.modules.system.entity.dto.GoodsSetting;
import cn.lili.modules.system.entity.dto.OrderSetting;
import cn.lili.modules.system.entity.enums.SettingEnum;
import cn.lili.mybatis.model.BaseEntity;
import cn.lili.mybatis.util.SceneHelp;
import cn.lili.routing.GoodsRoutingKey;
import cn.lili.routing.PromotionRoutingKey;
import cn.lili.util.AmqpMessage;
import cn.lili.util.AmqpSender;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.Date;


/**
 * <AUTHOR>
 * @since 2022/8/15
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class GoodsIntegrationServiceImpl implements GoodsIntegrationService {

    private final GoodsService goodsService;

    private final GoodsSkuService goodsSkuService;

    private final CategoryService categoryService;

    private final BrandService brandService;

    private final EsGoodsIndexService goodsIndexService;

    private final GoodsGalleryService goodsGalleryService;

    private final StoreClient storeClient;

    private final SettingClient settingClient;

    private final MemberEvaluationClient memberEvaluationClient;

    private final PromotionGoodsClient promotionGoodsClient;

    private final AmqpSender amqpSender;

    private final AmqpExchangeProperties amqpExchangeProperties;

    private final ApplicationEventPublisher applicationEventPublisher;

    private final PromotionsClient promotionsClient;

    private final MemberCouponClient memberCouponClient;

    private final FreightTemplateClient freightTemplateClient;

    private final Cache cache;

    private final GoodsCollectionClient goodsCollectionClient;

    private final FootPrintClient footPrintClient;

    private final PageDataClient pageDataClient;

    private final UploadClient uploadClient;

    @Override
    @Transactional
    public void saveGoods(GoodsOperationDTO goodsOperationDTO) {
        // 保存商品信息
        Goods goods = this.wrapperGoods(goodsOperationDTO);
        // 保存商品信息
        this.goodsService.saveOrUpdate(goods);
        // 保存商品相关其他信息，例如相册、sku
        this.generateGoodsHandler(goods, goodsOperationDTO);
        // 发送生成商品索引信息
        this.sendGenerateGoods(Collections.singletonList(goods.getId()));
    }

    @Override
    @Transactional
    public void updateGoods(GoodsOperationDTO goodsOperationDTO) {
        QueryWrapper<Goods> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", goodsOperationDTO.getGoodsId());
        queryWrapper.eq(!UserContext.isManager(), "store_id", UserContext.getCurrentId());
        Goods goods = goodsService.getOne(queryWrapper, false);

        if (goods == null) {
            throw new ServiceException(ResultCode.GOODS_NOT_EXIST);
        }
        //代理商品无法在此处修改
        if (Boolean.TRUE.equals(goods.getIsProxyGoods())) {
            throw new ServiceException(ResultCode.PROXY_GOODS_CANT_UPDATE_HEAR);
        }
        // 编辑商品时封装信息
        goods = this.wrapperGoods(goodsOperationDTO, goods);
        // 更新商品信息
        goodsService.saveOrUpdate(goods);
        // 保存商品相关其他信息，例如相册、sku
        this.generateGoodsHandler(goods, goodsOperationDTO);
        // 发送生成商品索引信息
        this.sendGenerateGoods(Collections.singletonList(goods.getId()));
        // 商品信息变更同步分销商品
        this.syncDistributionGoods(Collections.singletonList(goods.getId()));
    }

    @Override
    @Transactional
    public void saveSupplierGoods(SupplierGoodsOperationDTO supplierGoodsOperationDTO) {
        // 构建商品基本信息
        Goods goods = this.wrapperGoods(supplierGoodsOperationDTO);
        // 保存商品信息
        this.goodsService.save(goods);
        // 构建商品 sku基本信息
        List<GoodsSku> goodsSkus = this.generateGoodsHandler(goods, supplierGoodsOperationDTO);

        // 如果支持采购，并且配置有采购规则
        if (Boolean.TRUE.equals(goods.getSupportPurchase())
            && CollUtil.isNotEmpty(supplierGoodsOperationDTO.getWholesaleList())) {
            this.renderGoodsSkuWholesale(goodsSkus, supplierGoodsOperationDTO);
            this.goodsSkuService.updateBatch(goodsSkus);
        }

        // 发送生成商品索引信息
        this.sendGenerateGoods(Collections.singletonList(goods.getId()));
    }

    @Override
    @Transactional
    public void updateSupplierGoods(SupplierGoodsOperationDTO supplierGoodsOperationDTO) {

        QueryWrapper<Goods> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", supplierGoodsOperationDTO.getGoodsId());
        queryWrapper.eq("store_id", UserContext.getCurrentId());
        Goods goods = goodsService.getOne(queryWrapper, false);

        if (goods == null) {
            throw new ServiceException(ResultCode.GOODS_NOT_EXIST);
        }
        // 构建商品基本信息
        goods = this.wrapperGoods(supplierGoodsOperationDTO);
        // 更新商品信息
        goodsService.updateGoods(goods);
        // 构建商品 sku基本信息
        List<GoodsSku> goodsSkus = this.generateGoodsHandler(goods, supplierGoodsOperationDTO);

        // 如果支持采购，并且配置有采购规则
        if (Boolean.TRUE.equals(goods.getSupportPurchase())
            && CollUtil.isNotEmpty(supplierGoodsOperationDTO.getWholesaleList())) {
            this.renderGoodsSkuWholesale(goodsSkus, supplierGoodsOperationDTO);
            this.goodsSkuService.updateBatch(goodsSkus);
        }
        // 发送生成商品索引信息
        sendGenerateGoods(Collections.singletonList(goods.getId()));
        // 下架店铺代理商品 并告知原因
        downProxyGoods(Collections.singletonList(goods.getId()),
                "供应商重新编辑商品信息，如需再次上架请仔细核对商品价格信息等。");
        // 更新供应商商品信息后，更新代理商品信息
        updateStoreGoods(goods);
        List<Goods> list = goodsService.list(new LambdaQueryWrapper<Goods>().eq(Goods::getSupplierGoodsId, goods.getId()));
        List<String> storeIdList = list.stream().map(Goods::getStoreId).distinct().toList();
        updateSupplierGoodsSkuConst(storeIdList, goodsSkus);
    }

    @Override
    @Transactional
    public void saveProxyGoods(String goodsId) {
        Goods goods = goodsService.getById(goodsId);
        //商品为空
        if (goods == null) {
            throw new ServiceException(ResultCode.GOODS_NOT_EXIST);
        }
        // 当前用户
        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            throw new ServiceException(ResultCode.USER_NOT_LOGIN);
        }

        //判定是否可以代理商品
        if (!storeClient.getBusinessCategory(currentUser.getExtendId()).contains(goods.getCategoryPath().substring(0, goods.getCategoryPath().indexOf(",")))) {
            throw new ServiceException(ResultCode.BUSINESS_UN_PROXY);
        }


        //一个店铺只能对一个商品代理一次
        LambdaQueryWrapper<Goods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Goods::getSupplierGoodsId, goodsId);
        queryWrapper.eq(Goods::getDeleteFlag, false);
        queryWrapper.eq(Goods::getStoreId, currentUser.getExtendId());


        if (this.goodsService.count(queryWrapper) > 0) {
            throw new ServiceException(ResultCode.GOODS_PROXY_TOO_MUCH);
        }
        List<GoodsSku> goodsSkuList = goodsSkuService.getGoodsSkuListByGoodsId(goodsId);
        storeOwnHandler(goods);

//        List<FreightTemplateVO> freightTemplateList = freightTemplateClient.getFreightTemplateList(goods.getStoreId());

        // 如果没有运费模板或者商品类型为虚拟商品，则模板id为-1(无运费)
//        if (freightTemplateList.isEmpty() || goods.getGoodsType().equals(GoodsTypeEnum.VIRTUAL_GOODS.name())) {
//            goods.setTemplateId("-1");
//        } else {
//            goods.setTemplateId(freightTemplateList.get(0).getId());
//        }

        //处理sku信息
        List<GoodsSku> goodsSkus = storeOwnHandler(goodsSkuList, goods);

        goods.setPrice(goodsSkus.stream().mapToDouble(GoodsSku::getPrice).min().orElse(0.0));

        goodsService.save(goods);
        goodsSkuService.saveBatch(goodsSkus);
        this.sendGenerateGoods(Collections.singletonList(goods.getId()));
    }

    /**
     * 格式化商品为店铺所有
     *
     * @param goods 商品
     */
    private void storeOwnHandler(Goods goods) {

        String supplierGoodsId = goods.getId();
        String supplierId = goods.getStoreId();
        String supplierName = goods.getStoreName();

        AuthUser authUser = Objects.requireNonNull(UserContext.getCurrentUser());
        //格式化商品为店铺所有，需要将商品id置空，以及配置店铺id和店铺名称
        goods.setId(SnowFlake.getIdStr());
        goods.setStoreId(authUser.getExtendId());
        goods.setMarketEnable(GoodsMarketEnum.DOWN.name());
        goods.setStoreName(authUser.getExtendName());
        goods.setStoreLogo(authUser.getFace());

        goods.setSupplierGoodsId(supplierGoodsId);
        goods.setSupplierId(supplierId);
        goods.setSupplierName(supplierName);
        goods.setSupplierEnum(
                SupplierEnum.getSupplierEnum(supplierId).name()
        );
        goods.setSupportProxy(false);
        goods.setSupportPurchase(false);
        goods.setSalesModel(SalesModeEnum.RETAIL.name());
        goods.setIsProxyGoods(true);
        goods.setTemplateId(goods.getTemplateId());
        goods.setWholesale(null);
        // 写入当前用户场景
        SceneHelp.objectHandler(goods);

        //复制商品相册
        goodsGalleryService.copyGoodsGallery(supplierGoodsId, goods.getId());

    }


    /**
     * 格式化SKU为店铺所有
     * <p>
     * 该方法将一组商品SKU格式化为店铺所有的SKU。主要包括克隆原始SKU并设置相关属性，
     * 如店铺信息、供应商信息、价格、重量等。
     *
     * @param goodsSkus 商品SKU列表
     * @param goods     商品信息
     * @return 格式化后的SKU列表
     */
    private List<GoodsSku> storeOwnHandler(List<GoodsSku> goodsSkus, Goods goods) {
        // 获取当前用户信息，确保用户上下文存在
        AuthUser authUser = Objects.requireNonNull(UserContext.getCurrentUser());
        // 获取当前用户的店铺信息
        StoreVO store = storeClient.getStore(authUser.getExtendId());

        // 遍历商品SKU列表，克隆并格式化每个SKU
        return goodsSkus.stream().map(goodsSku -> {
                    try {
                        // 克隆原始SKU对象
                        GoodsSku sku = goodsSku.clone();
                        // 格式化克隆后的SKU
                        formatSku(sku, goods, goodsSku, authUser, store);
                        return sku;
                    } catch (Exception e) {
                        // 如果格式化过程中出现异常，抛出自定义服务异常
                        log.error("格式化SKU失败", e);
                        throw new ServiceException(ResultCode.GOODS_SKU_SUPPLIER_ADD_ERROR);
                    }
                }).peek(SceneHelp::objectHandler) // 对每个SKU应用场景处理
                .collect(Collectors.toList()); // 收集格式化后的SKU列表
    }

    /**
     * 格式化单个SKU
     * <p>
     * 该方法对克隆后的SKU进行格式化，设置其属性为供应商所有的SKU。
     * 包括清空ID、设置店铺信息、供应商信息、价格、重量等。
     *
     * @param sku      克隆后的SKU对象
     * @param goods    商品信息
     * @param goodsSku 原始SKU对象
     * @param authUser 当前用户信息
     * @param store    当前用户的店铺信息
     */
    private void formatSku(GoodsSku sku, Goods goods, GoodsSku goodsSku, AuthUser authUser, StoreVO store) {
        // 清空SKU的ID
        sku.setId(null);
        // 设置SKU所属的商品ID
        sku.setGoodsId(goods.getId());
        // 设置店铺ID和店铺名称
        sku.setStoreId(authUser.getExtendId());
        sku.setStoreName(authUser.getExtendName());
        sku.setStoreLogo(authUser.getFace());
        // 设置SKU的上下架状态为下架
        sku.setMarketEnable(GoodsMarketEnum.DOWN.name());

        // 设置供应商相关信息
        sku.setSupplierGoodsId(goodsSku.getGoodsId());
        sku.setSupplierSkuId(goodsSku.getId());
        sku.setSupplierId(goodsSku.getStoreId());
        sku.setSupplierName(goodsSku.getStoreName());

        // 设置SKU的代理和采购支持状态
        sku.setSupportProxy(false);
        sku.setSupportPurchase(false);
        // 设置销售模式为零售
        sku.setSalesModel(SalesModeEnum.RETAIL.name());
        // 标记SKU为代理商品
        sku.setIsProxyGoods(true);
        // 设置运费模板ID
        sku.setFreightTemplateId(goods.getTemplateId());
        // 设置SKU的重量
        sku.setWeight(goodsSku.getWeight());

        // 计算并设置SKU的价格
        sku.setPrice(agentPriceCalculation(sku.getPrice(), store.getAgencyRate()));
        // 设置SKU的成本价为原始SKU的价格
        sku.setCost(goodsSku.getPrice());
        // 清空批发规则
        sku.setWholesale(null);
    }

    /**
     * 更新供应商商品信息后，更新代理商品信息
     *
     * @param goods 商品信息
     */
    private void updateStoreGoods(Goods goods) {
        List<Goods> list = goodsService.list(new LambdaQueryWrapper<Goods>().eq(Goods::getDeleteFlag, false).eq(Goods::getSupplierGoodsId, goods.getId()));
        for (Goods updateGoods : list) {
            updateGoods.setGoodsUnit(goods.getGoodsUnit());
            updateGoods.setBrandId(goods.getBrandId());
            updateGoods.setParams(goods.getParams());
            updateGoods.setTemplateId(goods.getTemplateId());
            goodsService.updateGoods(updateGoods);
        }
        this.sendGenerateGoods(list.stream().map(BaseEntity::getId).toList());
    }

    /**
     * 更新供应商商品信息后，更新代理商品信息
     *
     * @param goodsSkus 商品sku信息
     */
    private void updateSupplierGoodsSkuConst(List<String> storeIdList, List<GoodsSku> goodsSkus) {
        // 更新代理商品sku信息 这里只能循环处理无法通过一条 sql 处理优化
        if (storeIdList.isEmpty()) {
            return;
        }
        // 获取店铺列表
        StoreSearchParams storeSearchParams = new StoreSearchParams();
        storeSearchParams.setStoreIds(storeIdList);
        List<Store> storeList = storeClient.list(storeSearchParams);
        //获取供应商品SkuId 集合
        List<String> supplierSkuIdList = new ArrayList<>(goodsSkus.stream().map(BaseEntity::getId).toList());
        for (Store store : storeList) {
            //查看该店铺下拥有的SKU列表
            List<GoodsSku> list = goodsSkuService.list(new LambdaQueryWrapper<GoodsSku>().eq(GoodsSku::getStoreId, store.getId()).in(GoodsSku::getSupplierSkuId, supplierSkuIdList));
            //删除原本代理商品规格
            String supplierGoodsId = list.getFirst().getSupplierGoodsId();
            goodsSkuService.remove(new LambdaQueryWrapper<GoodsSku>().eq(GoodsSku::getStoreId, store.getId()).eq(GoodsSku::getSupplierGoodsId, supplierGoodsId));
            //存在规格进行数据修改
            for (GoodsSku goodsSku : list) {
                for (GoodsSku skus : goodsSkus) {
                    if (skus.getId().equals(goodsSku.getSupplierSkuId())) {
                        //通过店铺的代理利率计算商品价格
                        Double costPrice = skus.getPrice();
                        goodsSku.setPrice(agentPriceCalculation(costPrice, store.getAgencyRate()));
                        goodsSku.setCost(skus.getPrice());
                        goodsSku.setWeight(skus.getWeight());
                        goodsSku.setGoodsName(skus.getGoodsName());
                        goodsSku.setSpecs(skus.getSpecs());
                        goodsSku.setQuantity(skus.getQuantity());
                        goodsSku.setSimpleSpecs(skus.getSimpleSpecs());
                        goodsSku.setSn(skus.getSn());
                    }
                }
            }

            //判断出需要新增的列表
            Goods storeGoods = goodsService.getOne(new LambdaQueryWrapper<Goods>().eq(Goods::getStoreId, store.getId()).eq(Goods::getDeleteFlag, false).eq(Goods::getSupplierGoodsId, supplierGoodsId));
            List<String> collect = list.stream().map(GoodsSku::getSupplierSkuId).toList();
            supplierSkuIdList.removeAll(collect);
            if (!supplierSkuIdList.isEmpty()) {
                for (String insertId : supplierSkuIdList) {
                    for (GoodsSku skus : goodsSkus) {
                        if (skus.getId().equals(insertId)) {
                            list.add(storeOwnHandler(skus, storeGoods, store));
                        }
                    }
                }
            }
            // 保存/修改 数据
            goodsSkuService.saveOrUpdateBatch(list);
        }
    }

    /**
     * 格式化SKU为供应商所有
     *
     * @param goodsSku 商品sku
     */
    private GoodsSku storeOwnHandler(GoodsSku goodsSku, Goods goods, Store store) {
        //格式化商品为店铺所有，需要将商品id置空，以及配置店铺id和店铺名称
        try {
            GoodsSku sku = goodsSku.clone();

            String supplierGoodsId = goodsSku.getGoodsId();
            String supplierSkuId = goodsSku.getId();
            String supplierId = goodsSku.getStoreId();
            String supplierName = goodsSku.getStoreName();

            sku.setId(null);
            sku.setGoodsId(goods.getId());
            sku.setStoreId(store.getId());
            sku.setStoreName(store.getStoreName());
            sku.setStoreLogo(store.getStoreLogo());
            sku.setMarketEnable(GoodsMarketEnum.DOWN.name());

            sku.setSupplierGoodsId(supplierGoodsId);
            sku.setSupplierSkuId(supplierSkuId);
            sku.setSupplierId(supplierId);
            sku.setSupplierName(supplierName);

            sku.setSupportProxy(false);
            sku.setSupportPurchase(false);
            sku.setSalesModel(SalesModeEnum.RETAIL.name());
            sku.setIsProxyGoods(true);
            sku.setFreightTemplateId(goods.getTemplateId());

            //通过店铺的代理利率计算商品价格
            Double costPrice = goodsSku.getPrice();
            sku.setPrice(agentPriceCalculation(costPrice, store.getAgencyRate()));

            //将代理商的价格设置为当前商品的成本价
            sku.setCost(goodsSku.getPrice());
            sku.setExtendId(store.getId());
            sku.setScene(SceneEnums.STORE.name());
            return sku;
        } catch (Exception e) {
            throw new ServiceException(ResultCode.GOODS_SKU_SUPPLIER_ADD_ERROR);
        }
    }


    //代理价格计算
    private double agentPriceCalculation(Double costPrice, Integer agencyRate) {
        //使用BigDecimal计算防止第一步精度丢失
        BigDecimal a = BigDecimal.valueOf(costPrice);
        BigDecimal percentage = new BigDecimal(agencyRate);
        BigDecimal hundred = new BigDecimal("100");
        BigDecimal result = a.multiply(percentage).divide(hundred, 2, RoundingMode.HALF_UP);
        double price = CurrencyUtil.add(costPrice, result.doubleValue());
        //计算的金额
        double amounts;
        //小于10不进行处理。
        if (price <= 10D) {
            amounts = price;
        } else {
            //先进行第一次取整(去除小数)处理
            amounts = Math.ceil(price);
            //数据取整之后，计算需要向上取整的位数
            double denominator = Math.pow(10, Double.toString(amounts).length() - 4);
            //金额向上取整
            amounts = CurrencyUtil.mul(Math.ceil(CurrencyUtil.div(amounts, denominator)), denominator);

        }
        return amounts;
    }

    /**
     * 下架代理商品
     *
     * @param goodsIds 商品id集合
     * @param reason   原因
     */
    private void downProxyGoods(List<String> goodsIds, String reason) {
        LambdaUpdateWrapper<Goods> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.in(Goods::getSupplierGoodsId, goodsIds);
        updateWrapper.set(Goods::getMarketEnable, GoodsMarketEnum.DOWN.name());
        updateWrapper.set(Goods::getUnderMessage, reason);
        updateWrapper.set(Goods::getOffShelfTime, new Date());
        goodsService.update(updateWrapper);


        Map<String, Object> query = MapUtil.builder(new HashMap<String, Object>()).put("supplierGoodsId", goodsIds).build();
        Map<String, Object> update = MapUtil.builder(new HashMap<String, Object>()).put("marketEnable", GoodsMarketEnum.DOWN.name()).build();
        this.updateGoodsEs(query, update, EsGoodsIndex.class);
    }

    @Override
    @Transactional
    public void updateGoodsStatus(GoodsUpdateStatusDTO goodsUpdateStatusDTO) {
        AuthUser authUser = Objects.requireNonNull(UserContext.getCurrentUser());
        // 如果不是管理员，则不允许修改商品审核状态 且只能修改自己的商品
        if (!SceneEnums.MANAGER.equals(authUser.getScene())) {
            goodsUpdateStatusDTO.setGoodsAuth(null);
            goodsUpdateStatusDTO.setStoreId(authUser.getExtendId());
        }

        try {
            // 检查商品是否可更新状态
            this.checkUpdateGoodsStatusAvailable(goodsUpdateStatusDTO);

            // 执行更新商品状态
            this.executeGoodsStatusUpdate(goodsUpdateStatusDTO);

            // 发送商品状态更新消息
            this.sendGoodsStatusUpdateMessage(goodsUpdateStatusDTO);
        } catch (IllegalArgumentException iae) {
            log.error("商品状态更新失败，参数错误", iae);
            throw new ServiceException(ResultCode.PARAMS_ERROR, "商品状态更新失败");
        } catch (ServiceException se) {
            throw se;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(ResultCode.PARAMS_ERROR, "商品状态更新失败");
        }

    }

    /**
     * 检查商品是否可更新状态
     *
     * @param params 更新商品状态参数
     */
    private void checkUpdateGoodsStatusAvailable(GoodsUpdateStatusDTO params) {
        if (CollUtil.isEmpty(params.getGoodsIds()) && CharSequenceUtil.isEmpty(params.getStoreId())) {
            throw new ServiceException(ResultCode.PARAMS_ERROR, "更新ID为空");
        }

        // 如果是下架操作，检查商品是否在装修页面中被使用
        if (CharSequenceUtil.isNotEmpty(params.getGoodsStatus()) &&
            GoodsMarketEnum.DOWN.name().equals(params.getGoodsStatus())) {
            this.checkGoodsUsedInPageData(params.getGoodsIds());
        }

        List<Goods> goodsList = this.goodsService.listByIds(params.getGoodsIds());
        for (Goods goods : goodsList) {
            // 上架检查
//            if (!GoodsMarketEnum.DOWN.name().equals(params.getGoodsStatus())) {
//                checkGoodsOtherTableData(goods);
//            }
            // 如果供应商商品不符合要求，或者 sku 信息变更，则抛出异常
            if (Boolean.TRUE.equals(goods.getIsProxyGoods())) {
                Goods supplierGoods = goodsService.getById(goods.getSupplierGoodsId());
                // 如果供应商商品不存在，则抛出异常
                if (supplierGoods == null) {
                    throw new ServiceException(ResultCode.GOODS_NOT_EXIST,
                            "供应商商品已被删除，当前商品无法再编辑操作，请删除后重新代理再进行操作。");
                }
                // 如果供应商商品未审核通过或未上架，则抛出异常
                if (!GoodsAuthEnum.PASS.name().equals(supplierGoods.getAuthFlag()) && !GoodsMarketEnum.UPPER.name()
                        .equals(supplierGoods.getMarketEnable())) {
                    throw new ServiceException(ResultCode.GOODS_NOT_EXIST, "供应商商品未审核通过，暂无法操作");
                }
                // 如果供应商商品 sku 信息变更，则抛出异常
                List<GoodsSku> goodsSkus = this.goodsSkuService.getGoodsSkuListByGoodsId(goods.getId());
                List<GoodsSku> supplierGoodsSkus = this.goodsSkuService.getGoodsSkuListByGoodsId(supplierGoods.getId());

                // 提取 supplierGoodsId 和 id 列表
                Set<String> supplierGoodsSkuIds =
                        supplierGoodsSkus.stream().map(GoodsSku::getId).collect(Collectors.toSet());
                // 检查所有 goodsSkus 中的 supplierGoodsId 是否都在 supplierGoodsSkuIds 中
                Boolean exists =
                        goodsSkus.stream().allMatch(goodsSku -> supplierGoodsSkuIds.contains(goodsSku.getSupplierSkuId()));
                if (!exists) {
                    throw new ServiceException(ResultCode.GOODS_NOT_EXIST,
                            "供应商商品 sku 信息变更，当前商品中部分 sku 已经不再存在于供应商商品中，请删除后重新代理再进行操作。");
                }

            }

        }
    }

    /**
     * 校验商品属性
     * 不严格情况下可以不进行校验
     *
     * @param goods 商品
     */
    public void checkGoodsOtherTableData(Goods goods) {
        String storeId = goods.getStoreId();
        String categoryPath = goods.getCategoryPath();
        String brandId = goods.getBrandId();
        StoreVO store = storeClient.getStore(storeId);
        if (StoreStatusEnum.CLOSED.name().equals(store.getStoreStatus())) {
            throw new ServiceException(ResultCode.GOODS_UPPER_STORE_DISABLE_ERROR);
        }

        // 如果分类不为空，则判定参数是否符合要求
        if (CharSequenceUtil.isNotEmpty(categoryPath)) {
            LambdaQueryWrapper<Category> categoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
            categoryLambdaQueryWrapper.in(Category::getId, Arrays.asList(categoryPath.split(",")));
            categoryLambdaQueryWrapper.eq(Category::getDeleteFlag, false);
            if (categoryService.count(categoryLambdaQueryWrapper) < 3) {
                throw new ServiceException(ResultCode.CATEGORY_NEED_EXIST);
            }
        }
        // 如果品牌不为空，则判定参数是否符合要求
        if (CharSequenceUtil.isNotEmpty(brandId) && !brandId.equals("0")) {
            LambdaQueryWrapper<Brand> brandLambdaQueryWrapper = new LambdaQueryWrapper<>();
            brandLambdaQueryWrapper.eq(Brand::getId, brandId);
            brandLambdaQueryWrapper.eq(Brand::getDeleteFlag, false);
            if (brandService.count(brandLambdaQueryWrapper) < 1) {
                throw new ServiceException(ResultCode.BRAND_NEED_EXIST);
            }
        }
    }

    /**
     * 执行更新商品状态
     *
     * @param params 更新商品状态参数
     */
    public void executeGoodsStatusUpdate(GoodsUpdateStatusDTO params) {
        boolean downFlag = CharSequenceUtil.isNotEmpty(params.getGoodsStatus()) && GoodsMarketEnum.DOWN.name()
                .equals(params.getGoodsStatus());

        // 更新商品
        LambdaUpdateWrapper<Goods> updateGoodsWrapper = new LambdaUpdateWrapper<>();
        LambdaUpdateWrapper<GoodsSku> updateGoodsSkuWrapper = new LambdaUpdateWrapper<>();

        // 更新商品索引查询所需更新商品id
        LambdaQueryWrapper<GoodsSku> querySkuWrapper = new LambdaQueryWrapper<>();
        querySkuWrapper.select(GoodsSku::getId, GoodsSku::getScene, GoodsSku::getGoodsId);
        Map<String, Object> query = new HashMap<>();
        Map<String, Object> update = new HashMap<>();


        // 更新商品状态
        if (CharSequenceUtil.isNotEmpty(params.getGoodsAuth())) {
            updateGoodsWrapper.set(Goods::getAuthFlag, GoodsAuthEnum.valueOf(params.getGoodsAuth()).name());
            updateGoodsWrapper.set(Goods::getAuthMessage, params.getAuthMessage());
            updateGoodsSkuWrapper.set(GoodsSku::getAuthFlag, params.getGoodsAuth());
            updateGoodsSkuWrapper.set(GoodsSku::getAuthMessage, params.getAuthMessage());
            update.put("authFlag", params.getGoodsAuth());

            log.debug("更新商品状态  authFlag = {}", params.getGoodsAuth());
        }
        // 更新商品上下架状态
        if (CharSequenceUtil.isNotEmpty(params.getGoodsStatus())) {
            log.debug("更新商品状态  marketEnable = {}", params.getGoodsStatus());
            // pageDataClient.getEnabledPageDataList(null);
            updateGoodsWrapper.set(Goods::getMarketEnable, GoodsMarketEnum.valueOf(params.getGoodsStatus()).name());
            updateGoodsSkuWrapper.set(GoodsSku::getMarketEnable, params.getGoodsStatus());
            if (params.getGoodsStatus().equals(GoodsMarketEnum.UPPER.name())) {
                updateGoodsWrapper.set(Goods::getUnderMessage, "");
            }
            update.put("marketEnable", params.getGoodsStatus());
            AuthUser currentExistUser = UserContext.getCurrentExistUser();
            // 如果为管理端下架商品, 则将状态改为待审核
            if (SceneEnums.MANAGER.equals(currentExistUser.getScene()) && downFlag) {
                update.put("authFlag", GoodsAuthEnum.TOBEAUDITED.name());
                updateGoodsWrapper.set(Goods::getAuthFlag, GoodsAuthEnum.TOBEAUDITED.name());
                updateGoodsSkuWrapper.set(GoodsSku::getAuthFlag, GoodsAuthEnum.TOBEAUDITED.name());
            }

        }
        // 更新商品下架原因
        updateGoodsWrapper.set(CharSequenceUtil.isNotEmpty(params.getUnderReason()), Goods::getUnderMessage,
                params.getUnderReason());
        // 更新商品删除标志
        if (params.getDeleteFlag() != null) {
            updateGoodsWrapper.set(Goods::getDeleteFlag, params.getDeleteFlag());
            updateGoodsSkuWrapper.set(GoodsSku::getDeleteFlag, params.getDeleteFlag());
            update.put("deleteFlag", params.getDeleteFlag());
        }
        // 更新商品sku下架原因
        updateGoodsSkuWrapper.set(CharSequenceUtil.isNotEmpty(params.getUnderReason()), GoodsSku::getUnderMessage,
                params.getUnderReason());

        // 下架商品，同时下架代理商品
        if (CharSequenceUtil.isNotEmpty(params.getStoreId()) && downFlag) {
            updateGoodsWrapper.nested(
                    i -> i.eq(Goods::getStoreId, params.getStoreId()).or().eq(Goods::getSupplierId, params.getStoreId()));
            updateGoodsSkuWrapper.nested(i -> i.eq(GoodsSku::getStoreId, params.getStoreId()).or()
                    .eq(GoodsSku::getSupplierId, params.getStoreId()));
            querySkuWrapper.nested(i -> i.eq(GoodsSku::getStoreId, params.getStoreId()).or()
                    .eq(GoodsSku::getSupplierId, params.getStoreId()));
        } else {
            updateGoodsWrapper.eq(CharSequenceUtil.isNotEmpty(params.getStoreId()), Goods::getStoreId,
                    params.getStoreId());
            updateGoodsSkuWrapper.eq(CharSequenceUtil.isNotEmpty(params.getStoreId()), GoodsSku::getStoreId,
                    params.getStoreId());
            querySkuWrapper.eq(CharSequenceUtil.isNotEmpty(params.getStoreId()), GoodsSku::getStoreId,
                    params.getStoreId());
        }

        // 下架商品，同时下架代理商品
        if (CollUtil.isNotEmpty(params.getGoodsIds()) && downFlag) {
            updateGoodsWrapper.nested(
                    i -> i.in(Goods::getId, params.getGoodsIds()).or().in(Goods::getSupplierGoodsId, params.getGoodsIds()));
            updateGoodsSkuWrapper.nested(i -> i.in(GoodsSku::getGoodsId, params.getGoodsIds()).or()
                    .in(GoodsSku::getSupplierGoodsId, params.getGoodsIds()));
            querySkuWrapper.nested(i -> i.in(GoodsSku::getGoodsId, params.getGoodsIds()).or()
                    .in(GoodsSku::getSupplierGoodsId, params.getGoodsIds()));
        } else {
            updateGoodsWrapper.in(CollUtil.isNotEmpty(params.getGoodsIds()), Goods::getId, params.getGoodsIds());
            updateGoodsSkuWrapper.in(CollUtil.isNotEmpty(params.getGoodsIds()), GoodsSku::getGoodsId,
                    params.getGoodsIds());
            querySkuWrapper.in(CollUtil.isNotEmpty(params.getGoodsIds()), GoodsSku::getGoodsId,
                    params.getGoodsIds());
        }

        this.goodsService.update(updateGoodsWrapper);

        //操作商品缓存key
        List<GoodsSku> list = this.goodsSkuService.list(querySkuWrapper);
        log.debug("商品状态更新，商品sku数量：{}", list.size());
        if (!list.isEmpty()) {
            // 根据 scene 分组
            Map<String, List<GoodsSku>> sceneMap = list.stream().collect(Collectors.groupingBy(GoodsSku::getScene));
            for (Map.Entry<String, List<GoodsSku>> entry : sceneMap.entrySet()) {
                List<String> ids = entry.getValue().stream().map(GoodsSku::getId).toList();
                query.put("id", ids);

                for (Map.Entry<String, Object> stringObjectEntry : query.entrySet()) {
                    log.debug("商品状态更新，查询条件：{}", stringObjectEntry.getKey() + ":" + stringObjectEntry.getValue());
                }
                if (SceneEnums.STORE.name().equals(entry.getKey())) {
                    this.updateGoodsEs(query, update, EsGoodsIndex.class);
                } else if (SceneEnums.SUPPLIER.name().equals(entry.getKey())) {
                    this.updateGoodsEs(query, update, EsSupplierGoodsIndex.class);
                }
            }
        }

        this.goodsSkuService.update(updateGoodsSkuWrapper);

    }

    /**
     * 发送更新商品状态消息
     *
     * @param params 更新商品状态参数
     */
    public void sendGoodsStatusUpdateMessage(GoodsUpdateStatusDTO params) {

        // 状态是否为通过
        boolean updateAuth = CharSequenceUtil.isNotEmpty(params.getGoodsAuth()) && GoodsAuthEnum.PASS.name()
                .equals(params.getGoodsAuth());
        // 状态是否为上架
        boolean updateStatus = CharSequenceUtil.isNotEmpty(params.getGoodsStatus()) && GoodsMarketEnum.UPPER.name()
                .equals(params.getGoodsStatus());

        // 如果为其他状态，则清除分销商品
        if (!(updateAuth || updateStatus)) {
            List<String> goodsIds = params.getGoodsIds();
            if (CollUtil.isEmpty(goodsIds) && CharSequenceUtil.isNotEmpty(params.getStoreId())) {
                goodsIds = this.goodsService.queryListIdByParams(
                        GoodsSearchParams.builder().storeAndSupplierId(params.getStoreId()).build());
            }
            if (CollUtil.isNotEmpty(goodsIds)) {
                // 同步清除分销商品
                this.syncDeleteDistributionGoods(goodsIds);
                // 同步删除砍价商品
                this.syncDeleteKanjiaGoods(goodsIds);
            }
        }
    }

    @Override
    @Transactional
    public void updateStoreDetail(Store store) {
        UpdateWrapper updateWrapper =
                new UpdateWrapper<>().eq("store_id", store.getId()).set("store_name", store.getStoreName())
                        .set("self_operated", store.getSelfOperated());
        this.goodsService.update(updateWrapper);
        this.goodsSkuService.update(updateWrapper);
        // 如果是供应商，则修改店铺商品信息
        if (store.getScene().equals(SceneEnums.SUPPLIER.name())) {
            updateWrapper =
                    new UpdateWrapper<>().eq("supplier_id", store.getId()).set("supplier_name", store.getStoreName());
            this.goodsService.update(updateWrapper);
            this.goodsSkuService.update(updateWrapper);
        }
    }

    @Override
    public GoodsVO getGoodsVO(String goodsId) {
        //查询商品信息
        Goods goods = this.goodsService.getById(goodsId);
        if (goods == null) {
            log.error("商品ID为{}的商品不存在", goodsId);
            throw new ServiceException(ResultCode.GOODS_NOT_EXIST);
        }
        GoodsVO goodsVO = new GoodsVO();
        //赋值
        BeanUtils.copyProperties(goods, goodsVO);
        //商品id
        goodsVO.setId(goods.getId());
        //商品相册赋值
        List<String> images = new ArrayList<>();
        List<GoodsGallery> galleryList = goodsGalleryService.goodsGalleryList(goodsId);
        for (GoodsGallery goodsGallery : galleryList) {
            images.add(goodsGallery.getOriginal());
        }
        goodsVO.setGoodsGalleryList(images);
        //商品sku赋值
        List<GoodsSkuVO> goodsListByGoodsId = goodsSkuService.getGoodsListByGoodsId(GoodsSearchParams.builder().goodsId(goodsId).build());
        if (goodsListByGoodsId != null && !goodsListByGoodsId.isEmpty()) {
            goodsVO.setSkuList(goodsListByGoodsId);
        }
        //商品分类名称赋值
        List<String> categoryName = new ArrayList<>();
        String categoryPath = goods.getCategoryPath();
        if (CharSequenceUtil.isNotEmpty(categoryPath)) {
            String[] strArray = categoryPath.split(",");
            List<Category> categories = categoryService.listByIds(Arrays.asList(strArray));
            for (Category category : categories) {
                categoryName.add(category.getName());
            }
            goodsVO.setCategoryName(categoryName);
        }

        //参数非空则填写参数
        if (CharSequenceUtil.isNotEmpty(goods.getParams())) {
            goodsVO.setGoodsParamsDTOList(JSONUtil.toList(goods.getParams(), GoodsParamsDTO.class));
        }

        FreightTemplateVO freightTemplate = freightTemplateClient.getFreightTemplate(goodsVO.getTemplateId());
        if (freightTemplate != null) {
            goodsVO.setFreightTemplate(freightTemplate);
        }

        return goodsVO;
    }

    @Override
    @Transactional
    public void syncGoodsSkuCommentCount(String skuId) {
        GoodsSku goodsSku = this.getGoodsSkuByIdFromCache(skuId);
        if (goodsSku == null) {
            return;
        }
        String goodsId = goodsSku.getGoodsId();
        //获取商品信息
        Goods goods = this.goodsService.getById(goodsId);
        if (goods == null) {
            return;
        }
        //修改商品评价数量
        long commentNum = memberEvaluationClient.getEvaluationCount(
                EvaluationQueryParams.builder().status(SwitchEnum.OPEN.name()).goodsId(goodsId).build());
        goods.setCommentNum((int) commentNum);

        //好评数量
        long highPraiseNum = memberEvaluationClient.getEvaluationCount(
                EvaluationQueryParams.builder().status(SwitchEnum.OPEN.name()).goodsId(goodsId)
                        .grade(EvaluationGradeEnum.GOOD.name()).build());
        //好评率
        //好评率
        if (goods.getCommentNum() <= 0) {
            goods.setGrade(0.0);
        } else {
            //好评率=好评数量/总评价数量*100(保留两位小数)
            double grade = NumberUtil.mul(NumberUtil.div(highPraiseNum, goods.getCommentNum().doubleValue(), 2), 100);
            goods.setGrade(grade);
        }
        LambdaUpdateWrapper<Goods> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Goods::getCommentNum, goods.getCommentNum());
        updateWrapper.set(Goods::getGrade, goods.getGrade());
        updateWrapper.eq(Goods::getId, goodsId);
        this.goodsService.update(updateWrapper);

        this.goodsSkuService.updateGoodsSkuGrade(goodsId, goods.getGrade(), goods.getCommentNum());

        //修改规格索引,发送mq消息
        EsGoodsIndexUpdateDTO esGoodsIndexUpdateDTO = new EsGoodsIndexUpdateDTO();

        esGoodsIndexUpdateDTO.setQueryFields(MapUtil.builder(new HashMap<String, Object>()).put("goodsId", goodsId).build());
        esGoodsIndexUpdateDTO.setUpdateFields(MapUtil.builder(new HashMap<String, Object>()).put("commentNum", goods.getCommentNum())
                .put("highPraiseNum", highPraiseNum).put("grade", goods.getGrade()).build());
        if (goods.getScene().equals(SceneEnums.SUPPLIER.name())) {
            esGoodsIndexUpdateDTO.setClazz(EsSupplierGoodsIndex.class);
        } else {
            esGoodsIndexUpdateDTO.setClazz(EsGoodsIndex.class);
        }

        applicationEventPublisher.publishEvent(TransactionCommitSendMQEvent.builder().source("更新商品评价数量")
                .exchange(amqpExchangeProperties.getGoods()).routingKey(GoodsRoutingKey.UPDATE_GOODS_INDEX_FIELD)
                .message(JSONUtil.toJsonStr(esGoodsIndexUpdateDTO)).build());

    }

    @Override
    public GoodsBuyerVO getGoodsSkuDetail(String goodsId, String skuId) {
        GoodsBuyerVO goodsBuyerVO = null;
//            (GoodsBuyerVO) cache.get(CachePrefix.VIEW_GOODS_BUYER.getPrefix(SceneEnums.MEMBER, goodsId + skuId));

        // 如果缓存中有数据，则直接返回并获取缓存中的库存信息
        if (goodsBuyerVO != null && goodsBuyerVO.getData() != null && goodsBuyerVO.getSpecs() != null) {
            //获取商品库存
            Integer integer = (Integer) cache.get(GoodsSkuClient.getStockCacheKey(goodsBuyerVO));


            if (integer != null && !goodsBuyerVO.getData().getQuantity().equals(integer)) {
                //写入最新的库存信息
                goodsBuyerVO.getData().setQuantity(integer);
            }

            for (GoodsSkuSpecVO spec : goodsBuyerVO.getSpecs()) {
                String cacheKey = CachePrefix.SKU_STOCK.getPrefix() + spec.getSkuId();
                if (CharSequenceUtil.isNotEmpty(spec.getSupplierSkuId())) {
                    cacheKey = CachePrefix.SKU_STOCK.getPrefix() + spec.getSupplierSkuId();
                }

                Integer stock = (Integer) cache.get(cacheKey);
                if (stock != null && !spec.getQuantity().equals(stock)) {
                    spec.setQuantity(stock);
                }
            }
            this.checkGoodsBuyerVOPromotion(goodsBuyerVO);

            return goodsBuyerVO;
        }
        return getGoodsBuyerVO(goodsId, skuId);
    }

    @NotNull
    private GoodsBuyerVO getGoodsBuyerVO(String goodsId, String skuId) {
        GoodsBuyerVO goodsBuyerVO;
        goodsBuyerVO = new GoodsBuyerVO();

        AuthUser currentUser = UserContext.getCurrentUser();
        //获取商品VO
        EsGoodsIndex goodsIndex = this.goodsIndexService.findEsGoodsIndexById(skuId);

        //如果skuId为空，则使用商品VO中sku信息获取
        if (CharSequenceUtil.isEmpty(skuId) || "undefined".equals(skuId) || goodsIndex == null) {
            goodsIndex = this.goodsIndexService.findEsGoodsIndexByGoodsId(goodsId);
        }


        //商品下架||商品未审核通过||商品删除，则提示：商品已下架
        if (goodsIndex == null || GoodsMarketEnum.DOWN.name().equals(goodsIndex.getMarketEnable()) || !GoodsAuthEnum.PASS.name()
                .equals(goodsIndex.getAuthFlag()) || CharSequenceUtil.isEmpty(goodsIndex.getSkuList())) {
            throw new ServiceException(ResultCode.GOODS_NOT_EXIST);
        }
        
        //是否允许出售商品
        String storeId = goodsIndex.getStoreId();
        if (StringUtil.isNotBlank(storeId)) {
           StoreVO storeVO =  storeClient.getStore(storeId);
           if (storeVO != null) {
                goodsBuyerVO.setIsBuyGoods(storeVO.getIsBuyGoods());
           }
        }
        GoodsSkuVO goodsSkuDetail = this.getGoodsSkuVO(goodsIndex);

        // 当前商品所有促销
        Map<String, Object> promotionMap = this.checkSkuPromotions(goodsSkuDetail, currentUser, skuId, goodsIndex.getPromotionMap());

        if (goodsIndex.getGoodsGalleryList() != null && !goodsIndex.getGoodsGalleryList().isEmpty()) {
            if (goodsSkuDetail.getGoodsGalleryList() == null || goodsSkuDetail.getGoodsGalleryList().isEmpty()) {
                goodsSkuDetail.setGoodsGalleryList(goodsIndex.getGoodsGalleryList());
            } else {
                List<String> goodsGalleryList = new ArrayList<>(goodsSkuDetail.getGoodsGalleryList());
                goodsGalleryList.addAll(goodsIndex.getGoodsGalleryList());
                goodsSkuDetail.setGoodsGalleryList(goodsGalleryList);
            }
        }

        //获取规格信息
        List<GoodsSkuSpecVO> specVOS = this.getGoodsSkuSpecVO(goodsIndex);
        goodsBuyerVO.setSpecs(specVOS);

        //累加商品库存
        Integer quantityTotal = 0;
        for (Integer quantity : specVOS.stream().map(GoodsSkuSpecVO::getQuantity).toList()) {
            quantityTotal += quantity;
        }


        Setting setting = settingClient.get(SettingEnum.ORDER_SETTING.name());
        OrderSetting orderSetting = JSONUtil.toBean(setting.getSettingValue(), OrderSetting.class);
        goodsSkuDetail.setCloseAfterSale(orderSetting.getCloseAfterSale());

        goodsSkuDetail.setCollectionCount(goodsCollectionClient.countCollectNum(skuId));
        goodsSkuDetail.setReleaseTime(goodsIndex.getReleaseTime());

        //统计规格库存
        goodsSkuDetail.setQuantity(quantityTotal);
        goodsBuyerVO.setData(new GoodsSkuBuyerVO(goodsSkuDetail));
        goodsBuyerVO.setPromotionMap(promotionMap);

        GoodsSku goodsSku = goodsSkuService.getById(skuId);
        if (null != goodsSku) {
            goodsBuyerVO.getData().setPreSaleDeadline(goodsSku.getPreSaleDeadline());
        }

        Goods goods = goodsService.getById(goodsId);
        if (null != goods) {
            goodsBuyerVO.getData().setViewCount(goods.getVisitorCount());
        }


        //获取参数信息
        if (CharSequenceUtil.isNotEmpty(goodsIndex.getParams())) {
            List<GoodsParamsDTO> list = JSONUtil.toList(goodsIndex.getParams(), GoodsParamsDTO.class);
            for (GoodsParamsDTO goodsParamsDTO : list) {
                // 根据sort排序
                goodsParamsDTO.getGoodsParamsItemDTOList().sort(Comparator.comparing(GoodsParamsItemDTO::getSort));
            }
            goodsBuyerVO.setGoodsParamsDTOList(list);
        }

        //记录用户足迹
        if (currentUser != null && currentUser.getScene() != null && !goodsId.equals("undefined")) {
            String userId = currentUser.getExtendId();
            FootPrint footPrint = new FootPrint(userId, goodsId, skuId, goodsIndex.getStoreId(), new Date());
            amqpSender.send(AmqpMessage.builder().exchange(amqpExchangeProperties.getGoods()).routingKey(GoodsRoutingKey.VIEW_GOODS)
                    .message(footPrint).build());
        }
        cache.put(CachePrefix.VIEW_GOODS_BUYER.getPrefix(SceneEnums.MEMBER, goodsId + skuId), goodsBuyerVO, 172800L);
        return goodsBuyerVO;
    }

    private GoodsSkuVO getGoodsSkuVO(EsGoodsIndex index) {
        //初始化商品
        GoodsSkuVO goodsSkuVO = new GoodsSkuVO(index);
        //获取sku信息
        JSONObject jsonObject = JSONUtil.parseObj(index.getSpecs());
        //用于接受sku信息
        List<SpecValueVO> specValueVOS = new ArrayList<>();
        //用于接受sku相册
        List<String> goodsGalleryList = new ArrayList<>();
        //循环提交的sku表单
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            SpecValueVO specValueVO = new SpecValueVO();

            specValueVO.setSpecName(entry.getKey());
            specValueVO.setSpecValue(entry.getValue().toString());
            specValueVOS.add(specValueVO);
        }
        goodsSkuVO.setGoodsGalleryList(goodsGalleryList);
        goodsSkuVO.setSpecList(specValueVOS);
        return goodsSkuVO;
    }

    private List<GoodsSkuSpecVO> getGoodsSkuSpecVO(EsGoodsIndex goodsVO) {
        List<GoodsSkuSpecVO> goodsSkuSpecVOList = new ArrayList<>();
        if (CharSequenceUtil.isNotBlank(goodsVO.getSkuList())) {
            List<GoodsSkuVO> skuList = JSONUtil.toList(goodsVO.getSkuList(), GoodsSkuVO.class);
            skuList.forEach(goodsSkuItem -> {
                GoodsSkuSpecVO goodsSkuSpecVO = new GoodsSkuSpecVO();
                List<SpecValueVO> specValueList = new ArrayList<>();
                if (goodsSkuItem.getSpecs() != null && !goodsSkuItem.getSpecs().isEmpty()) {
                    JSONObject jsonObject = JSONUtil.parseObj(goodsSkuItem.getSpecs());
                    jsonObject.forEach((key, value) -> {
                        SpecValueVO specValueVO = new SpecValueVO();
                        specValueVO.setSpecName(key);
                        specValueVO.setSpecValue(value.toString());
                        if (CollUtil.isNotEmpty(goodsSkuItem.getGoodsGalleryList())) {
                            specValueVO.setSpecType(1);
                            specValueVO.setSpecImage(goodsSkuItem.getGoodsGalleryList());
                        }
                        specValueList.add(specValueVO);
                    });
                }
                goodsSkuSpecVO.setSpecValues(specValueList);
                //获取商品库存
                Integer integer = (Integer) cache.get(GoodsSkuClient.getStockCacheKey(goodsSkuItem));

                //库存不为空,库存与缓存中不一致
                if (integer != null && !goodsSkuItem.getQuantity().equals(integer)) {
                    //写入最新的库存信息
                    goodsSkuItem.setQuantity(integer);
                }
                goodsSkuSpecVO.setQuantity(goodsSkuItem.getQuantity());
                goodsSkuSpecVO.setSkuId(goodsSkuItem.getId());
                goodsSkuSpecVO.setSupplierSkuId(goodsSkuItem.getSupplierSkuId());
                goodsSkuSpecVOList.add(goodsSkuSpecVO);

            });

        }

        return goodsSkuSpecVOList;
    }

    /**
     * 检测商品促销信息
     *
     * @param goodsSkuDetail 商品sku详情
     * @param currentUser    当前用户
     * @param skuId          skuId
     * @param promotionMap   促销信息
     */
    private Map<String, Object> checkSkuPromotions(GoodsSkuVO goodsSkuDetail, AuthUser currentUser, String skuId, Map<String, Object> promotionMap) {
        // 如果没有促销信息直接返回
        if (promotionMap == null || promotionMap.isEmpty()) {
            return promotionMap;
        }

        try {
            // 过滤有效促销活动
            promotionMap = filterValidPromotions(promotionMap, currentUser);

            // 查找特定类型的促销活动（秒杀、满减、拼团）
            Optional<Map.Entry<String, Object>> promotionEntry = findSpecificPromotion(promotionMap);

            if (promotionEntry.isPresent()) {
                // 处理促销商品信息
                processPromotionGoods(goodsSkuDetail, skuId, promotionEntry);

                // 检查促销库存状态
                checkPromotionStock(goodsSkuDetail, skuId, promotionMap, promotionEntry);
            } else {
                // 没有找到促销活动，重置促销标记
                resetPromotionFlags(goodsSkuDetail);
            }

            return promotionMap;
        } catch (Exception e) {
            log.error("检查商品促销信息时发生错误", e);
            // 出错时重置促销状态，确保商品正常显示
            resetPromotionFlags(goodsSkuDetail);
            return promotionMap;
        }
    }

    /**
     * 过滤有效的促销活动
     */
    private Map<String, Object> filterValidPromotions(Map<String, Object> promotionMap, AuthUser currentUser) {
        return promotionMap.entrySet().stream()
                .filter(entry -> isPromotionValid(entry, currentUser))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    /**
     * 判断促销活动是否有效
     */
    private boolean isPromotionValid(Map.Entry<String, Object> entry, AuthUser currentUser) {
        JSONObject jsonObject = JSONUtil.parseObj(entry.getValue());

        // 处理优惠券类型促销
        if (entry.getKey().contains(PromotionTypeEnum.COUPON.name()) && currentUser != null) {
            // 检查优惠券是否存在且未超过发放限制
            Coupon coupon = promotionsClient.getCouponById(jsonObject.getStr("id"));
            if (coupon == null || (coupon.getPublishNum() != 0 && coupon.getReceivedNum() >= coupon.getPublishNum())) {
                return false;
            }

            // 检查用户领取限制
            Integer couponLimitNum = jsonObject.getInt("couponLimitNum");
            if (couponLimitNum > 0) {
                try {
                    Long count = memberCouponClient.getMemberCouponCount(currentUser.getId(), jsonObject.getStr("id"));
                    if (count >= couponLimitNum) {
                        return false;
                    }
                } catch (Exception e) {
                    log.error("获取用户优惠券数量失败", e);
                    return false;
                }
            }
        }

        // 检查促销时间和领取方式
        return (jsonObject.get("getType") == null || CouponGetEnum.FREE.name().equals(jsonObject.getStr("getType")))
               && isPromotionTimeValid(jsonObject);
    }

    /**
     * 检查促销时间是否有效
     */
    private boolean isPromotionTimeValid(JSONObject jsonObject) {
        long currentTimeMillis = System.currentTimeMillis();
        return (jsonObject.getDate("startTime") == null || jsonObject.getDate("startTime").getTime() <= currentTimeMillis)
               && (jsonObject.getDate("endTime") == null || jsonObject.getDate("endTime").getTime() >= currentTimeMillis);
    }

    /**
     * 查找特定类型的促销活动
     */
    private Optional<Map.Entry<String, Object>> findSpecificPromotion(Map<String, Object> promotionMap) {
        return promotionMap.entrySet().stream()
                .filter(entry -> entry.getKey().matches(".*(SECKILL|MINUS|PINTUAN).*"))
                .findFirst();
    }

    /**
     * 处理促销商品信息
     */
    private void processPromotionGoods(GoodsSkuVO goodsSkuDetail, String skuId, Optional<Map.Entry<String, Object>> promotionEntry) {
        if (skuId == null || promotionEntry.isEmpty()) {
            return;
        }

        try {
            JSONObject jsonObject = JSONUtil.parseObj(promotionEntry.get().getValue());
            PromotionGoodsSearchParams searchParams = new PromotionGoodsSearchParams();
            searchParams.setSkuId(skuId);
            searchParams.setPromotionId(jsonObject.getStr("id"));
            PromotionGoods promotionGoods = promotionGoodsClient.getPromotionsGoods(searchParams);

            if (promotionGoods != null && promotionGoods.getPrice() != null) {
                boolean hasStock = isPromotionGoodsHasStock(promotionGoods);
                goodsSkuDetail.setPromotionFlag(true);
                goodsSkuDetail.setPromotionPrice(hasStock ? promotionGoods.getPrice() : null);
                goodsSkuDetail.setPromotionBuy(hasStock);
            }
        } catch (Exception e) {
            log.error("处理促销商品信息失败", e);
            resetPromotionFlags(goodsSkuDetail);
        }
    }

    /**
     * 判断促销商品是否有库存
     */
    private boolean isPromotionGoodsHasStock(PromotionGoods promotionGoods) {
        return promotionGoods.getScopeType().equals(PromotionsScopeTypeEnum.ALL.name())
               || (promotionGoods.getQuantity() != null && promotionGoods.getQuantity() > 0);
    }

    /**
     * 检查促销库存状态
     */
    private void checkPromotionStock(GoodsSkuVO goodsSkuDetail, String skuId, Map<String, Object> promotionMap,
                                     Optional<Map.Entry<String, Object>> promotionEntry) {
        try {
            if (promotionEntry.isPresent()) {
                String promotionType = promotionEntry.get().getKey().split("-")[0];
                JSONObject jsonObject = JSONUtil.parseObj(promotionEntry.get().getValue());
                String stockCacheKey = PromotionGoodsClient.getPromotionGoodsStockCacheKey(
                        PromotionTypeEnum.valueOf(promotionType), jsonObject.getStr("id"), skuId);
                Object stock = cache.get(stockCacheKey);

                // 如果促销库存不为空, 则设置促销库存
                if (stock != null) {
                    goodsSkuDetail.setPromotionQuantity(Integer.parseInt(stock.toString()));

                    // 小于等于0，重置促销状态并移除促销活动
                    if (Integer.parseInt(stock.toString()) <= 0) {
                        resetPromotionFlags(goodsSkuDetail);
                        promotionMap.remove(promotionEntry.get().getKey());
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查促销库存状态失败", e);
            resetPromotionFlags(goodsSkuDetail);
        }
    }

    /**
     * 重置促销标记
     */
    private void resetPromotionFlags(GoodsSkuVO goodsSkuDetail) {
        if (goodsSkuDetail != null) {
            goodsSkuDetail.setPromotionFlag(false);
            goodsSkuDetail.setPromotionPrice(null);
            goodsSkuDetail.setPromotionBuy(false);
        }
    }

    private void checkGoodsBuyerVOPromotion(GoodsBuyerVO goodsBuyerVO) {
        Optional<Map.Entry<String, Object>> containsPromotion =
                goodsBuyerVO.getPromotionMap().entrySet().stream().filter(i -> i.getKey().contains(PromotionTypeEnum.SECKILL.name()) || i.getKey().contains(PromotionTypeEnum.MINUS.name()) || i.getKey().contains(PromotionTypeEnum.PINTUAN.name())).findFirst();
        if (!containsPromotion.isPresent()) {
            goodsBuyerVO.getData().setPromotionPrice(null);
            goodsBuyerVO.getData().setPromotionBuy(false);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteGoods(List<String> goodsIds) {
        Assert.notEmpty(goodsIds, "商品ID不能为空");

        LambdaUpdateWrapper<Goods> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Goods::getMarketEnable, GoodsMarketEnum.DOWN.name());
        updateWrapper.set(Goods::getDeleteFlag, true);
        updateWrapper.set(Goods::getOffShelfTime, new Date());
        if (!UserContext.getCurrentExistUser().getIsManager()) {
            updateWrapper.eq(Goods::getStoreId, UserContext.getCurrentId());
        }
        updateWrapper.nested(s -> s.in(Goods::getId, goodsIds).or().in(Goods::getSupplierGoodsId, goodsIds));
        this.goodsService.update(updateWrapper);

        // 修改规格商品
        LambdaQueryWrapper<Goods> queryWrapper = new LambdaQueryWrapper<>();
        if (!UserContext.getCurrentExistUser().getIsManager()) {
            queryWrapper.eq(Goods::getStoreId, UserContext.getCurrentId());
        }
        queryWrapper.nested(s -> s.in(Goods::getId, goodsIds).or().in(Goods::getSupplierGoodsId, goodsIds));
        List<Goods> goodsList = this.goodsService.list(queryWrapper);
        for (Goods goods : goodsList) {
            // 修改SKU状态
            goodsSkuService.updateGoodsSkuStatus(goods);
        }

        // 删除商品相关的图片文件
        this.deleteGoodsImages(goodsIds);

        // 发送删除es商品的信息
        this.sendDeleteGoods(goodsIds);
        // 发送同步删除分销商品消息
        this.syncDeleteDistributionGoods(goodsIds);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSupplierGoods(List<String> goodsIds) {
        //先删除供应商自生的商品信息
        deleteGoods(goodsIds);
        //操作代理供应商商品的 信息做下架处理
        List<Goods> goodsList =
                this.goodsService.list(new LambdaQueryWrapper<Goods>().in(Goods::getSupplierGoodsId, goodsIds));
        if (goodsList.isEmpty()) {
            return;
        }
        //获取商品列表的商品 ID 字段集合
        List<String> storeGoodsIds = goodsList.stream().map(Goods::getId).toList();

        LambdaUpdateWrapper<Goods> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Goods::getMarketEnable, GoodsMarketEnum.DOWN.name());
        updateWrapper.set(Goods::getUnderMessage, "供应商商品删除，请重新选择其他商品代理。");
        updateWrapper.set(Goods::getOffShelfTime, new Date());
        updateWrapper.in(Goods::getId, storeGoodsIds);
        this.goodsService.update(updateWrapper);
        this.sendDeleteGoods(goodsIds);
    }

    @Override
    @Transactional
    public void editProxyGoods(ProxyGoodsOperationDTO proxyGoodsOperationDTO) {
        // 获取商品系统配置决定是否审核
        Setting setting = settingClient.get(SettingEnum.GOODS_SETTING.name());
        GoodsSetting goodsSetting = JSONUtil.toBean(setting.getSettingValue(), GoodsSetting.class);


        // 校验商品是否存在
        Goods goods = this.goodsService.getById(proxyGoodsOperationDTO.getGoodsId());
        if (goods == null) {
            throw new ServiceException(ResultCode.GOODS_NOT_EXIST);
        }
        // 校验商品是否是代理商品
        if (Boolean.FALSE.equals(goods.getIsProxyGoods())) {
            throw new ServiceException(ResultCode.NORMAL_GOODS_CANT_UPDATE_HEAR);
        }
        // 校验商品是否是自己的商品
        if (!goods.getStoreId().equals(Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId())) {
            throw new ServiceException(ResultCode.GOODS_NOT_EXIST);
        }

        //将商品可编辑的参数复制给商品本身
        BeanUtils.copyProperties(proxyGoodsOperationDTO, goods);
        //是否需要审核
        goods.setAuthFlag(Boolean.TRUE.equals(goodsSetting.getGoodsCheck()) ? GoodsAuthEnum.TOBEAUDITED.name() : GoodsAuthEnum.PASS.name());

        //赋值SKU
        List<GoodsSkuOperationDTO> skus = proxyGoodsOperationDTO.getSkuList();
        List<GoodsSku> goodsSkus = new ArrayList<>();
        for (GoodsSkuOperationDTO skuOperationDTO : skus) {
            if (CharSequenceUtil.isNotBlank(skuOperationDTO.getId())) {
                //校验商品是否存在
                GoodsSku goodsSku = goodsSkuService.getById(skuOperationDTO.getId());
                if (goodsSku == null) {
                    throw new ServiceException(ResultCode.GOODS_NOT_EXIST);
                }
                //校验价格是否小于原价
                double price = skuOperationDTO.getPrice();
                if (price < goodsSku.getCost()) {
                    throw new ServiceException(ResultCode.PROXY_PRICE_CANT_SMALL_THAN_ORIGIN,
                            goodsSku.getSpecs());
                }
                goodsSku.setIntro(goods.getIntro());
                goodsSku.setMobileIntro(goods.getMobileIntro());
                goodsSku.setPrice(skuOperationDTO.getPrice());
                goodsSku.setGoodsName(goods.getGoodsName());
                this.renderImages(goodsSku, skuOperationDTO, goods.getOriginal());
                goodsSku.setAuthFlag(Boolean.TRUE.equals(goodsSetting.getGoodsCheck()) ? GoodsAuthEnum.TOBEAUDITED.name() : GoodsAuthEnum.PASS.name());
                goodsSku.setFreightTemplateId(goods.getTemplateId());
                goodsSku.setStoreCategoryPath(goods.getStoreCategoryPath());
                goodsSkus.add(goodsSku);
            }
        }

        goodsService.updateGoods(goods);
        goodsSkuService.updateBatch(goodsSkus);
        this.sendGenerateGoods(Collections.singletonList(goods.getId()));
    }

    @Override
    public GoodsPageVO getGoodsPage(GoodsSearchParams goodsSearchParams) {
        Page<Goods> page = this.goodsService.queryByParams(goodsSearchParams);
        GoodsPageVO goodsInfoVO = new GoodsPageVO();
        BeanUtils.copyProperties(page, goodsInfoVO);
        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser != null && !currentUser.getScene().equals(SceneEnums.MEMBER)) {
            if (currentUser.getScene().equals(SceneEnums.STORE) || currentUser.getScene().equals(SceneEnums.SUPPLIER)) {
                goodsSearchParams.setStoreId(currentUser.getExtendId());
            }
            GoodsStatusCountVO goodsStatusCount = this.goodsService.countGoodsStatus(goodsSearchParams);
            goodsInfoVO.setStatusCount(goodsStatusCount);
        }

        if (CollectionUtils.isNotEmpty(goodsInfoVO.getRecords())) {
            for (Goods goods : goodsInfoVO.getRecords()) {
                //商品收藏数
                goods.setCollectNum(goodsCollectionClient.countCollectNum(goods.getId()));
                //图片访问数
                long visitNum = footPrintClient.getVisitNum(goods.getId());
                goods.setImageDownloadRate(0d);
                if (goods.getImageDownloadCount() != null) {
                    Long sun = goods.getImageDownloadCount() + visitNum;
                    NumberFormat numberFormat = NumberFormat.getInstance();
                    numberFormat.setMaximumFractionDigits(2);
                    String result1 = numberFormat.format((float) goods.getImageDownloadCount() / (float) sun * 100);
                    goods.setImageDownloadRate(Double.valueOf(result1));
                }
            }
        }
        return goodsInfoVO;
    }

    @Override
    @Transactional
    public void updateStocks(List<GoodsSku> goodsSkuList) {
        for (GoodsSku goodsSku : goodsSkuList) {
            this.updateStock(goodsSku.getId(), goodsSku.getQuantity());
        }
    }

    @Override
    @Transactional
    public void updateStock(String skuId, Integer quantity) {
        GoodsSku goodsSku = this.getGoodsSkuByIdFromCache(skuId);
        if (goodsSku != null) {
            goodsSku.setQuantity(quantity);
            executeUpdateGoodsStock(goodsSku, quantity);
        }
    }

    /**
     * 执行更新商品库存
     *
     * @param quantity 库存数量
     * @param goodsSku 商品sku
     */
    private void executeUpdateGoodsStock(GoodsSku goodsSku, Integer quantity) {
        this.goodsSkuService.update(new LambdaUpdateWrapper<GoodsSku>().eq(GoodsSku::getId, goodsSku.getId()).set(GoodsSku::getQuantity, quantity));
        cache.put(GoodsSkuClient.getStockCacheKey(goodsSku), quantity);

        this.promotionGoodsClient.updatePromotionGoodsStock(goodsSku.getId(), quantity);
        //商品库存为0是删除商品索引
        if (quantity <= 0) {
            goodsIndexService.deleteIndexById(goodsSku.getId());
        }
    }

    @Override
    public void updateStock(String skuId, Integer quantity, String type) {
        GoodsSku goodsSku = this.getGoodsSkuByIdFromCache(skuId);
        if (goodsSku != null) {

            //计算修改库存
            if (type.equals(GoodsStockTypeEnum.ADD.name())) {
                quantity = Convert.toInt(NumberUtil.add(goodsSku.getQuantity(), quantity));
            } else {
                quantity = Convert.toInt(NumberUtil.sub(goodsSku.getQuantity(), quantity));
            }
            goodsSku.setQuantity(quantity);

            //判断商品sku是否已经下架(修改商品库存为0时  会自动下架商品),再次更新商品库存时 需更新商品索引
            boolean isFlag = goodsSku.getQuantity() <= 0;

            executeUpdateGoodsStock(goodsSku, quantity);
            //商品SKU库存为0并且商品sku状态为上架时更新商品库存
            if (isFlag && CharSequenceUtil.equals(goodsSku.getMarketEnable(), GoodsMarketEnum.UPPER.name())) {
                List<String> goodsIds = new ArrayList<>();
                goodsIds.add(goodsSku.getGoodsId());
                applicationEventPublisher.publishEvent(
                        TransactionCommitSendMQEvent.builder()
                                .source("更新商品")
                                .exchange(amqpExchangeProperties.getGoods())
                                .routingKey(GoodsRoutingKey.UPDATE_GOODS_INDEX)
                                .message(goodsIds)
                                .build());
            }
        }
    }

    @Override
    @Transactional
    public void batchUpdateGoods(BatchUpdateGoodsDTO batchUpdateGoodsDTO) {
        //校验参数
        batchUpdateGoodsDTO.checkParams();
        if (CollUtil.isEmpty(batchUpdateGoodsDTO.getGoodsIds())) {
            List<Goods> goods = goodsService.queryListByParams(GoodsSearchParams.builder().storeId(batchUpdateGoodsDTO.getStoreId()).build());
            // 商铺没有发布商品无需后续操作
            if (goods == null || goods.isEmpty()) {
                return;
            }
            batchUpdateGoodsDTO.setGoodsIds(goods.stream().map(Goods::getId).toList());
        }
        //修改规格商品
        LambdaQueryWrapper<Goods> queryWrapper = new LambdaQueryWrapper<>();
        //
        queryWrapper.in(!batchUpdateGoodsDTO.getGoodsIds().isEmpty(), Goods::getId, batchUpdateGoodsDTO.getGoodsIds());
        queryWrapper.like(CharSequenceUtil.isNotEmpty(batchUpdateGoodsDTO.getCategoryId()), Goods::getCategoryPath,
                batchUpdateGoodsDTO.getCategoryId());
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(batchUpdateGoodsDTO.getStoreId()), Goods::getStoreId,
                batchUpdateGoodsDTO.getStoreId());

        List<Goods> goodsList = this.goodsService.list(queryWrapper);

        if (CollUtil.isEmpty(goodsList)) {
            return;
        }
        switch (batchUpdateGoodsDTO.getOperationType()) {
            case 1:
                //上架
                this.editMarketEnum(goodsList, GoodsMarketEnum.UPPER, null);
                break;
            case 2:
                //下架
                this.editMarketEnum(goodsList, GoodsMarketEnum.DOWN, batchUpdateGoodsDTO.getUnderReason());
                break;
            default:
                break;
        }
    }

    @Override
    public void updateStocksByType(List<GoodsSkuStockDTO> goodsSkuStockDTOS) {
        // 获取所有的goodsID，并去除相同的goodsID
        List<String> goodsIds = goodsSkuStockDTOS.stream()
                .map(GoodsSkuStockDTO::getGoodsId)
                .distinct()
                .toList();

        //更新SKU库存
        for (GoodsSkuStockDTO goodsSkuStockDTO : goodsSkuStockDTOS) {
            this.updateStock(goodsSkuStockDTO.getSkuId(), goodsSkuStockDTO.getQuantity(), goodsSkuStockDTO.getType());
        }
        //更新SPU库存
        for (String goodsId : goodsIds) {
            goodsService.syncStock(goodsId);
        }
    }

    /**
     * 更新商品状态
     *
     * @param goodsList 商品列表
     */
    private void editMarketEnum(List<Goods> goodsList, GoodsMarketEnum goodsMarketEnum, String reason) {
        List<String> goodsIds = goodsList.stream().map(Goods::getId).toList();
        Map<String, Object> query = new HashMap<>();
        Map<String, Object> update = new HashMap<>();


        update.put("marketEnable", goodsMarketEnum.name());

        LambdaUpdateWrapper<Goods> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Goods::getMarketEnable, goodsMarketEnum.name());
        updateWrapper.set(CharSequenceUtil.isNotEmpty(reason), Goods::getUnderMessage, reason);

        // 记录下架时间或清空下架时间
        if (goodsMarketEnum == GoodsMarketEnum.DOWN) {
            updateWrapper.set(Goods::getOffShelfTime, new Date());
        } else if (goodsMarketEnum == GoodsMarketEnum.UPPER) {
            updateWrapper.set(Goods::getOffShelfTime, null);
        }

        if (!UserContext.getCurrentExistUser().getIsManager()) {
            updateWrapper.eq(Goods::getStoreId, UserContext.getCurrentId());
            query.put("storeId", UserContext.getCurrentId());
        }
        updateWrapper.in(Goods::getId, goodsIds);
        this.goodsService.update(updateWrapper);

        // 更新sku状态
        LambdaUpdateWrapper<GoodsSku> updateSkuWrapper = new LambdaUpdateWrapper<>();
        updateSkuWrapper.set(GoodsSku::getMarketEnable, goodsMarketEnum.name());
        updateSkuWrapper.set(CharSequenceUtil.isNotEmpty(reason), GoodsSku::getUnderMessage, reason);
        if (!UserContext.getCurrentExistUser().getIsManager()) {
            updateSkuWrapper.eq(GoodsSku::getStoreId, UserContext.getCurrentId());
        }
        updateSkuWrapper.in(GoodsSku::getGoodsId, goodsIds);
        this.goodsSkuService.update(updateSkuWrapper);

        // 发送生成商品索引信息
        // 根据 scene 分组
        goodsList.stream().collect(Collectors.groupingBy(Goods::getScene)).forEach((scene, goods) -> {
            query.put("goodsId", goods.stream().map(Goods::getId).toList());
            if (SceneEnums.SUPPLIER.name().equals(scene)) {
                this.updateGoodsEs(query, update, EsSupplierGoodsIndex.class);
            } else {
                this.updateGoodsEs(query, update, EsGoodsIndex.class);
            }
        });
    }

    /**
     * 生成商品信息
     *
     * @param goodsIds 商品id集合
     * @param storeId  店铺id
     */
    public void generatorGoods(List<String> goodsIds, String storeId) {
        if (CollUtil.isNotEmpty(goodsIds)) {
            // 发送生成商品信息
            this.sendGenerateGoods(goodsIds);
        } else if (CharSequenceUtil.isNotEmpty(storeId)) {
            // 发送生成店铺商品信息
            applicationEventPublisher.publishEvent(TransactionCommitSendMQEvent.builder().source("生成店铺商品")
                    .exchange(amqpExchangeProperties.getGoods()).routingKey(GoodsRoutingKey.GENERATOR_STORE_GOODS_INDEX)
                    .message(storeId).build());
        }
    }

    /**
     * 发送生成ES商品索引
     *
     * @param goodIds 商品id集合
     */
    private void sendGenerateGoods(List<String> goodIds) {
        applicationEventPublisher.publishEvent(
                TransactionCommitSendMQEvent.builder().source("生成商品").exchange(amqpExchangeProperties.getGoods())
                        .routingKey(GoodsRoutingKey.GENERATOR_GOODS_INDEX).message(goodIds).build());

    }

    private void syncDistributionGoods(List<String> goodIds) {
        applicationEventPublisher.publishEvent(
                TransactionCommitSendMQEvent.builder().source("同步分销商品").exchange(amqpExchangeProperties.getGoods())
                        .routingKey(GoodsRoutingKey.SYNC_DISTRIBUTION_GOODS).message(goodIds).build());
    }

    private void syncDeleteDistributionGoods(List<String> goodIds) {
        applicationEventPublisher.publishEvent(
                TransactionCommitSendMQEvent.builder().source("同步删除分销商品").exchange(amqpExchangeProperties.getGoods())
                        .routingKey(GoodsRoutingKey.DELETE_DISTRIBUTION_GOODS).message(goodIds).build());
    }

    private void syncDeleteKanjiaGoods(List<String> goodIds) {
        applicationEventPublisher.publishEvent(
                TransactionCommitSendMQEvent.builder().source("同步关闭砍价商品").exchange(amqpExchangeProperties.getPromotion())
                        .routingKey(PromotionRoutingKey.KANJIA_ACTIVITY_GOODS_DELETE).message(goodIds).build());
    }

    /**
     * 发送删除商品索引的信息
     *
     * @param goodIds 商品id集合
     */
    public void sendDeleteGoods(List<String> goodIds) {

        applicationEventPublisher.publishEvent(
                TransactionCommitSendMQEvent.builder().source("删除商品索引").exchange(amqpExchangeProperties.getGoods())
                        .routingKey(GoodsRoutingKey.GOODS_DELETE).message(goodIds).build());

    }

    public void updateGoodsEs(Map<String, Object> query, Map<String, Object> update, Class clazz) {

        //修改规格索引,发送mq消息
        EsGoodsIndexUpdateDTO esGoodsIndexUpdateDTO = new EsGoodsIndexUpdateDTO();
        esGoodsIndexUpdateDTO.setClazz(clazz);
        esGoodsIndexUpdateDTO.setQueryFields(query);
        esGoodsIndexUpdateDTO.setUpdateFields(update);


        amqpSender.send(
                AmqpMessage.builder()
                        .exchange(amqpExchangeProperties.getGoods())
                        .routingKey(GoodsRoutingKey.UPDATE_GOODS_INDEX_FIELD)
                        .message(JSONUtil.toJsonStr(esGoodsIndexUpdateDTO))
                        .build()
        );
    }

    /**
     * 添加商品默认图片
     *
     * @param origin 图片
     * @param goods  基础商品
     */
    private void setGoodsGalleryParam(String origin, Goods goods) {
        //供应商商品，图片切割规律不明，暂不处理
        goods.setOriginal(origin);
        goods.setSmall(origin);
        goods.setThumbnail(origin);
    }

    /**
     * 封装商品
     *
     * @param goodsOperationDTO 商品操作DTO
     * @return 商品
     */
    private Goods wrapperGoods(GoodsOperationDTO goodsOperationDTO, Goods goods) {
        Goods newGoods;
        // 如果商品是供应商商品，则只可以修改部分字段
        if (Boolean.TRUE.equals(goods.getIsProxyGoods())) {
            newGoods = goods.supplierGoods(goodsOperationDTO);
        } else {
            newGoods = wrapperGoods(goodsOperationDTO);
        }
        return newGoods;

    }

    /**
     * 封装商品
     *
     * @param goodsOperationDTO 商品操作DTO
     * @return 商品
     */
    private Goods wrapperGoods(GoodsOperationDTO goodsOperationDTO) {
        // 是否存在规格
        if (goodsOperationDTO.getSkuList().isEmpty()) {
            throw new ServiceException(ResultCode.MUST_HAVE_GOODS_SKU);
        }
        // 存在一种情况，如果供应商发布商品，价格可能不存在价格字段交由其他模块处理
        if (!(goodsOperationDTO instanceof SupplierGoodsOperationDTO)) {
            if (SalesModeEnum.WHOLESALE.name().equals(goodsOperationDTO.getSalesModel())) {
                goodsOperationDTO.setPrice(goodsOperationDTO.getWholesaleList().stream().map(Wholesale::getPrice)
                        .min(Comparator.comparing(Double::doubleValue)).orElse(0.0D));
            } else {
                goodsOperationDTO.setPrice(goodsOperationDTO.getSkuList().stream().map(GoodsSkuOperationDTO::getPrice)
                        .min(Comparator.comparing(Double::doubleValue)).orElse(0.0D));
            }
        }

        // 商品信息初始化
        Goods goods = new Goods(goodsOperationDTO);
        // 店铺信息填充
        this.storeInformationHandler(goods);
        // 添加相册
        if (goodsOperationDTO.getGoodsGalleryList() != null && !goodsOperationDTO.getGoodsGalleryList().isEmpty()) {
            //向goods加入图片
            this.setGoodsGalleryParam(goodsOperationDTO.getGoodsGalleryList().getFirst(), goods);
        }
        // 添加商品参数
        if (goodsOperationDTO.getGoodsParamsDTOList() != null && !goodsOperationDTO.getGoodsParamsDTOList().isEmpty()) {
            goods.setParams(JSONUtil.toJsonStr(goodsOperationDTO.getGoodsParamsDTOList()));
        }

        SceneHelp.objectHandler(goods);

        this.checkGoodsOtherTableData(goods);
        return goods;
    }

    /**
     * 封装供应商商品
     *
     * @param supplierGoodsOperationDTO 供应商商品操作DTO
     * @return 供应商商品
     */
    private Goods wrapperGoods(SupplierGoodsOperationDTO supplierGoodsOperationDTO) {
        Goods goods = wrapperGoods((GoodsOperationDTO) supplierGoodsOperationDTO);
        goods.setSupportPurchase(supplierGoodsOperationDTO.getSupportPurchase());
        goods.setSupportProxy(supplierGoodsOperationDTO.getSupportProxy());
        if (Boolean.TRUE.equals(supplierGoodsOperationDTO.getSupportPurchase())) {
            goods.setPurchaseRule(supplierGoodsOperationDTO.getPurchaseRule().name());
        }
        goods.setTemplateId(supplierGoodsOperationDTO.getTemplateId());
        if (goods.getPrice() == null && (supplierGoodsOperationDTO.getWholesaleList() != null
                                         && !supplierGoodsOperationDTO.getWholesaleList().isEmpty())) {
            goods.setPrice(supplierGoodsOperationDTO.getWholesaleList().getFirst().getPrice());
        }
        goods.setWholesaleList(supplierGoodsOperationDTO.getWholesaleList());

        // 店铺信息填充
        this.storeInformationHandler(goods);
        return goods;
    }

    /**
     * 店铺信息填充
     *
     * @param goods 商品
     */
    private void storeInformationHandler(Goods goods) {
        StoreVO store = storeClient.getStore(Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId());
        if (store.getSelfOperated() != null) {
            goods.setSelfOperated(store.getSelfOperated());
        }
        goods.setStoreId(store.getId());
        goods.setStoreName(store.getStoreName());
        goods.setSelfOperated(store.getSelfOperated());
        goods.setStoreLogo(store.getStoreLogo());
        //获取商品系统配置决定是否审核
        Setting setting = settingClient.get(SettingEnum.GOODS_SETTING.name());
        GoodsSetting goodsSetting = JSONUtil.toBean(setting.getSettingValue(), GoodsSetting.class);
        //是否需要审核
        goods.setAuthFlag(Boolean.TRUE.equals(goodsSetting.getGoodsCheck()) ? GoodsAuthEnum.TOBEAUDITED.name()
                : GoodsAuthEnum.PASS.name());
    }

    /**
     * 生成商品处理业务，比如sku构造，相册构造等
     *
     * @param goods             商品信息
     * @param goodsOperationDTO 供应商商品信息
     * @return sku信息
     */
    private List<GoodsSku> generateGoodsHandler(Goods goods, GoodsOperationDTO goodsOperationDTO) {

        //sku 信息
        List<GoodsSku> skuList;

        // 重新生成sku
        skuList = GoodsSkuBuilder.buildBatch(goods, goodsOperationDTO.getSkuList());
        // 渲染sku图片
        for (int i = 0; i < skuList.size(); i++) {
            skuList.get(i).setGoodsGallery(String.join(",", goodsOperationDTO.getGoodsGalleryList()));
            this.renderImages(skuList.get(i), goodsOperationDTO.getSkuList().get(i), goodsOperationDTO.getGoodsGalleryList().getFirst());
        }
        this.extendSkuInformation(skuList);

        // 如果支持采购，并且配置有采购规则
        if (SalesModeEnum.WHOLESALE.name().equals(goodsOperationDTO.getSalesModel())
            && CollUtil.isNotEmpty(goodsOperationDTO.getWholesaleList())) {
            this.renderGoodsSkuWholesale(skuList, goodsOperationDTO);
        }

        // 删除原sku信息
        this.goodsSkuService.deleteByGoodsId(goods.getId());
        this.sendDeleteGoods(Collections.singletonList(goods.getId()));

        // 保存sku信息
        this.goodsSkuService.saveBatch(skuList);

        // 更新商品sku库存信息
        this.goodsSkuService.updateStock(skuList);

        //更新商品库存信息
        this.goodsService.updateStock(skuList);

        // 添加商品图片
        if (goodsOperationDTO.getGoodsGalleryList() != null && !goodsOperationDTO.getGoodsGalleryList().isEmpty()) {
            this.goodsGalleryService.add(goodsOperationDTO.getGoodsGalleryList(), goods.getId());
        }
        return skuList;
    }

    /**
     * 继承 sku 信息
     *
     * @param goodsSkuList sku集合
     */
    private void extendSkuInformation(List<GoodsSku> goodsSkuList) {
        for (GoodsSku goodsSku : goodsSkuList) {
            if (CharSequenceUtil.isNotEmpty(goodsSku.getGoodsId())) {
                GoodsSku oldSku = this.goodsSkuService.getById(goodsSku.getId());
                if (oldSku != null) {
                    goodsSku.setCommentNum(oldSku.getCommentNum());
                    goodsSku.setViewCount(oldSku.getViewCount());
                    goodsSku.setBuyCount(oldSku.getBuyCount());
                    goodsSku.setGrade(oldSku.getGrade());
                }
            }
        }
    }

    /**
     * 渲染sku批发规则
     *
     * @param goodsSkus         sku集合
     * @param goodsOperationDTO 商品操作DTO
     */
    private void renderGoodsSkuWholesale(List<GoodsSku> goodsSkus, GoodsOperationDTO goodsOperationDTO) {
        if (goodsOperationDTO.getSalesModel().equals(SalesModeEnum.WHOLESALE.name())) {
            if (CollUtil.isEmpty(goodsOperationDTO.getWholesaleList())) {
                throw new ServiceException(ResultCode.MUST_HAVE_SALES_MODEL);
            }
            //获取商品最高价格
            List<Wholesale> collect =
                    goodsOperationDTO.getWholesaleList().stream().sorted(Comparator.comparing(Wholesale::getPrice).reversed())
                            .toList();
            this.checkWholesaleList(collect);
            for (GoodsSku skus : goodsSkus) {
                skus.setPrice(collect.getFirst().getPrice());
                skus.setCost(collect.getFirst().getPrice());
            }
        }
    }

    /**
     * 校验批发规则
     *
     * @param wholesaleList 批发规则
     */
    private void checkWholesaleList(List<Wholesale> wholesaleList) {
        for (Wholesale wholesale : wholesaleList) {
            if (wholesale.getPrice() == null || wholesale.getPrice() <= 0 || wholesale.getNum() == null
                || wholesale.getNum() <= 0) {
                throw new ServiceException(ResultCode.HAVE_INVALID_SALES_MODEL);
            }
        }
    }

    /**
     * 渲染sku图片
     * <p> 如果sku没有图片，则使用商品图片
     *
     * @param goodsSku        sku
     * @param skuOperationDTO sku操作DTO
     * @param origin          商品图片
     */
    private void renderImages(GoodsSku goodsSku, GoodsSkuOperationDTO skuOperationDTO, String origin) {
        GoodsGallery goodsGallery;
        if (CharSequenceUtil.isEmpty(skuOperationDTO.getImages())) {
            goodsGallery = goodsGalleryService.getGoodsGallery(origin);
        } else {
            goodsGallery = goodsGalleryService.getGoodsGallery(skuOperationDTO.getImages());
            if (CharSequenceUtil.isNotEmpty(goodsSku.getGoodsGallery())) {
                goodsSku.setGoodsGallery(skuOperationDTO.getImages() + "," + goodsSku.getGoodsGallery());
            } else {
                goodsSku.setGoodsGallery(skuOperationDTO.getImages());
            }
        }

        goodsSku.setBig(goodsGallery.getOriginal());
        goodsSku.setOriginal(goodsGallery.getOriginal());
        goodsSku.setThumbnail(goodsGallery.getThumbnail());
        goodsSku.setSmall(goodsGallery.getSmall());
    }

    /**
     * 商品规格校验
     *
     * @param spec 规格
     * @return 是否通过
     */
    public Boolean checkSpec(String spec) {

        if (CharSequenceUtil.isEmpty(spec)) {
            return false;
        }
        String regex = "^([^:]{1,15}:[^,]{1,15}(,|$)){1,3}$";

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(spec);

        return matcher.matches();
    }

    @Override
    public List<GoodsSku> getGoodsSkuByIdFromCache(List<String> ids) {
        List<GoodsSku> goodsSkuList = new ArrayList<>();
        for (String id : ids) {
            GoodsSku goodsSku = this.getGoodsSkuByIdFromCache(id);
            if (goodsSku != null) {
                goodsSkuList.add(goodsSku);
            }
        }

        return goodsSkuList;
    }


    @Override
    public GoodsSku getGoodsSkuByIdFromCache(String id) {

        GoodsSku goodsSku = (GoodsSku) cache.get(CachePrefix.GOODS_SKU.getPrefix(id));
        if (goodsSku == null) {
            EsGoodsIndex esGoodsIndexById = this.goodsIndexService.findEsGoodsIndexById(id);

            if (esGoodsIndexById == null) {
                return null;
            }

            goodsSku = new GoodsSku();
            BeanUtils.copyProperties(esGoodsIndexById, goodsSku);
            if (esGoodsIndexById.getFreightTemplate() != null) {
                goodsSku.setFreightTemplateId(esGoodsIndexById.getFreightTemplate().getId());
            }
            if (goodsSku.getUpdateTime() == null) {
                goodsSku.setUpdateTime(esGoodsIndexById.getReleaseTime());
            }

            cache.put(CachePrefix.GOODS_SKU.getPrefix(id), goodsSku);
        }

        //获取商品库存
        Integer integer = (Integer) cache.get(GoodsSkuClient.getStockCacheKey(goodsSku));

        //库存不为空,库存与缓存中不一致
        if (integer != null && !goodsSku.getQuantity().equals(integer)) {
            //写入最新的库存信息
            goodsSku.setQuantity(integer);
        }
        return goodsSku;
    }

    /**
     * 检查商品是否在装修页面中被使用
     *
     * @param goodsIds 商品ID列表
     */
    private void checkGoodsUsedInPageData(List<String> goodsIds) {
        if (CollUtil.isEmpty(goodsIds)) {
            return;
        }

        try {
            // 获取所有启用的页面数据
            List<cn.lili.modules.page.entity.dos.PageData> pageDataList = pageDataClient.getEnabledPageDataList(null);

            if (CollUtil.isEmpty(pageDataList)) {
                return;
            }

            // 检查每个页面的 pageData 内容
            for (cn.lili.modules.page.entity.dos.PageData pageData : pageDataList) {
                if (CharSequenceUtil.isEmpty(pageData.getPageData())) {
                    continue;
                }

                String pageDataContent = pageData.getPageData();

                // 检查页面数据中是否包含要下架的商品ID
                for (String goodsId : goodsIds) {
                    if (pageDataContent.contains(goodsId)) {
                        // 获取商品名称用于错误提示
                        Goods goods = goodsService.getById(goodsId);
                        String goodsName = goods != null ? goods.getGoodsName() : goodsId;

                        throw new ServiceException(ResultCode.GOODS_USED_IN_PAGE_DECORATION,
                            String.format("商品【%s】正在装修页面【%s】中使用，不允许下架", goodsName, pageData.getName()));
                    }
                }
            }
        } catch (ServiceException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("检查商品是否在装修页面中被使用时发生异常", e);
            // 如果检查过程中发生异常，为了安全起见，不允许下架
            throw new ServiceException(ResultCode.ERROR, "检查商品装修使用状态时发生异常，请稍后重试");
        }
    }

    /**
     * 删除商品相关的图片文件
     *
     * @param goodsIds 商品ID列表
     */
    private void deleteGoodsImages(List<String> goodsIds) {
        if (CollUtil.isEmpty(goodsIds)) {
            return;
        }

        try {
            log.info("开始删除商品图片，商品ID：{}", goodsIds);

            // 1. 收集要删除的商品的所有图片URL
            List<String> imageUrls = new ArrayList<>();

            // 获取商品信息
            List<Goods> goodsList = goodsService.listByIds(goodsIds);
            for (Goods goods : goodsList) {
                // 收集商品缩略图
                if (CharSequenceUtil.isNotEmpty(goods.getThumbnail())) {
                    imageUrls.add(goods.getThumbnail());
                }

                // 收集商品小图
                if (CharSequenceUtil.isNotEmpty(goods.getSmall())) {
                    imageUrls.add(goods.getSmall());
                }

                // 收集商品原图
                if (CharSequenceUtil.isNotEmpty(goods.getOriginal())) {
                    imageUrls.add(goods.getOriginal());
                }

                // 收集图片资源包
                if (CharSequenceUtil.isNotEmpty(goods.getImageZipFile())) {
                    imageUrls.add(goods.getImageZipFile());
                }
            }

            // 获取商品相册图片
            List<GoodsGallery> galleryList = goodsGalleryService.list(
                new LambdaQueryWrapper<GoodsGallery>().in(GoodsGallery::getGoodsId, goodsIds)
            );
            for (GoodsGallery gallery : galleryList) {
                if (CharSequenceUtil.isNotEmpty(gallery.getThumbnail())) {
                    imageUrls.add(gallery.getThumbnail());
                }
                if (CharSequenceUtil.isNotEmpty(gallery.getSmall())) {
                    imageUrls.add(gallery.getSmall());
                }
                if (CharSequenceUtil.isNotEmpty(gallery.getOriginal())) {
                    imageUrls.add(gallery.getOriginal());
                }
            }

            List<GoodsSku> skuList = goodsSkuService.list(new LambdaQueryWrapper<GoodsSku>().in(GoodsSku::getGoodsId, goodsIds));
            for (GoodsSku sku : skuList) {
                if (CharSequenceUtil.isNotEmpty(sku.getThumbnail())) {
                    imageUrls.add(sku.getThumbnail());
                }
                if (CharSequenceUtil.isNotEmpty(sku.getSmall())) {
                    imageUrls.add(sku.getSmall());
                }
                if (CharSequenceUtil.isNotEmpty(sku.getOriginal())) {
                    imageUrls.add(sku.getOriginal());
                }
                if (CharSequenceUtil.isNotEmpty(sku.getBig())) {
                    imageUrls.add(sku.getBig());
                }
            }

            // 去重
            imageUrls = imageUrls.stream().distinct().collect(Collectors.toList());

            if (CollUtil.isEmpty(imageUrls)) {
                log.info("商品{}没有图片需要删除", goodsIds);
                return;
            }

            log.info("收集到商品图片URL：{}", imageUrls);

            // 2. 检查这些图片是否在其他商品中被使用
            // List<String> usedImageUrls = uploadClient.checkFileUsageInGoods(imageUrls, goodsIds);

            // 3. 过滤出可以删除的图片
            // List<String> canDeleteUrls = imageUrls.stream()
            //     .filter(url -> !usedImageUrls.contains(url))
            //     .collect(Collectors.toList());

            if (CollUtil.isEmpty(imageUrls)) {
                log.info("所有图片都在其他商品中被使用，跳过删除");
                return;
            }

            log.info("可以删除的图片URL：{}", imageUrls);

            // 4. 根据URL获取文件信息
            List<cn.lili.modules.file.entity.File> files = uploadClient.getFilesByUrls(imageUrls);

            if (CollUtil.isEmpty(files)) {
                log.warn("未找到对应的文件记录：{}", imageUrls);
                return;
            }

            // 5. 提取文件key并删除
            List<String> fileKeys = files.stream()
                .map(cn.lili.modules.file.entity.File::getFileKey)
                .filter(CharSequenceUtil::isNotEmpty)
                .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(fileKeys)) {
                uploadClient.deleteFiles(fileKeys);
                log.info("成功删除商品图片文件：{}", fileKeys);
            }

        } catch (Exception e) {
            log.error("删除商品图片时发生异常，商品ID：{}", goodsIds, e);
            // 图片删除失败不影响商品删除流程
        }
    }
}
