package cn.lili.modules.order.order.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 发货单打印VO
 * 包含发货单打印所需的所有信息
 *
 * <AUTHOR>
 * @since 2025/01/30
 */
@Data
@NoArgsConstructor
@Schema(title = "发货单打印VO")
public class DeliveryNotePrintVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(title = "订单编号")
    private String orderSn;

    @Schema(title = "下单时间")
    private String createTime;

    @Schema(title = "商家名称")
    private String storeName;

    @Schema(title = "商品信息列表")
    private List<DeliveryGoodsInfo> goodsList;

    /**
     * 发货商品信息
     */
    @Data
    @NoArgsConstructor
    @Schema(title = "发货商品信息")
    public static class DeliveryGoodsInfo implements Serializable {
        
        private static final long serialVersionUID = 1L;

        @Schema(title = "SKU名称")
        private String goodsName;

        @Schema(title = "货号")
        private String skuId;

        @Schema(title = "数量")
        private Integer num;

        @Schema(title = "规格信息")
        private String specs;
    }
}
