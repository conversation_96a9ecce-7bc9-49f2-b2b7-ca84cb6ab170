package cn.lili.modules.order.cart.render;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.cache.Cache;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.utils.CurrencyUtil;
import cn.lili.modules.goods.entity.enums.GoodsTypeEnum;
import cn.lili.modules.member.client.UserAddressClient;
import cn.lili.modules.member.entity.dos.UserAddress;
import cn.lili.modules.member.entity.dto.AddressSearchParams;
import cn.lili.modules.member.entity.enums.AddressTypeEnum;
import cn.lili.modules.order.cart.entity.dto.TradeDTO;
import cn.lili.modules.order.cart.entity.enums.CartSceneEnum;
import cn.lili.modules.order.cart.entity.enums.DeliveryMethodEnum;
import cn.lili.modules.order.cart.entity.enums.RenderStepEnums;
import cn.lili.modules.order.cart.entity.vo.CartSkuVO;
import cn.lili.modules.order.cart.entity.vo.TradeParams;
import cn.lili.modules.order.cart.service.CartHandler;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.Trade;
import cn.lili.modules.order.order.entity.dto.FreightDTO;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.order.order.service.TradeService;

import java.util.*;
import java.util.stream.Collectors;

import cn.lili.modules.store.client.FreightTemplateClient;
import cn.lili.modules.store.entity.dos.FreightTemplateChild;
import cn.lili.modules.store.entity.dto.FreightTemplateChildDTO;
import cn.lili.modules.store.entity.enums.FreightTemplateEnum;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 交易构造&&创建
 *
 * <AUTHOR>
 * @since 2020-04-01 9:47 下午
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TradeBuilder {

    /**
     * 购物车渲染步骤
     */
    private final List<CartRenderStep> cartRenderSteps;
    /**
     * 交易
     */
    private final TradeService tradeService;
    /**
     * 交易
     */
    private final OrderService orderService;

    private final UserAddressClient userAddressClient;
    private final Cache<Object> cache;

    private final FreightTemplateClient freightTemplateClient;


    /**
     * 构造购物车 购物车与结算信息不一致的地方主要是优惠券计算和运费计算，其他规则都是一致都
     *
     * @param checkedWay 购物车类型
     * @return 购物车展示信息
     */
    public TradeDTO buildCart(CartSceneEnum checkedWay) {
        //读取对应购物车的商品信息
        TradeDTO tradeDTO = this.readDTO(checkedWay);
        tradeDTO.setScene(CartSceneEnum.CART.name());

        //购物车需要将交易中的优惠券取消掉
        if (checkedWay.equals(CartSceneEnum.CART)) {
            tradeDTO.setStoreCoupons(null);
            tradeDTO.setPlatformCoupon(null);
            tradeDTO.setServiceFeeDTOList(null);
        }

        if(tradeDTO.getSkuList().isEmpty()){
            return tradeDTO;
        }
        //按照计划进行渲染
        renderCartBySteps(tradeDTO, RenderStepStatement.cartRender);
        return tradeDTO;
    }

    /**
     * 构造结算页面
     */
    public TradeDTO buildChecked(CartSceneEnum checkedWay) {
        try {
            //读取选中的列表
            //读取对应购物车的商品信息
            TradeDTO tradeDTO = this.readDTO(checkedWay);
            tradeDTO.setScene(null);

            // 根据默认地址获取运费模版列表
            if (tradeDTO.getUserAddress() != null) {
                buildFreightList(tradeDTO, tradeDTO.getUserAddress());
            }

            //需要对购物车渲染
            renderCartBySteps(tradeDTO, RenderStepStatement.checkedRender);

            return tradeDTO;
        } catch (ServiceException se) {
            log.error(se.getMsg(), se);
            throw se;
        } catch (Exception e) {
            log.error(ResultCode.CART_ERROR.message(), e);
            throw new ServiceException(ResultCode.CART_ERROR);
        }
    }


    /**
     * 创建一笔交易 1.构造交易 2.创建交易
     *
     * @param tradeDTO 交易模型
     * @return 交易信息
     */
    public Trade createTrade(TradeDTO tradeDTO) {

        if (GoodsTypeEnum.VIRTUAL_GOODS.name().equals(tradeDTO.getCheckedSkuList().getFirst().getGoodsSku().getGoodsType())) {
            renderCartBySteps(tradeDTO, RenderStepStatement.tradeVirtualRender);
        } else {
            renderCartBySteps(tradeDTO, RenderStepStatement.tradeRender);
        }
        //添加order订单及order_item子订单并返回
        return tradeService.createTrade(tradeDTO);
    }

    /**
     * 获取订单实际支付的总金额
     *
     * @param orderSn 订单sn
     * @return 金额
     */
    public Double getPaymentTotal(String orderSn) {
        Order order = orderService.getBySn(orderSn);
        return order.getFlowPrice();
    }

    /**
     * 获取整笔交易
     *
     * @param way 购物车类型
     * @return 购物车视图
     */
    public TradeDTO readDTO(CartSceneEnum way) {
        TradeDTO tradeDTO = (TradeDTO) cache.get(this.getOriginKey(way));
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        if (tradeDTO == null) {
            tradeDTO = new TradeDTO(way);
            tradeDTO.setMemberId(currentUser.getExtendId());
            tradeDTO.setMemberName(currentUser.getNickName());
        }
        if (tradeDTO.getUserAddress() == null && tradeDTO.getDeliveryMethodEnum()
            .equals(DeliveryMethodEnum.LOGISTICS)) {
            AddressSearchParams addressSearchParams = new AddressSearchParams();
            addressSearchParams.setExtendId(currentUser.getExtendId());
            addressSearchParams.setType(AddressTypeEnum.RECEIVE);
            addressSearchParams.setIsDefault(true);
            tradeDTO.setUserAddress(this.userAddressClient.getByParams(addressSearchParams));
        }
        return tradeDTO;
    }

    /**
     * 创建交易
     * 1.获取购物车类型，不同的购物车类型有不同的订单逻辑
     * 购物车类型：购物车、立即购买、虚拟商品、拼团、积分
     * 2.校验用户的收件人信息
     * 3.设置交易的基础参数
     * 4.交易信息存储到缓存中
     * 5.创建交易
     * 6.清除购物车选择数据
     *
     * @param tradeParams 创建交易参数
     * @return 交易信息
     */
    public Trade createTrade(TradeParams tradeParams) {
        try {
            //获取购物车
            CartSceneEnum cartSceneEnum = CartSceneEnum.getCartType(tradeParams.getWay());
            TradeDTO tradeDTO = this.readDTO(cartSceneEnum);
            if (null != tradeDTO.getNeedServiceFee() && tradeDTO.getNeedServiceFee() && CharSequenceUtil.isEmpty(tradeDTO.getFreightTemplateId())) {
                throw new ServiceException(ResultCode.FREIGHT_NOT_SELECTED);
            }
            //设置基础属性
            tradeDTO.setClientType(tradeParams.getClient());
            tradeDTO.setStoreRemark(tradeParams.getRemark());
            tradeDTO.setParentOrderSn(tradeParams.getParentOrderSn());

            // 校验收货地址
            if (tradeDTO.getDeliveryMethodEnum().equals(DeliveryMethodEnum.SELF_PICK_UP)) {
                if (tradeDTO.getStoreAddress() == null) {
                    throw new ServiceException(ResultCode.STORE_ADDRESS_NOT_EXIST);
                }
            }
            //虚拟商品不需要收货地址
            else if (tradeDTO.getDeliveryMethodEnum().equals(DeliveryMethodEnum.LOGISTICS) && !GoodsTypeEnum.VIRTUAL_GOODS.name()
                    .equals(tradeDTO.getCheckedSkuList().getFirst().getGoodsSku().getGoodsType())
                    && tradeDTO.getUserAddress() == null) {
                    throw new ServiceException(ResultCode.MEMBER_ADDRESS_NOT_EXIST);
                }

            //构建交易
            Trade trade = this.createTrade(tradeDTO);
            //获取订单SN用来将优惠券存入缓存
            TradeDTO newTradeDTO = this.readDTO(cartSceneEnum);
            newTradeDTO.setSn(trade.getSn());
            this.cleanChecked(newTradeDTO);
            return trade;
        } catch (ServiceException se) {
            log.info(se.getMsg(), se);
            throw se;
        } catch (Exception e) {
            log.error(ResultCode.ORDER_ERROR.message(), e);
            throw e;
        }
    }

    /**
     * 重新写入购物车
     *
     * @param tradeDTO 购物车构建器最终要构建的成品
     */
    public void resetTradeDTO(TradeDTO tradeDTO) {
        cache.put(this.getOriginKey(tradeDTO.getCartSceneEnum()), tradeDTO);
    }

    /**
     * 清空购物车
     *
     * @param way 购物车类型
     */
    public void clean(String way) {
        cache.remove(this.getOriginKey(CartSceneEnum.getCartType(way)));
    }

    /**
     * 清空购物车已选择的商品
     *
     * @param tradeDTO 购物车
     */
    private void cleanChecked(TradeDTO tradeDTO) {
        List<CartSkuVO> cartSkuVOS = tradeDTO.getSkuList();
        List<CartSkuVO> deleteVos = new ArrayList<>();
        for (CartSkuVO cartSkuVO : cartSkuVOS) {
            if (Boolean.TRUE.equals(cartSkuVO.getChecked())) {
                deleteVos.add(cartSkuVO);
            }
        }
        cartSkuVOS.removeAll(deleteVos);
        //清除添加过的备注
        tradeDTO.setNeedReceipt(false);
        tradeDTO.setReceiptVO(null);
        tradeDTO.setStoreRemark(null);
        cache.put(this.getOriginKey(tradeDTO.getCartSceneEnum()), tradeDTO);
    }

    /**
     * 读取当前会员购物原始数据key
     *
     * @param cartSceneEnum 获取方式
     * @return 当前会员购物原始数据key
     */
    private String getOriginKey(CartSceneEnum cartSceneEnum) {

        //缓存key，默认使用购物车
        if (cartSceneEnum != null) {
            AuthUser currentUser = UserContext.getCurrentExistUser();
            return cartSceneEnum.getPrefix() + currentUser.getExtendId();
        }
        throw new ServiceException();
    }

    /**
     * 根据渲染步骤，渲染购物车信息
     *
     * @param tradeDTO      交易DTO
     * @param defaultRender 渲染枚举
     */
    private void renderCartBySteps(TradeDTO tradeDTO, RenderStepEnums[] defaultRender) {
        for (RenderStepEnums step : defaultRender) {
            for (CartRenderStep render : cartRenderSteps) {
                try {
                    if (render.step().equals(step)) {
                        render.render(tradeDTO);
                        log.debug("购物车{}渲染完成", step.name());
                    }
                } catch (ServiceException e) {
                    throw e;
                } catch (Exception e) {
                    log.error("购物车{}渲染异常：", render.getClass(), e);
                }
            }
        }
    }

    /**
     * 计算运费
     *
     * @param count    重量/件
     * @param template 计算模版
     * @return 运费
     */
    private Double countFreight(Double count, FreightTemplateChildDTO template) {
        try {
            Double finalFreight = template.getFirstPrice();
            //不满首重 / 首件
            if (template.getFirstCompany() >= count) {
                return finalFreight;
            }
            //如果续重/续件，费用不为空，则返回
            if (template.getContinuedCompany() == 0 || template.getContinuedPrice() == 0) {
                return finalFreight;
            }

            //计算 续重 / 续件 价格
            Double continuedCount = count - template.getFirstCompany();
            return CurrencyUtil.add(finalFreight,
                    CurrencyUtil.mul(Math.ceil(continuedCount / template.getContinuedCompany()),
                            template.getContinuedPrice()));
        } catch (Exception e) {
            return 0D;
        }
    }

    public void buildFreightList(TradeDTO tradeDTO, UserAddress userAddress) {
        List<CartSkuVO> cartSkuVOList = tradeDTO.getSkuList().stream().filter(cartSkuVO -> !cartSkuVO.getSelfOperated()).toList();
        // 表示有代发店铺
        if (CollectionUtils.isNotEmpty(cartSkuVOList)) {
            tradeDTO.setNeedServiceFee(true);
            // 按店铺分组
            Map<String, List<CartSkuVO>> storeCartSkuVOMap = cartSkuVOList.stream().collect(Collectors.groupingBy(CartSkuVO::getStoreId));
            //获取市级别id匹配运费模版
            String addressId = userAddress.getConsigneeAddressIdPath().split(",")[1];
            List<FreightDTO> freightDTOList = new ArrayList<>();
            List<FreightTemplateVO> freightTemplateList = freightTemplateClient.getFreightTemplateList();
            if (CollectionUtils.isNotEmpty(freightTemplateList)) {
                for (FreightTemplateVO freightTemplateVO : freightTemplateList) {
                    if (CollectionUtils.isNotEmpty(freightTemplateVO.getFreightTemplateChildList())) {
                        List<FreightTemplateChild> freightTemplateChildList = freightTemplateVO.getFreightTemplateChildList().stream()
                                .filter(freightTemplateChild -> freightTemplateChild.getAreaId().contains(addressId)).toList();
                        if (CollectionUtils.isNotEmpty(freightTemplateChildList)) {
                            FreightDTO freightDTO = new FreightDTO();
                            freightDTO.setId(freightTemplateVO.getId());
                            freightDTO.setName(freightTemplateVO.getName());
                            freightDTO.setPricingMethod(freightTemplateVO.getPricingMethod());
                            freightDTO.setFreightTemplateChildList(freightTemplateChildList);
                            for (FreightTemplateChild freightTemplateChild : freightTemplateChildList) {
                                FreightTemplateChildDTO freightTemplateChildDTO = new FreightTemplateChildDTO(freightTemplateChild);
                                freightTemplateChildDTO.setPricingMethod(freightTemplateVO.getPricingMethod());
                                Double freightPrice = 0D;
                                for (Map.Entry<String, List<CartSkuVO>> entry : storeCartSkuVOMap.entrySet()) {
                                    Double count = entry.getValue().stream().mapToDouble(item ->
                                            // 根据计费规则 累加计费基数
                                            freightTemplateChildDTO.getPricingMethod().equals(FreightTemplateEnum.NUM.name()) ? item.getNum()
                                                    .doubleValue() : CurrencyUtil.mul(item.getNum(), item.getGoodsSku().getWeight())).sum();
                                    //计算运费
                                    Double countFreight = countFreight(count, freightTemplateChildDTO);
                                    freightPrice = CurrencyUtil.add(freightPrice, countFreight);
                                }
                                freightDTO.setFreightPrice(freightPrice);
                            }
                            freightDTOList.add(freightDTO);
                        }
                    }
                    tradeDTO.setFreightDTOList(freightDTOList);
                }
            }else {
                tradeDTO.setNotSupportFreight(new ArrayList<>());
            }

            if (CollectionUtils.isNotEmpty(tradeDTO.getFreightDTOList()) && CharSequenceUtil.isEmpty(tradeDTO.getFreightTemplateId())) {
                String id = tradeDTO.getFreightDTOList().stream().min(Comparator.comparing(FreightDTO::getFreightPrice)) // 找到价格最低的记录
                        .map(FreightDTO::getId).orElse(null);// 获取该记录的ID
                tradeDTO.setFreightTemplateId(id);
            }
        }
        this.resetTradeDTO(tradeDTO);
    }
}
