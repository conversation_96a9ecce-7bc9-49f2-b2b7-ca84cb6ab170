package cn.lili.modules.goods.serviceimpl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.event.TransactionCommitSendMQEvent;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.vo.PageVO;
import cn.lili.exchange.AmqpExchangeProperties;
import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.goods.entity.dos.GoodsSku;
import cn.lili.modules.goods.entity.dto.GoodsCompleteMessage;
import cn.lili.modules.goods.entity.dto.GoodsSearchParams;
import cn.lili.modules.goods.entity.dto.ProductSearchParams;
import cn.lili.modules.goods.entity.enums.GoodsAuthEnum;
import cn.lili.modules.goods.entity.enums.GoodsMarketEnum;
import cn.lili.modules.goods.entity.vos.GoodsStatusCountVO;
import cn.lili.modules.goods.mapper.GoodsMapper;
import cn.lili.modules.goods.service.GoodsService;
import cn.lili.modules.goods.service.GoodsSkuService;
import cn.lili.modules.search.entity.dos.EsGoodsIndex;
import cn.lili.modules.search.entity.dos.EsSupplierGoodsIndex;
import cn.lili.modules.search.entity.dto.EsGoodsIndexUpdateDTO;
import cn.lili.modules.search.entity.dto.EsGoodsSearchDTO;
import cn.lili.modules.search.service.EsGoodsIndexService;
import cn.lili.modules.search.service.EsGoodsSearchService;
import cn.lili.modules.store.client.FreightTemplateClient;
import cn.lili.modules.store.entity.dos.FreightTemplate;
import cn.lili.modules.store.entity.params.StoreRankStatisticsParams;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import cn.lili.mybatis.util.PageUtil;
import cn.lili.mybatis.util.SceneHelp;
import cn.lili.routing.GoodsRoutingKey;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.IOException;
import org.springframework.web.multipart.MultipartFile;


/**
 * 商品业务层实现
 *
 * <AUTHOR>
 * @since 2020-02-23 15:18:56
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GoodsServiceImpl extends ServiceImpl<GoodsMapper, Goods> implements GoodsService {

    private final FreightTemplateClient freightTemplateClient;

    private final GoodsSkuService goodsSkuService;

    private final AmqpExchangeProperties amqpExchangeProperties;

    private final ApplicationEventPublisher applicationEventPublisher;

    private EsGoodsIndexService goodsIndexService;

    private EsGoodsSearchService goodsSearchService;

    @Value("${product.baseUrl}")
    private String baseUrl;

    @Override
    public List<Goods> getByBrandIds(List<String> brandIds) {
        LambdaQueryWrapper<Goods> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(Goods::getBrandId, brandIds);
        return list(lambdaQueryWrapper);
    }

    /**
     * 更新商品参数
     *
     * @param goodsId 商品id
     * @param params  商品参数
     */
    @Override
    @Transactional
    public void updateGoodsParams(String goodsId, String params) {
        LambdaUpdateWrapper<Goods> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Goods::getId, goodsId);
        updateWrapper.set(Goods::getParams, params);
        this.update(updateWrapper);
    }

    @Override
    public final long getGoodsCountByCategory(String categoryId) {
        QueryWrapper<Goods> queryWrapper = Wrappers.query();
        queryWrapper.like("category_path", categoryId);
        queryWrapper.eq("delete_flag", false);
        return this.count(queryWrapper);
    }

    @Override
    @Transactional
    public void updateGoods(Goods goods) {
        //修改商品
        this.updateById(goods);
    }

    @Override
    public Page<Goods> queryByParams(GoodsSearchParams goodsSearchParams) {
        QueryWrapper queryWrapper = goodsSearchParams.queryWrapper();
        SceneHelp.queryHandler(queryWrapper, true);
        return this.page(PageUtil.initPage(goodsSearchParams), queryWrapper);
    }

    @Override
    public GoodsStatusCountVO countGoodsStatus(GoodsSearchParams goodsSearchParams) {
        goodsSearchParams.setAuthFlag(null);
        goodsSearchParams.setMarketEnable(null);
        goodsSearchParams.setGoodsStatus(null);
        return this.baseMapper.getGoodsStatusCount(goodsSearchParams.queryWrapper());
    }

    /**
     * 商品查询
     *
     * @param goodsSearchParams 查询参数
     * @return 商品信息
     */
    @Override
    public List<Goods> queryListByParams(GoodsSearchParams goodsSearchParams) {
        return this.list(goodsSearchParams.queryWrapper());
    }

    @Override
    public List<String> queryListIdByParams(GoodsSearchParams goodsSearchParams) {
        return this.baseMapper.getGoodsSkuIdByGoodsId(goodsSearchParams.queryWrapper());
    }

    @Override
    public List<String> queryListStoreIdByParams(GoodsSearchParams goodsSearchParams) {
        return this.baseMapper.getGoodsSkuStoreIdByGoodsId(goodsSearchParams.queryWrapper());
    }

    @Override
    @Transactional
    public Boolean freight(List<String> goodsIds, String templateId) {
        //获取当前用户
        String storeId = UserContext.getCurrentExistUser().getExtendId();
        //获取运费模板
        FreightTemplate freightTemplate = freightTemplateClient.getFreightTemplate(templateId);
        if (freightTemplate == null || freightTemplate.getId() == null) {
            throw new ServiceException(ResultCode.FREIGHT_TEMPLATE_NOT_EXIST);
        }
        //判断运费模板是否属于当前店铺
        if (!freightTemplate.getStoreId().equals(storeId)) {
            throw new ServiceException(ResultCode.USER_AUTHORITY_ERROR);
        }
        LambdaUpdateWrapper<Goods> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.set(Goods::getTemplateId, templateId);
        lambdaUpdateWrapper.eq(Goods::getStoreId, storeId);
        lambdaUpdateWrapper.in(Goods::getId, goodsIds);

        // 更新商品运费模板
        goodsSkuService.freight(goodsIds, templateId);
        return this.update(lambdaUpdateWrapper);
    }

    @Override
    public void updateStock(String goodsId, Integer quantity) {
        LambdaUpdateWrapper<Goods> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.set(Goods::getQuantity, quantity);
        lambdaUpdateWrapper.eq(Goods::getId, goodsId);
        this.update(lambdaUpdateWrapper);
    }

    @Override
    @Transactional
    public void updateStock(List<GoodsSku> goodsSkus) {
        // 根据商品id分组
        Map<String, List<GoodsSku>> groupByGoodId = goodsSkus.stream().collect(Collectors.groupingBy(GoodsSku::getGoodsId));
        //统计每个商品的库存
        for (Map.Entry<String, List<GoodsSku>> entry : groupByGoodId.entrySet()) {
            // 商品库存同步
            this.syncStock(entry.getKey());
        }
        if (!groupByGoodId.isEmpty()) {
            applicationEventPublisher.publishEvent(
                    TransactionCommitSendMQEvent.builder()
                            .source("更新商品")
                            .exchange(amqpExchangeProperties.getGoods())
                            .routingKey(GoodsRoutingKey.UPDATE_GOODS_INDEX)
                            .message(groupByGoodId.keySet())
                            .build());
        }
    }

    @Override
    public void syncStock(String goodsId) {
        Goods goods = this.getById(goodsId);
        // 如果商品不存在，则不同步库存
        if (goods == null) {
            return;
        }
        Integer stock = goodsSkuService.getGoodsStock(goodsId);
        // 如果是供应商商品同步库存，则将所有代理该商品的供应商商品库存同步
        if (goods.getScene().equals(SceneEnums.SUPPLIER.name())) {
            // 设置商品库存总数
            this.updateStockBySupplierGoodsID(goodsId, stock);
        }
        this.updateStock(goodsId, stock);
    }

    /**
     * 更新商品的购买数量
     *
     * @param goodsId  商品ID
     * @param buyCount 购买数量
     */
    @Override
    public void updateGoodsBuyCount(String goodsId, Integer buyCount) {
        this.baseMapper.updateBuyCount(goodsId, buyCount);
    }

    @Override
    public void updateGoodsBuyCount(List<GoodsCompleteMessage> goodsCompleteMessageList) {

        for (GoodsCompleteMessage goodsCompleteMessage : goodsCompleteMessageList) {
            GoodsSku goodsSku = goodsSkuService.getById(goodsCompleteMessage.getSkuId());

            int buyCount = goodsCompleteMessage.getBuyNum() ;

            goodsSkuService.updateBuyCount(goodsCompleteMessage.getSkuId(), buyCount);

            long goodsBuyCount = goodsSkuService.getGoodsBuyCountSum(goodsSku.getGoodsId());

            this.baseMapper.updateBuyCount(goodsCompleteMessage.getGoodsId(), Long.valueOf(goodsBuyCount).intValue());

            EsGoodsIndexUpdateDTO updateDTO = new EsGoodsIndexUpdateDTO();

            updateDTO.setQueryFields(
                    MapUtil.builder(new HashMap<String, Object>()).put("id", goodsCompleteMessage.getSkuId()).build());
            updateDTO.setUpdateFields(
                    MapUtil.builder(new HashMap<String, Object>()).put("buyCount", goodsSku.getBuyCount() + buyCount).build());
            if (goodsSku.getScene().equals(SceneEnums.SUPPLIER.name())) {
                updateDTO.setClazz(EsSupplierGoodsIndex.class);
            } else {
                updateDTO.setClazz(EsGoodsIndex.class);
            }

            goodsIndexService.updateIndex(updateDTO.getQueryFields(), updateDTO.getUpdateFields(),
                    updateDTO.getClazz());
        }
    }
    @Override
    public long countStoreGoodsNum(String storeId) {
        return this.count(
                new LambdaQueryWrapper<Goods>().eq(Goods::getStoreId, storeId).eq(Goods::getDeleteFlag, Boolean.FALSE)
                        .eq(Goods::getAuthFlag, GoodsAuthEnum.PASS.name())
                        .eq(Goods::getMarketEnable, GoodsMarketEnum.UPPER.name()));
    }

    @Override
    @Transactional
    public void addGoodsCommentNum(Integer commentNum, String goodsId) {
        this.baseMapper.addGoodsCommentNum(commentNum, goodsId);
    }

    @Override
    public Goods getGoodsByParams(GoodsSearchParams goodsSearchParams) {
        return this.getOne(goodsSearchParams.queryWrapper(), false);
    }

    @Override
    public long countGoodsNum(GoodsSearchParams goodsSearchParams) {
        return this.count(goodsSearchParams.queryWrapper());
    }

    /**
     * 根据供应商商品ID更新库存
     *
     * @param supplierGoodsId 商品ID
     * @param quantity        库存
     */
    private void updateStockBySupplierGoodsID(String supplierGoodsId, Integer quantity) {
        LambdaUpdateWrapper<Goods> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.set(Goods::getQuantity, quantity);
        lambdaUpdateWrapper.eq(Goods::getSupplierGoodsId, supplierGoodsId);
        this.update(lambdaUpdateWrapper);
    }

    @Autowired
    @Lazy
    public void setGoodsIndexService(EsGoodsIndexService goodsIndexService) {
        this.goodsIndexService = goodsIndexService;
    }

    @Override
    public long getImageDownloadCount(String storeId) {
        return this.baseMapper.getImageDownloadCount(storeId);
    }

    @Override
    public Page<Goods> getGoodsByPage(GoodsSearchParams searchParams) {
        QueryWrapper<Goods> queryWrapper = searchParams.queryWrapper();
        SceneHelp.queryHandler(queryWrapper, true);
        Page<Goods> page = this.baseMapper.selectPage(PageUtil.initPage(searchParams), queryWrapper);
        if (page.getRecords().isEmpty() || CharSequenceUtil.isEmpty(page.getRecords().getFirst().getSupplierGoodsId())) {
            return page;
        }
        return page;
    }

    @Override
    public Page<EsGoodsIndex> searchProduct (ProductSearchParams productSearchParams) {
        String createUrl = baseUrl + "/search";
        Map<String, Object> searchParams = new HashMap<>();
        if (StringUtils.isNotBlank(productSearchParams.getTitle())) {
            searchParams.put("title", productSearchParams.getTitle());
        }
        if (StringUtils.isNotBlank(productSearchParams.getImage_url())) {
            searchParams.put("image_url", productSearchParams.getImage_url());
        }
        if (productSearchParams.getTop_k() != null) {
            searchParams.put("top_k", productSearchParams.getTop_k());
        }
        if (productSearchParams.getSimilarity_threshold() != null) {
            searchParams.put("similarity_threshold", productSearchParams.getSimilarity_threshold());
        }
        
        List<String> productIds = new ArrayList<>();
        try {
            URI uri = new URI(createUrl);
            URL url = uri.toURL();
            HttpURLConnection connection;
            
            // 判断是通过image_url还是image文件进行搜索
            if (productSearchParams.getImage() != null && !productSearchParams.getImage().isEmpty()) {
                // 使用multipart/form-data格式上传文件
                String boundary = "----WebKitFormBoundary" + System.currentTimeMillis();
                connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);
                connection.setDoOutput(true);
                connection.setDoInput(true);
                
                try (OutputStream os = connection.getOutputStream()) {
                    // 写入其他参数
                    if (StringUtils.isNotBlank(productSearchParams.getTitle())) {
                        writeBoundaryField(os, boundary, "title", productSearchParams.getTitle());
                    }
                    if (StringUtils.isNotBlank(productSearchParams.getImage_url())) {
                        writeBoundaryField(os, boundary, "image_url", productSearchParams.getImage_url());
                    }
                    if (productSearchParams.getTop_k() != null) {
                        writeBoundaryField(os, boundary, "top_k", productSearchParams.getTop_k().toString());
                    }
                    if (productSearchParams.getSimilarity_threshold() != null) {
                        writeBoundaryField(os, boundary, "similarity_threshold", productSearchParams.getSimilarity_threshold().toString());
                    }
                    
                    // 写入文件内容
                    MultipartFile file = productSearchParams.getImage();
                    os.write(("--" + boundary + "\r\n").getBytes(StandardCharsets.UTF_8));
                    os.write(("Content-Disposition: form-data; name=\"image\"; filename=\"" + 
                            file.getOriginalFilename() + "\"\r\n").getBytes(StandardCharsets.UTF_8));
                    os.write(("Content-Type: " + file.getContentType() + "\r\n\r\n").getBytes(StandardCharsets.UTF_8));
                    os.write(file.getBytes());
                    os.write("\r\n".getBytes(StandardCharsets.UTF_8));
                    
                    // 结束边界
                    os.write(("--" + boundary + "--\r\n").getBytes(StandardCharsets.UTF_8));
                }
            } else {
                // 使用普通JSON格式请求
                String requestBody = JSONObject.toJSONString(searchParams);
                connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setDoOutput(true);
                connection.setDoInput(true);
                
                // 发送请求体
                try (java.io.OutputStream os = connection.getOutputStream()) {
                    byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
                    os.write(input, 0, input.length);
                }
            }
            
            // 处理响应
            int responseCode = connection.getResponseCode();
            if (responseCode >= 200 && responseCode < 300) {
                // 读取响应内容
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    StringBuilder response = new StringBuilder();
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        response.append(responseLine.trim());
                    }
                    
                    // 解析响应JSON
                    JSONObject jsonResponse = JSONObject.parseObject(response.toString());
                    if (jsonResponse.containsKey("results")) {
                        List<JSONObject> results = jsonResponse.getJSONArray("results").toJavaList(JSONObject.class);
                        for (JSONObject result : results) {
                            JSONObject product = result.getJSONObject("product");
                            if (product != null && product.containsKey("id")) {
                                productIds.add(product.getString("id"));
                            }
                        }
                    }
                    log.info("搜索商品成功，找到{}个商品", productIds.size());
                }
            } else {
                // 处理错误
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
                    StringBuilder errorResponse = new StringBuilder();
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        errorResponse.append(responseLine.trim());
                    }
                    log.error("搜索商品失败，响应代码: {}, 错误信息: {}", responseCode, errorResponse.toString());
                }
            }
            connection.disconnect();
        } catch (Exception e) {
            log.error("搜索商品失败", e);
        }
        
        if (CollectionUtils.isNotEmpty(productIds)) {
            log.info("搜索商品成功，找到{}个商品", productIds.size());
            EsGoodsSearchDTO goodsSearchParams = EsGoodsSearchDTO.builder()
                    .productId(String.join(",", productIds))
                    .build();
            PageVO pageVo = new PageVO();
            pageVo.setPageNumber(1);
            pageVo.setPageSize(20);
            pageVo.setNotConvert(true);
            return goodsSearchService.searchGoodsByPage(goodsSearchParams, pageVo, EsGoodsIndex.class);
        }
        return new Page<>();
    }
    
    /**
     * 写入表单字段到multipart请求
     */
    private void writeBoundaryField(OutputStream os, String boundary, String fieldName, String fieldValue) throws IOException {
        os.write(("--" + boundary + "\r\n").getBytes(StandardCharsets.UTF_8));
        os.write(("Content-Disposition: form-data; name=\"" + fieldName + "\"\r\n\r\n").getBytes(StandardCharsets.UTF_8));
        os.write((fieldValue + "\r\n").getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public Page<StoreRankStatisticsVO> getGoodsImageDownloadCountByStore (StoreRankStatisticsParams params) {
        QueryWrapper queryWrapper = new QueryWrapper<>();
        // 如果指定了店铺ID，则只查询该店铺
        if (CollectionUtils.isNotEmpty(params.getStoreIds())) {
            queryWrapper.in("lg.store_id", params.getStoreIds());
        }
        // 分组查询
        queryWrapper.groupBy("lg.store_id");
        queryWrapper.orderByDesc("SUM(lg.image_download_count)");
        return this.baseMapper.getGoodsImageDownloadCountByStore(PageUtil.initPage(params), queryWrapper);
    }

    @Override
    public List<Goods> countGoodsNum(String storeId) {
        LambdaQueryWrapper<Goods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Goods::getStoreId, storeId)
                .eq(Goods::getDeleteFlag, Boolean.FALSE)
                .eq(Goods::getAuthFlag, GoodsAuthEnum.PASS.name())
                .eq(Goods::getMarketEnable, GoodsMarketEnum.UPPER.name());
        return this.list(queryWrapper);
    }

    @Override
    public Double getRealShootRate(String storeId) {
        return this.baseMapper.getRealShootRate(storeId);
    }

    @Override
    public Double getQualityPassRate(String storeId) {
        return this.baseMapper.getQualityPassRate(storeId);
    }

    @Override
    @Transactional
    public void incrementVisitorCount(String goodsId) {
        LambdaUpdateWrapper<Goods> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(Goods::getId, goodsId);
        updateWrapper.setSql("visitor_count = IFNULL(visitor_count, 0) + 1");
        this.update(updateWrapper);
    }

    @Override
    public Page<Goods> getGoodsVisitorRanking(GoodsSearchParams params) {
        QueryWrapper<Goods> queryWrapper = params.queryWrapper();
        // 只查询已上架且审核通过的商品
        queryWrapper.eq("market_enable", GoodsMarketEnum.UPPER.name());
        queryWrapper.eq("auth_flag", GoodsAuthEnum.PASS.name());
        queryWrapper.eq("delete_flag", false);
        // 按访客数量降序排列
        queryWrapper.orderByDesc("visitor_count");
        // 只查询有访客记录的商品
        queryWrapper.gt("visitor_count", 0);

        SceneHelp.queryHandler(queryWrapper, true);
        return this.page(PageUtil.initPage(params), queryWrapper);
    }

    @Override
    public Page<Goods> getGoodsImageDownloadRanking(GoodsSearchParams params) {
        QueryWrapper<Goods> queryWrapper = params.queryWrapper();
        // 只查询已上架且审核通过的商品
        queryWrapper.eq("market_enable", GoodsMarketEnum.UPPER.name());
        queryWrapper.eq("auth_flag", GoodsAuthEnum.PASS.name());
        queryWrapper.eq("delete_flag", false);
        // 按图片下载量降序排列
        queryWrapper.orderByDesc("image_download_count");
        // 只查询有图片下载记录的商品
        queryWrapper.gt("image_download_count", 0);

        SceneHelp.queryHandler(queryWrapper, true);
        return this.page(PageUtil.initPage(params), queryWrapper);
    }
}