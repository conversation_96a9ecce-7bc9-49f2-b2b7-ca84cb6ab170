package cn.lili.modules.order.fallback;

import cn.lili.common.exception.ServiceException;
import cn.lili.modules.order.order.client.OrderFlowStatisticsClient;
import cn.lili.modules.statistics.entity.dto.GoodsStatisticsQueryParam;
import cn.lili.modules.statistics.entity.vo.CategoryStatisticsDataVO;
import cn.lili.modules.statistics.entity.vo.GoodsStatisticsDataVO;
import cn.lili.modules.statistics.entity.vo.OrderOverviewVO;
import cn.lili.modules.statistics.entity.vo.StoreStatisticsDataVO;
import cn.lili.modules.store.entity.dto.StoreStatisticsOverviewSearchParams;
import cn.lili.modules.store.entity.dto.StoreStatisticsSearchParams;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/6/24
 **/
public class OrderFlowStatisticsFallback implements OrderFlowStatisticsClient {
    @Override
    public List<GoodsStatisticsDataVO> getGoodsStatisticsData(GoodsStatisticsQueryParam goodsStatisticsQueryParam, Integer num) {
        throw new ServiceException();
    }

    @Override
    public long getGoodsSalesVolume(GoodsStatisticsQueryParam queryDTO) {
        throw new ServiceException();
    }

    @Override
    public List<CategoryStatisticsDataVO> getCategoryStatisticsData(GoodsStatisticsQueryParam goodsStatisticsQueryParam) {
        throw new ServiceException();
    }

    @Override
    public List<StoreStatisticsDataVO> getStoreStatisticsData(StoreStatisticsSearchParams searchParams) {
        throw new ServiceException();
    }

    @Override
    public Map<String, Object> getOrderStatisticsPrice(String storeId) {
        throw new ServiceException();
    }

    @Override
    public OrderOverviewVO overview(StoreStatisticsOverviewSearchParams searchParams) {
        throw new ServiceException();
    }

    @Override
    public GoodsStatisticsDataVO getSaleNumByStoreId(String storeId) {
        throw new ServiceException();
    }
}
