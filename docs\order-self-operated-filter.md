# 订单按店铺类型筛选功能

## 功能概述

在订单查询接口中新增了按店铺类型（直营/非直营）筛选订单的功能。通过 `li_store` 表中的 `self_operated` 字段来判断店铺是否为直营店铺。

## 实现详情

### 1. 新增查询参数

**文件**: `yht-sdk/src/main/java/cn/lili/modules/order/order/entity/dto/OrderSearchParams.java`

新增字段：
```java
@Schema(title = "店铺类型筛选", description = "true:直营店铺订单, false:非直营店铺订单, null:不筛选")
private Boolean selfOperated;
```

### 2. 修改店铺查询参数

**文件**: `yht-sdk/src/main/java/cn/lili/modules/store/entity/dto/StoreSearchParams.java`

新增字段：
```java
@Schema(title = "是否自营", description = "true:直营店铺, false:非直营店铺, null:不筛选")
private Boolean selfOperated;
```

在 `queryWrapper()` 方法中添加查询条件：
```java
if (selfOperated != null) {
    wrapper.eq("self_operated", selfOperated);
}
```

### 3. 修改订单查询逻辑

**文件**: `service/order-service/src/main/java/cn/lili/modules/order/order/serviceimpl/OrderServiceImpl.java`

#### 实现思路：
1. 先根据 `selfOperated` 参数查询对应类型的店铺ID列表
2. 使用 `IN` 查询来筛选这些店铺的订单

#### 核心方法：
```java
private List<String> getStoreIdsBySelfOperated(Boolean selfOperated) {
    StoreSearchParams storeSearchParams = new StoreSearchParams();
    storeSearchParams.setSelfOperated(selfOperated);

    List<Store> stores = storeClient.list(storeSearchParams);

    return stores.stream()
            .map(Store::getId)
            .filter(Objects::nonNull)
            .toList();
}
```

#### 修改的查询方法：
1. **queryByParams** - 订单列表查询
2. **queryListByParams** - 订单信息查询

查询逻辑：
```java
if (orderSearchParams.getSelfOperated() != null) {
    List<String> storeIds = getStoreIdsBySelfOperated(orderSearchParams.getSelfOperated());
    if (CollectionUtils.isEmpty(storeIds)) {
        return new Page<>(orderSearchParams.getPageNumber(), orderSearchParams.getPageSize());
    }
    queryWrapper.in("o.store_id", storeIds);
}
```

### 4. 数据表关系

- **li_order**: 订单表
  - `store_id`: 店铺ID
  
- **li_store**: 店铺表
  - `id`: 店铺ID
  - `self_operated`: 是否自营（Boolean类型）
    - `true`: 直营店铺
    - `false`: 非直营店铺

## API 使用示例

### 1. 查询直营店铺订单

```http
GET /order?selfOperated=true&pageNumber=1&pageSize=10
```

### 2. 查询非直营店铺订单

```http
GET /order?selfOperated=false&pageNumber=1&pageSize=10
```

### 3. 查询所有订单（不筛选店铺类型）

```http
GET /order?pageNumber=1&pageSize=10
```

或者明确设置为 null：
```http
GET /order?selfOperated=&pageNumber=1&pageSize=10
```

### 4. 结合其他条件查询

```http
GET /order?selfOperated=true&orderStatus=COMPLETED&pageNumber=1&pageSize=10
```

## 代码示例

### Java 调用示例

```java
// 查询直营店铺订单
OrderSearchParams searchParams = new OrderSearchParams();
searchParams.setSelfOperated(true);
searchParams.setPageNumber(1);
searchParams.setPageSize(10);

Page<OrderSimpleVO> result = orderService.queryByParams(searchParams);

// 查询非直营店铺订单
searchParams.setSelfOperated(false);
Page<OrderSimpleVO> nonDirectResult = orderService.queryByParams(searchParams);

// 查询所有订单
searchParams.setSelfOperated(null);
Page<OrderSimpleVO> allResult = orderService.queryByParams(searchParams);
```

### 前端调用示例

```javascript
// 查询直营店铺订单
const directOrders = await api.get('/order', {
  params: {
    selfOperated: true,
    pageNumber: 1,
    pageSize: 10
  }
});

// 查询非直营店铺订单
const nonDirectOrders = await api.get('/order', {
  params: {
    selfOperated: false,
    pageNumber: 1,
    pageSize: 10
  }
});
```

## 响应数据结构

响应数据结构保持不变，仍然是 `Page<OrderSimpleVO>` 格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "sn": "O202507260001",
        "storeName": "直营旗舰店",
        "storeId": "store123",
        "orderStatus": "COMPLETED",
        "flowPrice": 299.00,
        "createTime": "2025-07-26 10:30:00"
      }
    ],
    "total": 100,
    "current": 1,
    "size": 10
  }
}
```

## 性能考虑

1. **索引优化**: 建议在 `li_store.self_operated` 字段上创建索引以提高查询性能
2. **两次查询**: 先查询店铺ID，再查询订单，会产生两次数据库查询，但避免了复杂的 JOIN 操作
3. **缓存策略**: 可以考虑对店铺ID列表进行缓存，减少重复的店铺查询
4. **空结果优化**: 当没有找到对应类型的店铺时，直接返回空结果，避免无效的订单查询

## 兼容性

- **向后兼容**: 新增的 `selfOperated` 参数为可选参数，不影响现有的查询功能
- **默认行为**: 当 `selfOperated` 为 `null` 时，不进行店铺类型筛选，查询所有订单

## 测试用例

**文件**: `service/order-service/src/test/java/cn/lili/modules/order/OrderSelfOperatedTest.java`

包含以下测试场景：
1. 查询直营店铺订单
2. 查询非直营店铺订单  
3. 查询所有订单（不筛选）
4. 结合其他条件的复合查询

## 注意事项

1. **数据一致性**: 确保 `li_store.self_operated` 字段的数据准确性
2. **权限控制**: 根据用户角色可能需要限制查询范围
3. **日志记录**: 建议记录店铺类型筛选的查询日志，便于分析和监控

## 扩展性

该功能设计具有良好的扩展性：
- 可以轻松添加其他店铺属性的筛选条件
- 支持与现有的所有查询条件组合使用
- 为后续的店铺分类管理提供了基础

## 总结

通过添加 `selfOperated` 查询参数，订单查询接口现在支持：
- ✅ 查询直营店铺订单
- ✅ 查询非直营店铺订单
- ✅ 查询所有订单（不筛选店铺类型）
- ✅ 与其他查询条件组合使用
- ✅ 保持向后兼容性

这个功能为平台管理员和运营人员提供了更精细的订单数据分析能力，有助于区分直营业务和第三方商家业务的表现。
