package cn.lili.modules.file.fallback;

import cn.lili.common.exception.ServiceException;
import cn.lili.modules.file.client.UploadClient;
import cn.lili.modules.file.entity.File;
import cn.lili.modules.file.entity.dto.FileUploadDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * 上传Fallback
 */
@Slf4j
public class UploadClientFallback implements UploadClient {
    @Override
    public String upload(FileUploadDTO fileUploadDTO) {
        throw new ServiceException();
    }

    @Override
    public String uploadByUrl(FileUploadDTO fileUploadDTO) {
        throw new ServiceException();
    }

    @Override
    public String uploadFile(FileUploadDTO fileUploadDTO) {
        throw new ServiceException();
    }

    @Override
    public void remove(List<String> keys) {
        throw new ServiceException();
    }

    @Override
    public List<File> getFilesByUrls(List<String> urls) {
        log.error("UploadClient.getFilesByUrls 服务调用失败，参数：{}", urls);
        return Collections.emptyList();
    }

    @Override
    public void deleteFiles(List<String> fileKeys) {
        log.error("UploadClient.deleteFiles 服务调用失败，参数：{}", fileKeys);
        // 降级时不执行删除操作
    }
}
