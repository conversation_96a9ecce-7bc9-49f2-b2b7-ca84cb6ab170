package cn.lili.controller.goods;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.aop.annotation.PreventDuplicateSubmissions;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.goods.entity.dos.GoodsImageDownloadRecord;
import cn.lili.modules.goods.entity.dos.GoodsSku;
import cn.lili.modules.goods.entity.dto.*;
import cn.lili.modules.goods.entity.dto.OffShelfGoodsSearchParams;
import cn.lili.modules.goods.entity.enums.GoodsAuthEnum;
import cn.lili.modules.goods.entity.vos.GoodsBuyerVO;
import cn.lili.modules.goods.entity.vos.GoodsPageVO;
import cn.lili.modules.goods.entity.vos.OffShelfGoodsCategoryCountVO;
import cn.lili.modules.goods.entity.vos.GoodsSkuVO;
import cn.lili.modules.goods.entity.vos.GoodsVO;
import cn.lili.modules.goods.integration.GoodsIntegrationService;
import cn.lili.modules.goods.service.GoodsImageDownloadRecordService;
import cn.lili.modules.goods.service.GoodsService;
import cn.lili.modules.goods.service.GoodsSkuService;
import cn.lili.modules.goods.service.GoodsStatisticsService;
import cn.lili.modules.search.entity.dos.EsGoodsIndex;
import cn.lili.modules.statistics.aop.PageViewPoint;
import cn.lili.modules.statistics.aop.enums.PageViewEnum;
import cn.lili.modules.store.client.FreightTemplateClient;
import cn.lili.modules.store.client.StoreClient;
import cn.lili.modules.store.entity.dos.FreightTemplate;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 商品管理接口
 *
 * <AUTHOR>
 * @since 2020-02-23 15:18:56
 */
@Slf4j
@RestController
@Tag(name = "商品管理接口")
@RequiredArgsConstructor
@RequestMapping("/goods")
public class GoodsController {

    private final GoodsService goodsService;

    private final GoodsSkuService goodsSkuService;

    private final GoodsIntegrationService goodsIntegrationService;

    private final StoreClient storeClient;

    private final GoodsStatisticsService goodsStatisticsService;

    private final FreightTemplateClient freightTemplateClient;

    private final GoodsImageDownloadRecordService goodsImageDownloadRecordService;

    @Operation(summary = "分页获取")
    @GetMapping
    public ResultMessage<GoodsPageVO> getByPage(GoodsSearchParams goodsSearchParams) {

        return ResultUtil.data(goodsIntegrationService.getGoodsPage(goodsSearchParams));
    }


    @Operation(summary = "分页获取商品列表")
    @GetMapping(value = "/sku/list")
    public ResultMessage<Page<GoodsSku>> getSkuByPage(GoodsSearchParams goodsSearchParams) {
        return ResultUtil.data(goodsSkuService.getGoodsSkuByPage(goodsSearchParams));
    }

    @Operation(summary = "根据goodsId分页获取商品规格列表")
    @GetMapping(value = "/sku/{goodsId}/list")
    public ResultMessage<List<GoodsSkuVO>> getSkuByList(@PathVariable String goodsId) {
        String storeId = Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId();
        return ResultUtil.data(goodsSkuService.getGoodsSkuVOList(goodsSkuService.list(new LambdaQueryWrapper<GoodsSku>().eq(GoodsSku::getGoodsId,
                goodsId).eq(GoodsSku::getStoreId, storeId))));
    }

    @Operation(summary = "分页获取库存告警商品列表")
    @GetMapping(value = "/list/stock")
    public ResultMessage<IPage<GoodsSku>> getWarningStockByPage(GoodsSearchParams goodsSearchParams) {
        //获取当前登录商家账号
        String storeId = Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId();
        goodsSearchParams.setStoreId(storeId);
        goodsSearchParams.setAlertQuantity(true);
        Page<GoodsSku> goodsSku = goodsSkuService.getGoodsSkuByPage(goodsSearchParams);
        return ResultUtil.data(goodsSku);
    }

    @PreventDuplicateSubmissions(expire = 5)
    @Operation(summary = "管理商品状态", description = "管理商品状态")
    @PutMapping(value = "/update/status")
    public ResultMessage<Object> underGoods(@RequestBody GoodsUpdateStatusDTO goodsUpdateStatusDTO) {
        goodsIntegrationService.updateGoodsStatus(goodsUpdateStatusDTO);
        return ResultUtil.success();
    }


    @Operation(summary = "新增商品")
    @PostMapping
    public ResultMessage<GoodsOperationDTO> save(@RequestBody GoodsOperationDTO goodsOperationDTO) {
        if (Objects.requireNonNull(UserContext.getCurrentUser()).getScene().equals(SceneEnums.STORE)) {
            goodsOperationDTO.validateParams();
            goodsIntegrationService.saveGoods(goodsOperationDTO);
            return ResultUtil.success();
        } else {
            return ResultUtil.error();
        }
    }

    @Operation(summary = "修改商品")
    @PutMapping(value = "/{goodsId}")
    public ResultMessage<GoodsOperationDTO> update(@PathVariable String goodsId, @RequestBody GoodsOperationDTO goodsOperationDTO) {
        if (Objects.requireNonNull(UserContext.getCurrentUser()).getScene().equals(SceneEnums.STORE)) {
            goodsOperationDTO.setGoodsId(goodsId);
            goodsOperationDTO.validateParams();
            goodsIntegrationService.updateGoods(goodsOperationDTO);
            return ResultUtil.success();
        } else {
            return ResultUtil.error();
        }
    }

    @Operation(summary = "删除商品")
    @DeleteMapping(value = "/delete")
    public ResultMessage<Object> deleteGoods(@RequestParam List<String> goodsId) {
        Boolean result = goodsIntegrationService.deleteGoods(goodsId);
        return ResultUtil.data(result);
    }

    @Operation(summary = "设置商品运费模板")
    @PutMapping(value = "/freight")
    public ResultMessage<Object> freight(@RequestBody GoodsUpdateFreightDTO goodsUpdateFreightDTO) {
        goodsUpdateFreightDTO.validateParams();
        Boolean result = goodsService.freight(goodsUpdateFreightDTO.getGoodsId(), goodsUpdateFreightDTO.getTemplateId());
        return ResultUtil.data(result);
    }

    @Operation(summary = "修改商品库存")
    @PutMapping(value = "/update/stocks")
    public ResultMessage<Object> updateStocks(@RequestBody List<GoodsSkuStockDTO> updateStockList) {
        if (CollUtil.isEmpty(updateStockList)) {
            throw new ServiceException(ResultCode.POINT_GOODS_ACTIVE_STOCK_MUST_ERROR);
        }
        List<GoodsSku> goodsSkuList = filterStoreGoods(updateStockList);

        //设置库存
        for (GoodsSku goodsSku : goodsSkuList) {
            //如果是代理商修改供应商发布的商品库存信息，则抛出异常
            if (Boolean.TRUE.equals(goodsSku.getIsProxyGoods()) && (CharSequenceUtil.isNotEmpty(goodsSku.getSupplierGoodsId()))) {
                throw new ServiceException(ResultCode.PROXY_GOODS_CANT_UPDATE_STOCK);

            }
            for (GoodsSkuStockDTO goodsSkuStockDTO : updateStockList) {
                if (goodsSku.getId().equals(goodsSkuStockDTO.getSkuId())) {
                    goodsSku.setQuantity(goodsSkuStockDTO.getQuantity());
                }
            }
        }

        goodsIntegrationService.updateStocks(goodsSkuList);
        goodsService.updateStock(goodsSkuList);
        return ResultUtil.success();
    }

    @Operation(summary = "通过id获取商品详情")
    @GetMapping(value = "/{id}")
    public ResultMessage<GoodsVO> get(@PathVariable String id) {
        return ResultUtil.data(goodsIntegrationService.getGoodsVO(id));
    }

    @Operation(summary = "通过id获取商品信息")
    @GetMapping(value = "/sku/vo/{goodsId}/{skuId}")
    @PageViewPoint(type = PageViewEnum.SKU, id = "#id")
    public ResultMessage<GoodsBuyerVO> getSku(@NotNull(message = "商品ID不能为空") @PathVariable("goodsId") String goodsId, @NotNull(message =
            "SKU ID不能为空") @PathVariable("skuId") String skuId) {
        try {
            // 读取选中的列表
            GoodsBuyerVO map = goodsIntegrationService.getGoodsSkuDetail(goodsId, skuId);
            return ResultUtil.data(map);
        } catch (ServiceException se) {
            log.info(se.getMsg(), se);
            throw se;
        } catch (Exception e) {
            log.error(ResultCode.GOODS_ERROR.message(), e);
            return ResultUtil.error(ResultCode.GOODS_ERROR);
        }
    }

    @Operation(summary = "获取审核数量")
    @GetMapping(value = "/authNum")
    public ResultMessage<Object> getGoodsAuthNum() {
        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser != null && (currentUser.getScene().equals(SceneEnums.STORE) || currentUser.getScene().equals(SceneEnums.SUPPLIER))) {
            String storeId = Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId();
            if (CharSequenceUtil.isNotBlank(storeId)) {
                Map map = new HashMap<>();
                map.put("waitAuthNum", goodsStatisticsService.goodsNum(null, GoodsAuthEnum.TOBEAUDITED, storeId));
                map.put("refuseNum", goodsStatisticsService.goodsNum(null, GoodsAuthEnum.REFUSE, storeId));
                return ResultUtil.data(map);
            } else {
                return ResultUtil.error(ResultCode.ERROR);
            }
        } else {
            return ResultUtil.error(ResultCode.ERROR);
        }
    }

    @Operation(summary = "导出商品数据")
    @GetMapping(value = "/export")
    public ResultMessage<Object> exportGoodsSkuList(GoodsSearchParams goodsSearchParams) {
        goodsSkuService.exportGoodsSkuList(goodsSearchParams);
        return ResultUtil.success();
    }

    @GetMapping("/freight/{id}")
    @Operation(summary = "获取运费模板")
    public ResultMessage<FreightTemplate> getFreightTemplate(@PathVariable String id) {
        return ResultUtil.data(freightTemplateClient.getFreightTemplate(id));
    }

    @Operation(summary = "修改告警库存")
    @PutMapping(value = "/alert/stocks")
    public ResultMessage<Object> updateAlertStocks(@Valid @RequestBody GoodsSkuStockDTO goodsSkuStockDTO) {
        goodsSkuService.updateAlertQuantity(goodsSkuStockDTO);
        return ResultUtil.success();
    }

    @Operation(summary = "批量修改告警库存")
    @PutMapping(value = "/alert/stocks/batch")
    public ResultMessage<Object> batchUpdateAlertStocks(@RequestBody List<GoodsSkuStockDTO> updateStockList) {
        List<GoodsSku> goodsSkuList = filterStoreGoods(updateStockList);
        // 过滤不符合当前店铺的商品
        List<String> filterGoodsSkuIds = goodsSkuList.stream().map(GoodsSku::getId).toList();
        List<GoodsSkuStockDTO> collect = updateStockList.stream().filter(i -> filterGoodsSkuIds.contains(i.getSkuId())).toList();
        goodsSkuService.batchUpdateAlertQuantity(collect);
        return ResultUtil.success();
    }

    private List<GoodsSku> filterStoreGoods(List<GoodsSkuStockDTO> updateStockList) {
        String storeId = Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId();
        // 获取商品skuId集合
        List<String> goodsSkuIds = updateStockList.stream().map(GoodsSkuStockDTO::getSkuId).toList();
        // 根据skuId集合查询商品信息
        return goodsSkuService.list(new LambdaQueryWrapper<GoodsSku>().in(GoodsSku::getId, goodsSkuIds).eq(GoodsSku::getStoreId, storeId));
    }

    /**
     * 获取资源包
     * @param goodsId 商品id
     * @return 资源包路径
     */
    @GetMapping("/getImageZipFile")
    public ResultMessage<Object> getImageZipFile(@RequestParam String goodsId, HttpServletResponse response) {
        Goods goods = goodsService.getById(goodsId);
        if (goods == null) {
            throw new ServiceException(ResultCode.GOODS_NOT_EXIST);
        }
        if (StringUtil.isEmpty(goods.getImageZipFile())) {
            throw new ServiceException(ResultCode.GOODS_NOT_EXIST);
        }
        AuthUser currentUser = UserContext.getCurrentUser();

        long count = goodsImageDownloadRecordService.count(new LambdaQueryWrapper<GoodsImageDownloadRecord>().
                eq(GoodsImageDownloadRecord::getGoodsId, goodsId)
                .eq(GoodsImageDownloadRecord::getMemberId, currentUser.getId()));
        if (count == 0) {
            GoodsImageDownloadRecord goodsImageDownloadRecord = new GoodsImageDownloadRecord();
            goodsImageDownloadRecord.setGoodsId(goodsId);
            goodsImageDownloadRecord.setMemberId(currentUser.getId());
            goodsImageDownloadRecordService.save(goodsImageDownloadRecord);
            if (goods.getImageDownloadCount() != null) {
                goods.setImageDownloadCount(goods.getImageDownloadCount() + 1);
            } else {
                goods.setImageDownloadCount(1);
            }
            goodsService.updateById(goods);
        }
//
//        try {
//            //使用java语言对这个地址进行连接并读取
//            HttpURLConnection httpUrl = (HttpURLConnection) new URL(goods.getImageZipFile()).openConnection();
//            httpUrl.connect();
//            InputStream inputStream = httpUrl.getInputStream();
//            //以上这样我们就得到了一个文件流；不管是视频，图片，音频，文本文件，都是可以的
//            //返回文件流
//            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
//            //创建存放文件内容的数组
//            byte[] buff =new byte[1024];
//            //所读取的内容使用n来接收
//            int n;
//            //当没有读取完时,继续读取,循环
//            while((n=inputStream.read(buff))!=-1){
//                //将字节数组的数据全部写入到输出流中
//                outputStream.write(buff,0,n);
//            }
//            //强制将缓存区的数据进行输出
//            outputStream.flush();
//            //关流
//            outputStream.close();
//            inputStream.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        return ResultUtil.data(goods.getImageZipFile());
    }

    @Operation(summary = "分页获取商品列表")
    @GetMapping(value = "/list")
    public ResultMessage<Page<Goods>> getGoodsByPage(GoodsSearchParams goodsSearchParams) {
        return ResultUtil.data(goodsService.getGoodsByPage(goodsSearchParams));
    }

    @Operation(summary = "搜同款商品")
    @PostMapping(value = "/search/product", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResultMessage<Page<EsGoodsIndex>> searchProduct(
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "image_url", required = false) String imageUrl,
            @RequestParam(value = "top_k", required = false, defaultValue = "20") Integer topK,
            @RequestParam(value = "similarity_threshold", required = false, defaultValue = "0.7") Double similarityThreshold,
            @RequestParam(value = "image", required = false) MultipartFile image) {
        
        ProductSearchParams productSearchParams = new ProductSearchParams();
        productSearchParams.setTitle(title);
        productSearchParams.setImage_url(imageUrl);
        productSearchParams.setTop_k(topK);
        productSearchParams.setSimilarity_threshold(similarityThreshold);
        productSearchParams.setImage(image);
        
        return ResultUtil.data(goodsService.searchProduct(productSearchParams));
    }

}
