package cn.lili.modules.store.entity.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.modules.store.entity.enums.StoreStatusEnum;
import cn.lili.modules.store.entity.enums.StoreQueryTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 店铺搜索参数VO
 *
 * <AUTHOR>
 * @since 2020-03-07 17:02:05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StoreSearchParams implements Serializable {

    @Serial
    private static final long serialVersionUID = 6916054310764833369L;


    @Schema(title = "店铺id集合")
    private List<String> storeIds;

    @Schema(title = "店铺名称")
    private String storeName;
    /**
     * @see StoreStatusEnum
     */
    @Schema(title = "店铺状态")
    private String storeStatus;

    @Schema(title = "开始时间")
    private String startDate;

    @Schema(title = "结束时间")
    private String endDate;

    @Schema(title = "是否认证")
    private Boolean verify;


    @Schema(title = "保留字段1", hidden = true)
    private String field1;

    @Schema(title = "保留字段2", hidden = true)
    private String field2;

    @Schema(title = "保留字段3", hidden = true)
    private String field3;

    @Schema(title = "保留字段4", hidden = true)
    private String field4;

    @Schema(title = "保留字段5", hidden = true)
    private String field5;

    @Schema(title = "场景", hidden = true)
    private String scene;

    @Schema(title = "法人姓名")
    private String legalPerson;

    @Schema(title = "店铺地区id列表")
    private List<String> storeRegionIdList;

    @Schema(title = "店铺标签")
    private String storeTag;

    @Schema(title = "排序方式", description = "CREATE_TIME_ASC:创建时间升序, CREATE_TIME_DESC:创建时间降序, SALES_ASC:销量升序, SALES_DESC:销量降序")
    private String sortType;

    @Schema(title = "是否实拍商品")
    private Boolean realPhoto;

    @Schema(title = "是否发货快")
    private Boolean fastDelivery;

    @Schema(title = "查询类型", description = "1-推荐, 2-上新, 3-实拍, 4-发货快, 5-热销")
    private Integer queryType;

    @Schema(title = "市场")
    private String market;

    public <T> QueryWrapper<T> queryWrapper() {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        if (CollUtil.isNotEmpty(storeIds)) {
            queryWrapper.in("id", storeIds);
        }
        if (verify != null) {
            queryWrapper.eq("verify", verify);
        }
        if (CharSequenceUtil.isNotEmpty(storeName)) {
            queryWrapper.eq("store_name", storeName);
        }
        if (CharSequenceUtil.isNotEmpty(storeStatus)) {
            queryWrapper.eq("store_status", storeStatus);
        }
        //按时间查询
        if (CharSequenceUtil.isNotEmpty(startDate)) {
            queryWrapper.ge("create_time", DateUtil.parse(startDate));
        }
        if (CharSequenceUtil.isNotEmpty(endDate)) {
            queryWrapper.le("create_time", DateUtil.parse(endDate));
        }

        if (CharSequenceUtil.isNotEmpty(field1)) {
            queryWrapper.eq("field1", field1);
        }
        if (CharSequenceUtil.isNotEmpty(field2)) {
            queryWrapper.eq("field2", field2);
        }
        if (CharSequenceUtil.isNotEmpty(field3)) {
            queryWrapper.eq("field3", field3);
        }
        if (CharSequenceUtil.isNotEmpty(field4)) {
            queryWrapper.eq("field4", field4);
        }
        if (CharSequenceUtil.isNotEmpty(field5)) {
            queryWrapper.eq("field5", field5);
        }
        if (CharSequenceUtil.isNotEmpty(scene)) {
            queryWrapper.eq("scene", SceneEnums.getScene(scene).name());
        }
        if (CharSequenceUtil.isNotEmpty(legalPerson)) {
            queryWrapper.eq("legal_person", legalPerson);
        }
        if (CollUtil.isNotEmpty(storeRegionIdList)) {
            String storeRegionIds = storeRegionIdList.stream().collect(Collectors.joining(","));
            queryWrapper.apply("find_in_set ('" + storeRegionIds + "', region_ids)");
        }
        if (CharSequenceUtil.isNotEmpty(storeTag)) {
            queryWrapper.like("store_tag", storeTag);
        }
        if (realPhoto != null) {
            queryWrapper.eq("real_photo", realPhoto);
        }

        if (fastDelivery != null) {
            queryWrapper.eq("fast_delivery", fastDelivery);
        }

        if (CharSequenceUtil.isNotEmpty(market)) {
            queryWrapper.like("market", market);
        }

        // 根据查询类型处理不同的查询逻辑
        if (queryType != null) {
            StoreQueryTypeEnum queryTypeEnum = StoreQueryTypeEnum.getByCode(queryType);
            switch (queryTypeEnum) {
                case RECOMMEND:
                    // 1 - 推荐店铺（有标签的优质店铺）
                    queryWrapper.eq("store_tag", "RECOMMEND");
                    break;
                case NEW_ARRIVAL:
                    // 2 - 上新店铺（按创建时间降序）
                    queryWrapper.orderByDesc("create_time");
                    break;
                case REAL_PHOTO:
                    // 3 - 实拍店铺
                    queryWrapper.eq("real_photo", true);
                    break;
                case FAST_DELIVERY:
                    // 4 - 发货快店铺
                    queryWrapper.eq("fast_delivery", true);
                    break;
                case HOT_SALES:
                    // 5 - 热销店铺（按销量排序）
                    queryWrapper.orderByDesc("sale_num");
                    break;
                default:
                    break;
            }
        }

        // 排序处理（如果没有通过queryType设置排序，则使用sortType）
        if (queryType == null || (!StoreQueryTypeEnum.HOT_SALES.getCode().equals(queryType) && !StoreQueryTypeEnum.NEW_ARRIVAL.getCode().equals(queryType))) {
            if (CharSequenceUtil.isNotEmpty(sortType)) {
                switch (sortType) {
                    case "CREATE_TIME_ASC":
                        queryWrapper.orderByAsc("create_time");
                        break;
                    case "CREATE_TIME_DESC":
                        queryWrapper.orderByDesc("create_time");
                        break;
                    case "SALES_ASC":
                        queryWrapper.orderByAsc("goods_num");
                        break;
                    case "SALES_DESC":
                        queryWrapper.orderByDesc("goods_num");
                        break;
                    default:
                        queryWrapper.orderByDesc("create_time");
                        break;
                }
            } else if (queryType == null) {
                // 如果没有指定查询类型和排序类型，默认按创建时间降序
                queryWrapper.orderByDesc("create_time");
            }
        }

        return queryWrapper;
    }
}
