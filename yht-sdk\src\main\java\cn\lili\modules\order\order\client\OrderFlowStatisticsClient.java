package cn.lili.modules.order.order.client;

import cn.lili.modules.constant.ServiceConstant;
import cn.lili.modules.order.fallback.OrderFlowStatisticsFallback;
import cn.lili.modules.statistics.entity.dto.GoodsStatisticsQueryParam;
import cn.lili.modules.statistics.entity.vo.CategoryStatisticsDataVO;
import cn.lili.modules.statistics.entity.vo.GoodsStatisticsDataVO;
import cn.lili.modules.statistics.entity.vo.OrderOverviewVO;
import cn.lili.modules.statistics.entity.vo.StoreStatisticsDataVO;
import cn.lili.modules.store.entity.dto.StoreStatisticsOverviewSearchParams;
import cn.lili.modules.store.entity.dto.StoreStatisticsSearchParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/6/24
 **/
@FeignClient(name = ServiceConstant.ORDER_SERVICE,
        contextId = "order-flow-statistics", fallback = OrderFlowStatisticsFallback.class)
public interface OrderFlowStatisticsClient {

    /**
     * 查询热卖商品
     * 查询TOP100的商品
     *
     * @param goodsStatisticsQueryParam 查询参数
     * @param num                       数量
     * @return
     */
    @PostMapping("/feign/order/store/flow/statistics/getGoodsStatisticsData/{num}")
    List<GoodsStatisticsDataVO> getGoodsStatisticsData(@RequestBody GoodsStatisticsQueryParam goodsStatisticsQueryParam, @PathVariable Integer num);

    /**
     * 根据查询条件获取商品销量统计
     *
     * @param queryDTO 查询条件
     * @return 商品销量统计
     */
    @PostMapping("/feign/order/store/flow/statistics/getGoodsSalesVolume")
    long getGoodsSalesVolume(@RequestBody GoodsStatisticsQueryParam queryDTO);

    /**
     * 查询行业统计
     * 根据商品一级分类ID查询
     *
     * @param goodsStatisticsQueryParam 查询参数
     * @return
     */
    @PostMapping("/feign/order/store/flow/statistics/getCategoryStatisticsData")
    List<CategoryStatisticsDataVO> getCategoryStatisticsData(@RequestBody GoodsStatisticsQueryParam goodsStatisticsQueryParam);

    /**
     * 店铺流水 根据店铺 统计数据
     * @param searchParams 查询参数
     * @return 店铺统计数据
     */
    @PostMapping("/feign/order/store/flow/statistics/getStoreStatisticsData")
    List<StoreStatisticsDataVO> getStoreStatisticsData(@RequestBody StoreStatisticsSearchParams searchParams);

    /**
     * 查询今日付款统计
     *
     * @return 订单统计金额
     */
    @GetMapping("/feign/order/store/flow/statistics/getOrderStatisticsPrice")
    Map<String, Object> getOrderStatisticsPrice(@RequestParam String storeId);

    /**
     * 订单统计，数据概览
     */
    @PostMapping("/feign/order/store/flow/statistics/overview")
    OrderOverviewVO overview(@RequestBody StoreStatisticsOverviewSearchParams searchParams);

    /**
     * 获取商品销售量
     */
    @GetMapping("/feign/order/store/flow/statistics/getSaleNumByStoreId/{storeId}")
    GoodsStatisticsDataVO getSaleNumByStoreId(@PathVariable String storeId);
}
