package cn.lili.modules.order.order.task;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.event.TransactionCommitSendMQEvent;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.modules.member.client.MemberEvaluationClient;
import cn.lili.modules.member.entity.dto.MemberEvaluationDTO;
import cn.lili.modules.member.entity.enums.EvaluationGradeEnum;
import cn.lili.modules.order.aftersale.service.AfterSaleService;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderFlow;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.dto.OrderFlowLog;
import cn.lili.modules.order.order.entity.dto.OrderSearchParams;
import cn.lili.modules.order.order.entity.enums.CommentStatusEnum;
import cn.lili.modules.order.order.entity.enums.FlowLogTypeEnum;
import cn.lili.modules.order.order.entity.enums.OrderComplaintStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.order.order.service.OrderFlowService;
import cn.lili.modules.order.order.service.OrderItemService;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.payment.entity.dos.PaymentSubsidies;
import cn.lili.modules.system.client.SettingClient;
import cn.lili.modules.system.entity.dos.Setting;
import cn.lili.modules.system.entity.dto.OrderSetting;
import cn.lili.modules.system.entity.enums.SettingEnum;
import java.util.List;

import cn.lili.routing.GoodsRoutingKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 订单每日任务
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class OrderEveryDayTask {

    /**
     * 设置
     */
    private final SettingClient settingClient;
    /**
     * 会员评价
     */
    private final MemberEvaluationClient memberEvaluationClient;
    /**
     * 订单
     */
    private final OrderService orderService;
    /**
     * 订单流水
     */
    private final OrderFlowService orderFlowService;
    /**
     * 订单货物
     */
    private final OrderItemService orderItemService;

    private final AfterSaleService afterSaleService;
    /**
     * 执行每日任务
     */
    public void execute() {

        Setting setting = settingClient.get(SettingEnum.ORDER_SETTING.name());
        //订单设置
        OrderSetting orderSetting = JSONUtil.toBean(setting.getSettingValue(), OrderSetting.class);
        if (orderSetting == null) {
            throw new ServiceException(ResultCode.ORDER_SETTING_ERROR);
        }

        //自动确认收货
        try {
            completedOrder(orderSetting);
        } catch (Exception e) {
            log.error("自动确认收货异常", e);
        }
        //自动好评
        try {
            memberEvaluation(orderSetting);
        } catch (Exception e) {
            log.error("自动好评异常", e);
        }
        //强制关闭售后
        try {
            afterSaleService.forceEndAfterSale();
        } catch (Exception e) {
            log.error("强制关闭售后失败", e);
        }
        //关闭允许售后申请
        try {
            closeAfterSale(orderSetting);
        } catch (Exception e) {
            log.error("关闭允许售后申请异常", e);
        }
        //关闭允许投诉
        try {
            closeComplaint(orderSetting);
        } catch (Exception e) {
            log.error("关闭允许投诉异常", e);
        }

        try {
            //自动分账
            profitSharing();
        } catch (Exception e) {
            log.error("自动分账异常", e);
        }
        try {
            //分账状态更新
            profitSharingUpdate();
        } catch (Exception e) {
            log.error("自动分账异常", e);
        }
    }

    /**
     * 自动分账
     */
    private void profitSharing() {
        //将待分账流水状态更新
        orderFlowService.updateProfitSharingStatus();
        //获取所有待分账的订单
        List<OrderFlow> orderFlows = orderFlowService.waitProfitSharingFlows();
        //订单挨个分账
        for (OrderFlow orderFlow : orderFlows) {
            log.info("订单分账开始-订单号：{}, 分账状态:{}, ", orderFlow.getSn(), orderFlow.getProfitSharing());
            try {
                // 获取订单流水日志
                List<OrderFlowLog> orderFlowLogs = orderFlow.getOrderFlowPaymentLog();

                // 写入日志信息
                orderFlow.setOrderFlowPaymentLog(orderFlowLogs);
                // 分账
                orderFlowService.profitSharingOrderFlow(orderFlow);
            } catch (ServiceException e) {
                // 捕获异常进行记录
                orderFlow.setErrorMsg(e.getMessage());
                orderFlowService.updateById(orderFlow);
            } catch (Exception e) {
                log.error("订单分账失败：{}", orderFlow.getSn(), e);
            }
        }
    }

    /**
     * 分账状态更新
     */
    private void profitSharingUpdate() {
        //获取所有待分账的订单
        List<OrderFlow> orderFlows = orderFlowService.waitUpdateProfitSharingFlows();

        //订单挨个分账
        for (OrderFlow orderFlow : orderFlows) {
            try {
                // 分账
                orderFlowService.profitSharingOrderFlow(orderFlow);
            } catch (ServiceException e) {
                // 捕获异常进行记录
                orderFlow.setErrorMsg(e.getMessage());
                orderFlowService.updateById(orderFlow);
            } catch (Exception e) {
                log.error("订单分账失败：{}", orderFlow.getSn(), e);
            }
        }
    }

    /**
     * 自动确认收获，订单完成
     *
     * @param orderSetting 订单设置
     */
    private void completedOrder(OrderSetting orderSetting) {
        //订单自动收货时间 = 当前时间 - 自动收货时间天数
        DateTime receiveTime = DateUtil.offsetDay(DateUtil.date(), -orderSetting.getAutoReceive());
        OrderSearchParams searchParams = new OrderSearchParams();
        searchParams.setOrderStatus(OrderStatusEnum.DELIVERED.name());
        //订单发货时间 >= 订单自动收货时间
        searchParams.setLeLogisticsTime(receiveTime);
        List<Order> list = orderService.queryListByParams(searchParams);
        for (Order order : list) {
            try {
                orderService.complete(order.getSn());
            } catch (Exception e) {
                log.error("订单自动完成失败  {}：", order.getSn(), e);
            }
        }
    }

    /**
     * 自动好评
     *
     * @param orderSetting 订单设置
     */
    private void memberEvaluation(OrderSetting orderSetting) {
        //订单自动收货时间 = 当前时间 - 自动收货时间天数
        DateTime receiveTime = DateUtil.offsetDay(DateUtil.date(), -orderSetting.getAutoEvaluation());

        //订单完成时间 <= 订单自动好评时间
        OrderSearchParams searchParams = new OrderSearchParams();
        searchParams.setCommentStatus(CommentStatusEnum.UNFINISHED.name());
        searchParams.setLeCompleteTime(receiveTime);

        List<OrderItem> orderItems = orderItemService.waitOperationOrderItem(searchParams);

        //判断是否有符合条件的订单，进行自动评价处理
        if (!orderItems.isEmpty()) {
            for (OrderItem orderItem : orderItems) {
                try {
                    MemberEvaluationDTO memberEvaluationDTO = new MemberEvaluationDTO();
                    memberEvaluationDTO.setOrderItemSn(orderItem.getSn());
                    memberEvaluationDTO.setContent("系统默认好评");
                    memberEvaluationDTO.setGoodsId(orderItem.getGoodsId());
                    memberEvaluationDTO.setSkuId(orderItem.getSkuId());
                    memberEvaluationDTO.setGrade(EvaluationGradeEnum.GOOD.name());
                    memberEvaluationDTO.setDeliveryScore(5);
                    memberEvaluationDTO.setDescriptionScore(5);
                    memberEvaluationDTO.setServiceScore(5);

                    memberEvaluationClient.addMemberEvaluation(memberEvaluationDTO, false);
                } catch (Exception e) {
                    // 修改订单货物评价状态为已评价避免无限调用评价异常
                    orderItemService.updateCommentStatus(orderItem.getSn(), CommentStatusEnum.FINISHED);
                    log.error("订单自动好评失败  {}：", orderItem.getSn(), e);
                }
            }
        }
    }


    /**
     * 关闭允许售后申请
     *
     * @param orderSetting 订单设置
     */
    private void closeAfterSale(OrderSetting orderSetting) {

        //订单关闭售后申请时间 = 当前时间 - 自动关闭售后申请天数
        DateTime expiredTime = DateUtil.offsetDay(DateUtil.date(), -orderSetting.getCloseAfterSale());

        //售后过期标识
        orderItemService.expiredAfterSaleStatus(expiredTime);
    }


    /**
     * 关闭允许交易投诉
     *
     * @param orderSetting 订单设置
     */
    private void closeComplaint(OrderSetting orderSetting) {

        //订单关闭交易投诉申请时间 = 当前时间 - 自动关闭交易投诉申请天数
        DateTime receiveTime = DateUtil.offsetDay(DateUtil.date(), -orderSetting.getCloseComplaint());

        //关闭售后订单=未售后订单+小于订单关闭售后申请时间
        OrderSearchParams searchParams = new OrderSearchParams();
        searchParams.setLeCompleteTime(receiveTime);
        searchParams.setComplainStatus(OrderComplaintStatusEnum.NO_APPLY.name());
        List<OrderItem> orderItems = orderItemService.waitOperationOrderItem(searchParams);

        //判断是否有符合条件的订单，关闭允许售后申请处理
        if (!orderItems.isEmpty()) {
            //获取订单货物ID
            List<String> orderItemIdList = orderItems.stream().map(OrderItem::getId).toList();
            //修改订单投诉状态
            orderItemService.updateComplainStatus(orderItemIdList);
        }

    }

}
