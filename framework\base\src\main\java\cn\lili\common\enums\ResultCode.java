package cn.lili.common.enums;

/**
 * 返回状态码
 * 第一位 1:商品；2:用户；3:交易,4:促销,5:店铺,6:页面,7:设置,8:其他
 *
 * <AUTHOR>
 * @since 2020/4/8 1:36 下午
 */
public enum ResultCode {

    /**
     * 成功状态码
     */
    SUCCESS(200, "成功"),

    /**
     * 失败返回码
     */
    ERROR(400, "服务器繁忙，请稍后重试"),

    /**
     * 失败返回码
     */
    DEMO_SITE_EXCEPTION(404, "演示站点禁止使用"),
    /**
     * 参数异常
     */
    PARAMS_ERROR(402, "参数异常"),
    /**
     * 权限不足
     */
    PERMISSIONS_DENIED(403, "权限不足"),    /**
     * 自定义日期格式化解析异常
     */
    DATE_PARSE_ERROR(405, "日期格式化解析异常"),


    /**
     * 5XX
     * oauth2 错误
     */
    OAUTH_CODE_EMPTY(401, "用户未登录或已失效，请重新尝试"),
    /**
     * 系统异常
     */
    SCENE_NOT_SUPPORT(1000, "场景传入错误"),
    WECHAT_CONNECT_NOT_EXIST(1001, "微信联合登录未配置"),
    VERIFICATION_EXIST(1002, "验证码服务异常"),
    LIMIT_ERROR(1003,
            "访问过于频繁，请稍后再试"),
    ILLEGAL_REQUEST_ERROR(1004, "非法请求，请重新刷新页面操作"),
    IMAGE_FILE_EXT_ERROR(1005, "不支持图片格式"),
    PLATFORM_NOT_SUPPORTED_IM(1006, "平台未开启IM"),
    STORE_NOT_SUPPORTED_IM(1007, "店铺未开启IM"),
    UNINITIALIZED_PASSWORD(1008,
            "非初始化密码，无法进行初始化设置"),
    STORE_BUSINESS_CATEGORY_NOT_EXIST(1009, "店铺未设置经营类目无法发布商品"),


    FILE_TYPE_NOT_SUPPORT(1010, "不支持上传的文件类型！"),
    FILE_IMPORT_ERROR(1011, "文件导入失败！"),
    FILE_NOT_EXIST_ERROR(1012, "上传文件不能为空"),

    /**
     * 分类
     */
    CATEGORY_NOT_EXIST(10001, "商品分类不存在"),

    CATEGORY_NAME_IS_EXIST(10002, "该分类名称已存在"),
    CATEGORY_PARENT_NOT_EXIST(10003, "该分类名称已存在"),
    CATEGORY_BEYOND_THREE(10004, "最多为三级分类,添加失败"),
    CATEGORY_HAS_CHILDREN(10005, "此类别下存在子类别不能删除"),
    CATEGORY_HAS_GOODS(10006, "此类别下存在商品不能删除"),
    CATEGORY_SAVE_ERROR(10007, "此类别下存在商品不能删除"),
    CATEGORY_PARAMETER_SAVE_ERROR(10008, "分类绑定参数组添加失败"),
    CATEGORY_PARAMETER_UPDATE_ERROR(10009, "分类绑定参数组添加失败"),
    CATEGORY_DELETE_FLAG_ERROR(10010, "子类状态不能与父类不一致！"),
    CATEGORY_COMMISSION_RATE_ERROR(10011, "分类的佣金不正确！"),
    CATEGORY_PARAMETER_NOT_EXIST(10012, "分类绑定参数组不存在"),
    CATEGORY_NEED_EXIST(10013, "商品分类不存在或被禁用，请先选择一个分类"),
    BRAND_NEED_EXIST(10014, "品牌不存在或被禁用，请先选择一个品牌"),
    CATEGORY_DOWNLOAD_ERROR(10015, "下载分类模版失败！"),
    CATEGORY_IMPORT_ERROR(10016, "导入分类失败，请检查文件。"),
    GOODS_UNIT_NAME_REPEAT(10017, "商品单位名称重复"),
    PAYMENT_SETTING_ERROR(100118,"请配置提现设置"),
    NO_BUSINESS_CATEGORY_AVAILABLE(10020, "暂无经营类目，无法发布商品，请联系管理员！"),
    /**
     * 商品
     */
    GOODS_ERROR(11000, "商品异常，请稍后重试"),
    GOODS_NOT_EXIST(11001, "商品已下架"),
    GOODS_NAME_ERROR(11002, "商品名称不正确，名称应为2-50字符"),
    GOODS_UNDER_ERROR(11003, "商品下架失败"),
    GOODS_UPPER_ERROR(11004, "商品上架失败"),
    GOODS_AUTH_ERROR(11005, "商品审核失败"),
    POINT_GOODS_ERROR(11006, "积分商品业务异常，请稍后重试"),
    GOODS_SKU_SN_ERROR(11007, "商品SKU货号不能为空"),
    GOODS_SKU_PRICE_ERROR(11008, "商品SKU价格不能小于等于0"),
    GOODS_SKU_COST_ERROR(11009, "商品SKU成本价不能小于等于0"),
    GOODS_SKU_WEIGHT_ERROR(11010, "商品重量需要大于0"),
    GOODS_SKU_QUANTITY_ERROR(11011, "商品库存数量不能为负数"),
    MUST_HAVE_GOODS_SKU(11012, "规格必须要有一个！"),
    GOODS_PROXY_TOO_MUCH(11013, "商品无法被多次代理"),
    BUSINESS_UN_PROXY(11014, "没有对应的经营类目，请联系平台处理"),
    GOODS_SKU_SUPPLIER_ADD_ERROR(11117, "供应商商品SKU添加失败"),
    SUPPLIER_GOODS_MUST_SUPPORT(11018, "供应商商品必须选择一种销售模式，支持代发或者支持采购"),
    PURCHASE_RULE_CANT_BE_NULL(11019, "采购规则不能为空"),
    SPU_PURCHASE_NEED_RULE(11020, "商品采购模式必须配置采购规则"),
    PURCHASE_RULE_MAX_THREE(11021, "采购规则最多配置三条"),
    GOODS_UNIT_ID_NOT_NULL(11022, "商品单位ID不能为空"),
    PURCHASE_RULE_NUM_DUPLICATE(11022, "采购规则起购数量重复"),
    PURCHASE_RULE_PRICE_ERROR(11023, "采购阶梯价格错误"),
    GOODS_UNIT_NAME_NOT_NULL(11024, "商品单位名称不能为空"),
    GOODS_IMPORT_REQUIRED_ERROR(11026, "请检查并完善商品信息"),
    GOODS_IMPORT_SIZE_ERROR(11027, "没有上传数据"),
    GOODS_IMPORT_SPEC_ERROR(11028, "规格项需要与第一个规格项对应，请参考商品规格表头的说明"),
    HAVE_INVALID_SALES_MODEL(11029, "批发规则存在小于等于0的无效数据！"),
    MUST_HAVE_GOODS_SKU_VALUE(11030, "规格值不能为空！"),
    GOODS_NOT_SUPPORT_PURCHASE(11032, "该商品不支持采购"),
    PROXY_GOODS_CANT_UPDATE_STOCK(11033, "代理商无法修改供应商商品库存"),
    PROXY_GOODS_CANT_UPDATE_HEAR(11034, "代理商品无法请求当前商品修改接口"),
    NORMAL_GOODS_CANT_UPDATE_HEAR(11035, "标准商品无法请求当前商品修改接口"),
    PROXY_PRICE_CANT_SMALL_THAN_ORIGIN(11036, "代理价格不能小于原价"),
    GOODS_USED_IN_PAGE_DECORATION(11037, "商品正在装修页面中使用，不允许下架"),

    POINT_GOODS_NOT_EXIST(11100, "积分商品不存在"),
    POINT_GOODS_CATEGORY_EXIST(11101, "当前积分商品分类已存在"),
    MUST_HAVE_SALES_MODEL(11102, "销售模式为批发时必须要有批发规则！"),

    /**
     * 参数
     */
    PARAMETER_SAVE_ERROR(12001, "参数添加失败"),
    PARAMETER_UPDATE_ERROR(12002, "参数编辑失败"),

    /**
     * 规格
     */
    SPEC_SAVE_ERROR(13001, "规格修改失败"),
    SPEC_UPDATE_ERROR(13002, "规格修改失败"),
    SPEC_DELETE_ERROR(13003, "分类已经绑定此规格，请先解除关联"),

    /**
     * 品牌
     */
    BRAND_SAVE_ERROR(14001, "品牌添加失败"),
    BRAND_UPDATE_ERROR(14002, "品牌修改失败"),
    BRAND_DISABLE_ERROR(14003, "品牌禁用失败"),
    BRAND_DELETE_ERROR(14004,
            "品牌删除失败"),
    BRAND_NAME_EXIST_ERROR(14005, "品牌名称重复！"),
    BRAND_USE_DISABLE_ERROR(14006, "品牌已经绑定分类，请先解除分类关联"),
    BRAND_BIND_GOODS_ERROR(14007,
            "品牌已经绑定商品，请先解除关联"),
    BRAND_NOT_EXIST(14008, "品牌不存在"),


    GOODS_PARAMS_SAVE_ERROR(15001, "商品参数添加失败"),
    GOODS_PARAMS_UPDATE_ERROR(15002, "商品参数修改失败"),
    GOODS_PARAMS_DISABLE_ERROR(15003, "商品参数禁用失败"),
    GOODS_PARAMS_DELETE_ERROR(15004,
            "商品参数删除失败"),

    GOODS_PARAMS_NOT_EXIST(15005, "商品参数名不存在"),

    GOODS_PARAMS_EXIST_ERROR(15006, "商品参数名称重复！"),

    /**
     * 用户
     */
    USER_DISABLE(20001, "用户被禁用，请联系客服处理。"),
    USER_NOT_EXIST(20002, "账号或密码输入有误"),

    USER_NOT_LOGIN(20003, "用户未登录"),
    USER_AUTH_EXPIRED(20004, "用户已退出，请重新登录"),
    USER_AUTHORITY_ERROR(20005, "权限不足"),
    USER_CONNECT_LOGIN_ERROR(20006, "未找到登录信息"),
    USER_EXIST(20008, "该用户名或手机号已被注册"),
    USER_PHONE_NOT_EXIST(20009, "手机号不存在"),
    USER_PASSWORD_ERROR(20010, "密码不正确"),
    USER_NOT_PHONE(20011, "非当前用户的手机号"),
    USER_CONNECT_ERROR(20012, "联合第三方登录，授权信息错误"),
    USER_RECEIPT_REPEAT_ERROR(20013, "会员发票信息重复"),
    USER_RECEIPT_NOT_EXIST(20014, "会员发票信息不存在"),
    USER_EDIT_ERROR(20015, "用户修改失败"),
    USER_OLD_PASSWORD_ERROR(20016, "旧密码不正确"),
    USER_COLLECTION_EXIST(20017, "无法重复收藏"),
    USER_NOT_BINDING(20020, "未绑定用户"),
    USER_AUTO_REGISTER_ERROR(20021, "自动注册失败,请稍后重试"),
    USER_OVERDUE_CONNECT_ERROR(20022, "授权信息已过期，请重新授权/登录"),
    USER_CONNECT_BANDING_ERROR(20023, "当前联合登陆方式，已绑定其他账号，需进行解绑操作"),
    USER_CONNECT_NOT_EXIST_ERROR(20024, "暂无联合登陆信息，无法实现一键注册功能，请点击第三方登录进行授权"),
    USER_POINTS_ERROR(20025, "用户积分不足"),
    USER_LABEL_NOT_EXIST(20026, "用户标签信息不存在"),
    USER_LABEL_IS_BIND(20027, "用户标签已被绑定"),
    USER_LABEL_EXIST(20028, "用户标签已存在"),
    USER_NOT_PASSWORD_ERROR(20030,"用户没有设置支付密码"),
    USER_BOUND_PHONE_ALREADY(20031,"当前手机号已经被其他用户绑定"),

    USER_BOUND_PHONE_ALREADY_CURRENT(20032,"当前手机号已经绑定当前用户，无需重复绑定"),
    USER_NEW_PASSWORD_EQUALS_OLD(20033, "新密码不能与旧密码相同"),
    USER_PASSWORD_LENGTH_ERROR(20034, "密码长度必须在6-20位之间"),

    CLERK_SUPPER(20100, "店长无法操作"),
    CLERK_SAVE_ERROR(20101, "店员保存失败"),
    CLERK_NOT_FOUND_ERROR(20102, "店员不存在"),
    USER_STATUS_ERROR(20103, "用户已禁用"),
    CLERK_USER_ERROR(20104, "此账户已经绑定其他店铺"),
    CLERK_ALREADY_EXIT_ERROR(20105, "店员已经存在"),
    CLERK_DISABLED_ERROR(20106, "店员已禁用"),
    CLERK_CURRENT_SUPPER(20107, "无法删除当前登录店员"),
    USER_MOBILE_REPEATABLE_ERROR(20108, "手机号重复"),


    //不能删除超级管理员
    USER_SUPER_DELETE_ERROR(20201, "不能删除超级管理员"),
    //不能删除自己
    USER_DELETE_ERROR(20202, "不能删除自己"),
    /**
     * 权限
     */
    ROLE_BOUND_BY_DEPARTMENT(21001, "角色已绑定部门，请逐个删除"),
    ROLE_BOUND_BY_USER(21002, "角色已绑定用户，请逐个删除"),
    MENU_BOUND_BY_ROLE(21003, "菜单已绑定角色，请逐个删除"),
    DEPARTMENT_BOUND_BY_USER(21004, "部门已经绑定角色，请逐个删除"),
    PERMISSION_BEYOND_FIVE(21005, "最多可以设置5个角色"),
    DEPARTMENT_NOT_FOUND_ERROR(21006, "部门不存在"),
    ROLE_NOT_FOUND_ERROR(21007, "角色不存在"),
    DEPARTMENT_PARENT_ERROR(21008, "上级部门不能是自己"),
    DEPARTMENT_NOT_EXIST(21009, "部门不存在"),
    ROLE_NAME_REPEATABLE(21010,"角色名称重复"),
    DEPARTMENT_CHANGE_PARENT_ERROR(210011, "顶级部门不能修改上级部门"),
    /**
     * 分销
     */
    DISTRIBUTION_CLOSE(22000, "分销功能关闭"),
    DISTRIBUTION_NOT_EXIST(22001, "分销员不存在"),
    DISTRIBUTION_IS_APPLY(22002, "分销员已申请，无需重复提交"),
    DISTRIBUTION_IS_APPEAL(22003, "分销员已申诉，无需重复申诉"),
    DISTRIBUTION_IS_NOT_APPLY(22004, "分销员未申请，无法进行申诉"),
    DISTRIBUTION_AUDIT_ERROR(22005, "审核分销员失败"),
    DISTRIBUTION_RETREAT_ERROR(22006, "分销员清退失败"),
    DISTRIBUTION_CASH_NOT_EXIST(22007, "分销员提现记录不存在"),
    DISTRIBUTION_GOODS_DOUBLE(22008, "不能重复添加分销商品"),
    DISTRIBUTION_STORE_NOT_EXIST(22009, "尚未设置分销佣金比例"),
    DISTRIBUTION_COMMISSION_ERROR(22010, "分销佣金比例设置异常"),
    DISTRIBUTION_COMMISSION_RATE_ERROR(22011, "分销佣金比例 0%-10%"),

    ADDRESS_UN_SETTING(22012, "用户未设置收货地址，请与其协商"),
    STORE_ADDRESS_UN_SETTING(22013, "商家未设置收货地址，请与其协商"),

    ADDRESS_NOT_EXIST(22014, "收货地址不存在"),
    ADDRESS_DEFAULT_ERROR(22015, "仅有一组地址，无法修改为非默认地址"),
    /**
     * 购物车
     */
    CART_ERROR(30001, "读取结算页的购物车异常"),
    CART_NUM_ERROR(30010, "购买数量必须大于0"),
    CART_PINTUAN_NOT_EXIST_ERROR(30002, "拼团活动已关闭，请稍后重试"),
    CART_PINTUAN_LIMIT_ERROR(30003, "购买数量超过拼团活动限制数量"),
    SHIPPING_NOT_APPLY(30005, "购物商品不支持当前收货地址配送"),

    /**
     * 订单
     */
    ORDER_ERROR(31001, "创建订单异常，请稍后重试"),
    ORDER_NOT_EXIST(31002, "订单不存在"),
    ORDER_UPDATE_PRICE_ERROR(31003, "已支付的订单不能修改金额"),
    ORDER_LOGISTICS_ERROR(31004, "物流错误"),
    ORDER_PAID_CANNOT_CANCEL(31005, "已付款订单无法取消，请申请售后处理"),
    ORDER_DELIVER_ERROR(31006, "订单已发货"),
    ORDER_NOT_USER(31007
            , "非当前会员的订单"),
    ORDER_TAKE_ERROR(31008, "当前订单无法核销"),
    MEMBER_ADDRESS_NOT_EXIST(31009, "订单无收货地址，请先配置收货地址"),
    ORDER_DELIVER_NUM_ERROR(31010,
            "没有待发货的订单"),

    ORDER_NOT_SUPPORT_DISTRIBUTION(31011, "购物车中包含不支持配送的商品，请重新选择收货地址，或者重新选择商品："),
    GOODS_SKU_QUANTITY_NOT_ENOUGH(31012, "商品库存不足"),
    STORE_ADDRESS_NOT_EXIST(31013, "自提地址未选择，自提订单需选择自提地址"),
    ORDER_NOT_EXIST_VALID(31014,
            "购物车中无有效商品，请检查购物车内商品，或者重新选择商品"),
    ORDER_CAN_NOT_CANCEL(31015, "当前订单状态不可取消"),
    ORDER_PINTUAN_CANNOT_CANCEL(31016, "当前订单为拼团订单，订单未成团，不能取消订单"),
    ORDER_BATCH_DELIVER_ERROR(31017, "批量发货,文件读取失败"),
    ORDER_LOGISTICS_NOT_COMPLETE(31018, "批量发货，文件缺失订单物流信息，请完善！"),
    ORDER_ITEM_NOT_EXIST(31019, "当前订单项不存在！"),
    POINT_NOT_ENOUGH(31020, "当前用户积分不足购买当前积分商品！"),
    ORDER_VERIFY_ERROR(31021, "当前订单金额存在问题，需手动审核验证"),
    ORDER_GIFT_DELIVER_ERROR(31022, "赠品订单在所属订单发货后才可以进行发货"),
    ORDER_PACKAGE_NOT_EXIST(31023, "当前订单包裹不存在！"),
    ORDER_LABEL_ORDER_ERROR(31024, "订单不能打印电子面单"),
    ORDER_LABEL_ORDER_EXIST(31025, "订单打印电子面单错误，请检查面单参数"),
    ORDER_VERIFICATION_CODE_ERROR(31042, "核销码错误"),
    ORDER_VERIFICATION_CODE_DESTRUCTION_ERROR(31043, "核销码已销毁"),
    ORDER_AFTER_SALE_NOT_COMPLETE(31044, "售后单未完成，请等待系统处理"),
    ORDER_CANCEL_ERROR(31045,"已经申请售后的订单无法重复申请。"),
    ORDER_OPERATION_ERROR(31046, "订单无法进行当前操作"),
    TRADE_STATUS_ERROR(31100, "交易状态异常"),

    FREIGHT_NOT_SELECTED(31101, "请选择物流配送方式"),

    /**
     * 支付
     */
    PAY_UN_WANTED(32000, "当前订单不需要付款，返回订单列表等待系统订单出库即可"),
    PAY_INCONSISTENT_ERROR(32002, "付款金额和应付金额不一致"),
    PAY_DOUBLE_ERROR(32003, "订单已支付，不能再次进行支付"),
    PAY_CASHIER_ERROR(32004, "收银台信息获取错误"),
    PAY_ERROR(32005, "支付业务异常"),
    PAY_BAN(32006,
            "当前订单不需要付款，请返回订单列表重新操作"),
    PAY_NOT_SUPPORT(32008, "支付暂不支持"),
    PAY_CLIENT_TYPE_ERROR(32009, "错误的客户端"),
    PAY_POINT_ENOUGH(32010, "积分不足，不能兑换"),
    PAY_NOT_EXIST_ORDER(32011, "支付订单不存在"),
    CAN_NOT_RECHARGE_WALLET(32012, "不能使用余额进行充值"),

    RECHARGE_ORDER_ERROR(32013, "充值订单异常"),


    /**
     * 售后
     */
    AFTER_SALES_NOT_PAY_ERROR(33001, "当前订单未支付，不能申请售后"),
    AFTER_SALES_CANCEL_ERROR(33002, "当前售后单无法取消"),
    AFTER_SALES_BAN(33003,
            "订单状态不允许申请售后，请联系平台或商家"),
    AFTER_SALES_DOUBLE_ERROR(33004, "售后已审核，无法重复操作"),
    AFTER_SALES_LOGISTICS_ERROR(33005, "物流公司错误，请重新选择"),
    AFTER_STATUS_ERROR(33006, "售后状态错误，请刷新页面"),
    AFTER_SALE_PRICE_ERROR(33007,"退款金额不能小于或等于0"),
    AFTER_SALE_PRICE_EXCEED_ERROR(33008, "退款金额不能大于申请金额"),
    RETURN_MONEY_OFFLINE_BANK_ERROR(33009, "当账号类型为银行转账时，银行信息不能为空"),
    AFTER_SALES_PRICE_ERROR(33010, "申请退款金额错误"),
    AFTER_GOODS_NUMBER_ERROR(33011, "申请售后商品数量错误"),
    AFTER_SALE_REASON_EXIST(33012, "当前售后原因已存在"),
    AFTER_SALE_NOT_EXIST(33013, "售后单不存在"),
    AFTER_SALE_TYPE_ERROR(33014,"退款单无法操作"),

    /**
     * 投诉
     */
    COMPLAINT_ORDER_ITEM_EMPTY_ERROR(34100, "订单不存在"),
    COMPLAINT_SKU_EMPTY_ERROR(34101, "商品已下架，如需投诉请联系平台客服"),
    COMPLAINT_ERROR(34102, "投诉异常，请稍后重试"),
    COMPLAINT_NOT_EXIT(34103, "当前投诉记录不存在"),
    COMPLAINT_ARBITRATION_RESULT_ERROR(34104, "结束订单投诉时，仲裁结果不能为空"),
    COMPLAINT_APPEAL_CONTENT_ERROR(34105,
            "商家申诉时，申诉内容不能为空"),
    COMPLAINT_CANCEL_ERROR(34106, "申诉已完成，不需要进行取消申诉操作"),

    /**
     * 流水
     */
    ORDER_FLOW_NOT_EXIST(35001, "订单流水不存在"),
    ORDER_FLOW_NEED_NOT_VERIFY(35002, "订单流水不需要审核"),
    ORDER_FLOW_ERROR(35003, "异常流水数据错误，请联系管理员"),

    /**
     * 余额
     */
    WALLET_NOT_EXIT_ERROR(34000, "钱包不存在，请联系管理员"),
    WALLET_EXIT_ERROR(34001, "钱包已存在，无法重复创建"),
    WALLET_INSUFFICIENT(34002, "余额不足以支付订单，请充值!"),
    WALLET_ERROR_INSUFFICIENT(34003, "零钱提现失败！"),
    WALLET_WITHDRAWAL_INSUFFICIENT(34004, "可提现金额不足！"),
    WALLET_REMARK_ERROR(34005, "请填写审核备注！"),
    WALLET_APPLY_ERROR(34006, "提现申请异常！"),
    WALLET_WITHDRAWAL_AMOUNT_ERROR(34007, "提现金额错误！"),
    WALLET_WITHDRAWAL_FROZEN_AMOUNT_INSUFFICIENT(34008,
            "冻结金额不足，无法处理提现申请请求！"),

    WALLET_WITHDRAWAL_APPLY_NOT_EXIST(34009, "提现申请不存在！"),

    WALLET_WITHDRAWAL_APPLY_NOT_Day(34010, "提现时间不符合要求："),
    /**
     * 评价
     */
    EVALUATION_DOUBLE_ERROR(35001, "无法重复提交评价"),
    EVALUATION_NOT_EXIST(35002, "评价不存在"),

    /**
     * 活动
     */
    PROMOTION_GOODS_NOT_EXIT(40000, "当前促销商品不存在！"),
    PROMOTION_GOODS_QUANTITY_NOT_EXIT(40020, "当前促销商品库存不足！"),
    PROMOTION_GOODS_CAN_NOT_JOIN(40050, "特殊商品无法参与促销"),

    WHOLESALE_NUM_ERROR(40051, "批发数量错误"),
    PROMOTION_SAME_ACTIVE_EXIST(40001, "活动时间内已存在同类活动，请选择关闭、删除当前时段的活动"),
    PROMOTION_START_TIME_ERROR(40002, "活动起始时间不能小于当前时间"),
    PROMOTION_END_TIME_ERROR(40003, "活动结束时间不能小于当前时间"),
    PROMOTION_TIME_ERROR(40004,
            "活动起始时间必须大于结束时间"),
    PROMOTION_SAME_ERROR(40005, "当前时间段已存在相同活动！"),
    PROMOTION_GOODS_ERROR(40006, "请选择要参与活动的商品"),
    PROMOTION_STATUS_END(40007, "当前活动已停止"),
    PROMOTION_UPDATE_ERROR(40008, "当前活动已开始/结束，无法编辑！"),
    PROMOTION_ACTIVITY_GOODS_ERROR(40009, "当前活动已经开始无法添加商品"),
    PROMOTION_ACTIVITY_ERROR(400010, "当前促销活动不存在"),
    PROMOTION_TIME_NOT_EXIST(40011, "活动起始时间和活动结束时间不能为空"),
    PROMOTION_LOG_EXIST(40012,
            "活动已参加，请勿重复参加"),
    PROMOTION_NAME_EXIST(40013,"活动名称必须在2-30个字符之间"),

    /**
     * 优惠券
     */
    COUPON_LIMIT_ERROR(41000, "超出领取限制"),
    COUPON_EXPIRED(41003, "优惠券已使用/已过期，不能使用"),
    COUPON_EDIT_STATUS_ERROR(41004, "优惠券修改状态失败！"),
    COUPON_RECEIVE_ERROR(41005, "当前优惠券已经被领取完了，下次要早点来哦"),
    COUPON_NUM_INSUFFICIENT_ERROR(41006, "优惠券剩余领取数量不足"),
    COUPON_NOT_EXIST(41007, "当前优惠券不存在"),
    COUPON_LIMIT_NUM_LESS_THAN_0(41008, "领取限制数量不能为负数"),
    COUPON_LIMIT_GREATER_THAN_PUBLISH(41009,
        "领取限制数量超出发行数量"),
    COUPON_DISCOUNT_ERROR(41010, "优惠券折扣必须小于10且大于0"),
    COUPON_SCOPE_TYPE_GOODS_ERROR(41011, "当前关联范围类型为指定商品时，商品列表不能为空"),
    COUPON_SCOPE_TYPE_CATEGORY_ERROR(41012, "当前关联范围类型为部分商品分类时，范围关联的id不能为空"),
    COUPON_SCOPE_TYPE_STORE_ERROR(41013, "当前关联范围类型为部分店铺分类时，范围关联的id不能为空"),
    COUPON_SCOPE_ERROR(41014, "指定商品范围关联id无效！"),
    COUPON_MEMBER_NOT_EXIST(41015, "没有当前会员优惠券"),
    COUPON_MEMBER_STATUS_ERROR(41016,
        "当前会员优惠券已过期/作废无法变更状态！"),
    COUPON_ACTIVITY_FREQUENCY_ERROR(41017,
        "促销活动设置为自动赠券，则赠送频率不能为空"),
    COUPON_SELECTED_EXIST(41018, "该优惠券已被选择"),
    COUPON_ACTIVITY_NOT_EXIST(410022, "当前优惠券活动不存在"),
    COUPON_SAVE_ERROR(41020, "保存优惠券失败"),
    COUPON_ACTIVITY_SAVE_ERROR(41023, "保存优惠券活动失败"),
    COUPON_ACTIVITY_MAX_NUM(41024, "优惠券活动赠券数量最多为3"),
    COUPON_DELETE_ERROR(41021, "删除优惠券失败"),
    COUPON_DO_NOT_RECEIVER(41030, "当前优惠券不允许主动领取"),

    /**
     * 拼团
     */
    PINTUAN_MANUAL_OPEN_ERROR(42006, "手动开启拼团活动失败"),
    PINTUAN_MANUAL_CLOSE_ERROR(42007, "手动关闭拼团活动失败"),
    PINTUAN_ADD_ERROR(42008, "添加拼团活动失败"),
    PINTUAN_EDIT_ERROR(42009, "修改拼团活动失败"),
    PINTUAN_DELETE_ERROR(42010, "删除拼团活动失败"),
    PINTUAN_JOIN_ERROR(42011, "不能参与自己发起的拼团活动！"),
    PINTUAN_LIMIT_NUM_ERROR(42012, "购买数量超过拼团活动限制数量！"),
    PINTUAN_NOT_EXIST_ERROR(42013, "当前拼团活动不存在！"),
    PINTUAN_GOODS_NOT_EXIST_ERROR(42014,
            "当前拼团商品不存在！"),
    PINTUAN_EDIT_ERROR_ITS_OPEN(42019, "拼团活动已开启，无法修改拼团活动！"),

    /**
     * 满额活动
     */
    FULL_DISCOUNT_EDIT_DELETE(43002, "删除满优惠活动成功"),
    FULL_DISCOUNT_MODIFY_ERROR(43003,
            "当前编辑的满优惠活动已经开始或者已经结束，无法修改"),
    FULL_DISCOUNT_NOT_EXIST_ERROR(43004, "当前要操作的满优惠活动不存在！"),
    FULL_DISCOUNT_WAY_ERROR(43005, "请选择一种优惠方式！"),
    FULL_DISCOUNT_GIFT_ERROR(43006, "请选择赠品！"),
    FULL_DISCOUNT_COUPON_TIME_ERROR(43007, "赠送的优惠券有效时间必须在活动时间之内"),
    FULL_DISCOUNT_MONEY_ERROR(43008,
            "请填写满减金额"),
    FULL_DISCOUNT_MONEY_GREATER_THAN_MINUS(43009, "满减金额不能大于优惠门槛"),
    FULL_RATE_NUM_ERROR(43010, "请填写打折数值"),
    FULL_DISCOUNT_COUPON_ERROR(43011, "优惠券错误，请重新选择"),
    /**
     * 秒杀
     */
    SECKILL_NOT_START_ERROR(45000, "今日没有限时抢购活动，请明天再来看看吧。"),
    SECKILL_NOT_EXIST_ERROR(45001, "当前参与的秒杀活动不存在！"),
    SECKILL_UPDATE_ERROR(45002, "当前秒杀活动活动已经开始，无法修改！"),
    SECKILL_PRICE_ERROR(45003, "活动价格不能大于商品原价"),
    SECKILL_TIME_ERROR(45004, "时刻参数异常"),
    SECKILL_DELETE_ERROR(45005, "该秒杀活动活动的状态不能删除"),
    SECKILL_OPEN_ERROR(45010, "该秒杀活动活动的状态不能删除"),
    SECKILL_CLOSE_ERROR(45006, "该秒杀活动活动的状态不能关闭"),
    SECKILL_APPLY_NOT_EXIST_ERROR(45010,
        "当前参与的秒杀活动不存在或无效！"),

    /**
     * 优惠券活动
     */
    COUPON_ACTIVITY_START_TIME_ERROR(46001, "活动时间小于当前时间，不能进行编辑删除操作"),
    COUPON_ACTIVITY_MEMBER_ERROR(46002, "指定精准发券则必须指定会员，会员不可以为空"),
    COUPON_ACTIVITY_ITEM_ERROR(46003, "优惠券活动必须指定优惠券，不能为空"),
    COUPON_ACTIVITY_ITEM_MUST_NUM_ERROR(46004, "优惠券活动最多指定10个优惠券"),
    COUPON_ACTIVITY_ITEM_NUM_ERROR(46005, "赠券数量必须大于0"),

    /**
     * 其他促销
     */
    MEMBER_SIGN_REPEAT(47001, "请勿重复签到"),
    POINT_GOODS_ACTIVE_STOCK_ERROR(47002, "活动库存数量不能高于商品库存"),
    POINT_GOODS_ACTIVE_STOCK_MUST_ERROR(47004, "活动库存数量必须大于0"),
    POINT_GOODS_ACTIVE_STOCK_INSUFFICIENT(47003, "积分商品库存不足"),
    POINT_GOODS_POINTS_MUST_ERROR(47005, "兑换积分必须大于0"),

    /**
     * 砍价活动
     */
    KANJIA_GOODS_ACTIVE_STOCK_ERROR(48001, "活动库存数量不能高于商品库存"),
    KANJIA_GOODS_ACTIVE_PRICE_ERROR(48002, "最低购买金额不能高于商品金额"),
    KANJIA_GOODS_ACTIVE_HIGHEST_PRICE_ERROR(48003, "最高砍价金额不能为0且不能超过商品金额"),
    KANJIA_GOODS_ACTIVE_LOWEST_PRICE_ERROR(48004, "最低砍价金额不能为0且不能超过商品金额"),
    KANJIA_GOODS_ACTIVE_HIGHEST_LOWEST_PRICE_ERROR(48005, "最低砍价金额不能高于最高砍价金额"),
    KANJIA_GOODS_ACTIVE_SETTLEMENT_PRICE_ERROR(48006, "结算金额不能高于商品金额"),
    KANJIA_GOODS_DELETE_ERROR(48007, "删除砍价商品异常"),
    KANJIA_ACTIVITY_NOT_FOUND_ERROR(48008, "砍价记录不存在"),
    KANJIA_ACTIVITY_LOG_MEMBER_ERROR(48009, "当前会员已经帮砍"),
    KANJIA_ACTIVITY_MEMBER_ERROR(48010, "当前会员已经发起此砍价商品活动"),
    KANJIA_ACTIVITY_NOT_PASS_ERROR(48011, "当前砍价未满足条件，不能进行购买"),
    KANJIA_NUM_BUY_ERROR(48012, "砍价商品购买数量不正确"),
    KANJIA_GOODS_UPDATE_ERROR(48013, "更新砍价商品异常"),

    /**
     * 抽奖活动
     */
    RAFFLE_NOT_EXIST(49001,"抽奖活动不存在"),
    RAFFLE_DAY_LIMIT_ERROR(49002,"超过活动每日抽奖限制"),
    RAFFLE_ACTIVE_LIMIT_ERROR(49003,"超过活动抽奖限制"),
    RAFFLE_DAY_ACTIVE_LIMIT_ERROR(49004,"活动周期限制不能小于每日限制"),

    /**
     * 店铺
     */

    STORE_NOT_EXIST(50001, "此店铺不存在"),
    STORE_NAME_EXIST_ERROR(50002, "店铺名称已存在!"),
    STORE_APPLY_DOUBLE_ERROR(50003, "已有店铺，无需重复申请!"),
    STORE_NOT_OPEN(50004, "该会员未开通店铺"),
    STORE_NOT_LOGIN_ERROR(50005, "未登录店铺"),
    STORE_CLOSE_ERROR(50006, "店铺未开放，请联系管理员"),
    GOODS_UPPER_STORE_DISABLE_ERROR(50008, "店铺已关闭，无法上架商品"),
    STORE_KEEPER_SUPER_ERROR(50009,"不能关闭店长的超级管理员"),
    FREIGHT_TEMPLATE_NOT_EXIST(50010, "当前模版不存在"),


    FREIGHT_TEMPLATE_NOT_HAVE(50011, "当前店铺没有运费模版"),

    FREIGHT_TEMPLATE_NAME_EXIST(50012, "当前模版名称已存在"),

    FREIGHT_TEMPLATE_BIND_NOT_EXIST(50013,"物流模板下存在商品，请修改后删除"),

    STORE_MOBILE_EXIST_ERROR(50014,"此手机号已注册店铺"),

    /**
     * 商家标签相关错误码
     */
    STORE_TAG_NOT_EXIST(50015, "商家标签不存在"),
    STORE_TAG_NAME_EXIST(50016, "标签名称已存在"),


    SELF_PICKUP_ADDRESS_NOT_HAVE(50015, "当前店铺未设置自提地址，无法开启自提功能"),
    RECEIVE_ADDRESS_NOT_HAVE(50015, "店铺未设置默认收货地址，无法提供退货功能，请前往地址管理，添加收货地址并设置为默认"),

    SELF_OPERATED_STORE_ERROR(50016, "只有直营店铺有才可操作"),
    /**
     * 结算单
     */
    BILL_CHECK_ERROR(51001, "只有已出账结算单可以核对"),
    BILL_COMPLETE_ERROR(51002, "只有已审核结算单可以支付"),

    /**
     * 文章
     */
    ARTICLE_CATEGORY_NAME_EXIST(60001, "文章分类名称已存在"),
    ARTICLE_CATEGORY_PARENT_NOT_EXIST(60002, "文章分类父分类不存在"),
    ARTICLE_CATEGORY_BEYOND_TWO(60003,
            "最多为二级分类,操作失败"),
    ARTICLE_CATEGORY_DELETE_ERROR(60004, "该文章分类下存在子分类，不能删除"),
    ARTICLE_CATEGORY_HAS_ARTICLE(60005, "该文章分类下存在文章，不能删除"),
    ARTICLE_CATEGORY_NO_DELETION(60007, "默认文章分类不能进行删除"),
    ARTICLE_NO_DELETION(60008, "默认文章不能进行删除"),

    /**
     * 会员推荐
     */
    TESTIMONIAL_COMMENT_NOT_EXIST(110001, "该评论不存在"),
    TESTIMONIAL_NOT_EXIST(110002, "该会员发布内容不存在"),
    TESTIMONIAL_COLLECT_INFO_ERROR(110003, "收藏信息异常"),

    /**
     * 页面
     */
    PAGE_NOT_EXIST(61001, "页面不存在"),
    PAGE_OPEN_DELETE_ERROR(61002, "当前页面处于启用状态，无法直接删除"),
    PAGE_DELETE_ERROR(61003, "当前页面为唯一页面，无法删除"),
    PAGE_RELEASE_ERROR(61004, "页面已发布，无需重复提交"),
    PAGE_OPEN_CLOSE_ERROR(61005, "无法关闭此页面"),

    /**
     * 话题
     */
    TOPIC_NOT_EXIST(62001, "该话题不存在"),
    TOPIC_NAME_EXIST(62002, "该话题名称已存在"),
    TOPIC_NAME_EMPTY(62003, "话题名称不能为空"),
    TOPIC_CATEGORY_NAME_EXIST(62004, "话题分类名称已存在"),
    TOPIC_CATEGORY_PARENT_NOT_EXIST(62005, "话题分类父分类不存在"),
    TOPIC_CATEGORY_BEYOND_TWO(62006, "最多为二级分类,操作失败"),
    TOPIC_CATEGORY_DELETE_ERROR(62007, "该话题分类下存在子分类，不能删除"),
    TOPIC_CATEGORY_HAS_TOPIC(62008, "该话题分类下存在话题，不能删除"),
    TOPIC_CATEGORY_NO_DELETION(62009, "默认话题分类不能进行删除"),
    TOPIC_CATEGORY_ERROR(62010, "话题分类数据异常"),

    /**
     * 设置
     */
    SETTING_NOT_TO_SET(70001, "该参数不需要设置"),
    ALIPAY_NOT_SETTING(70002, "支付宝支付未配置"),
    ALIPAY_EXCEPTION(70003, "支付宝支付错误，请稍后重试"),
    ALIPAY_PARAMS_EXCEPTION(70004, "支付宝参数异常"),
    LOGISTICS_NOT_SETTING(70005, "您还未配置快递查询"),
    ORDER_SETTING_ERROR(70006, "系统订单配置异常"),
    ALI_SMS_SETTING_ERROR(70007, "您还未配置阿里云短信"),
    SMS_SIGN_EXIST_ERROR(70008, "短信签名已存在"),
    LOGISTICS_CHECK_SETTING(70009, "操作失败,请检查您的快递鸟配置"),
    STORE_HOT_WORDS_NOT_EXIST(700010, "店铺热门关键词配置项不存在"),
    DISTRIBUTION_SETTING_ERROR(700011, "系统分销设置异常"),

    /**
     * 站内信
     */
    NOTICE_NOT_EXIST(80001, "当前消息模板不存在"),
    NOTICE_ERROR(80002, "修改站内信异常，请稍后重试"),
    NOTICE_SEND_ERROR(80003, "发送站内信异常，请检查系统日志"),

    /**
     * OSS
     */
    OSS_NOT_EXIST(80101, "OSS未配置"),
    OSS_EXCEPTION_ERROR(80102, "文件上传失败，请稍后重试"),
    OSS_DELETE_ERROR(80103, "图片删除失败"),
    DIRECTORY_NOT_EMPTY(80104, "文件夹不为空，无法删除，请先删除文件夹中的文件再进行操作"),

    /**
     * 验证码
     */
    VERIFICATION_ERROR(80202, "验证失败"),
    CONFIRM_PASSWORD_VERIFICATION_ERROR(80203, "确认密码验证失败"),
    VERIFICATION_CODE_INVALID(80204, "验证码已失效，请重新校验"),
    VERIFICATION_SMS_CHECKED_ERROR(80210, "短信验证码错误，请重新校验"),

    /**
     * 微信相关异常
     */
    WECHAT_CONNECT_NOT_SETTING(80300, "微信联合登陆信息未配置"),
    WECHAT_PAYMENT_NOT_SETTING(80301, "微信支付信息未配置"),
    WECHAT_QRCODE_ERROR(80302, "微信二维码生成异常"),
    WECHAT_MP_MESSAGE_ERROR(80303, "微信小程序小消息订阅异常"),
    WECHAT_JSAPI_SIGN_ERROR(80304, "微信JsApi签名异常"),
    WECHAT_CERT_ERROR(80305, "证书获取失败"),
    WECHAT_MP_MESSAGE_TMPL_ERROR(80306, "未能获取到微信模版消息id"),
    WECHAT_ERROR(80307, "微信接口异常"),
    WXPAY_APPLY_ERROR(80308, "微信支付申请异常"),
    WECHAT_RESULT_ERROR(80309, "微信接口返回异常"),
    WECHAT_GET_PROVINCE_ERROR(80310, "获取省份信息异常"),
    WECHAT_GET_PERSONAL_BANK_ERROR(80311, "获取个人银行卡信息异常"),
    WECHAT_GET_CITY_ERROR(80312, "获取城市信息异常"),
    WECHAT_GET_BRANCH_ERROR(80313, "获取支行信息异常"),

    // 未选择APP类型
    APP_VERSION_PARAM_ERROR(80311, "添加APP版本参数异常"),
    APP_VERSION_TYPE_ERROR(80312, "请选择有效的APP类型"),
    APP_VERSION_EXIST(80313, "APP版本已存在"),
    /**
     * IM
     */
    IM_MESSAGE_ADD_ERROR(80400, "IM消息发送错误"),
    IM_MESSAGE_EDIT_ERROR(80401, "IM消息更新错误"),

    /**
     * 其他
     */
    CUSTOM_WORDS_EXIST_ERROR(90000, "当前自定义分词已存在！"),
    CUSTOM_WORDS_NOT_EXIST_ERROR(90001, "当前自定义分词不存在！"),
    CUSTOM_WORDS_SECRET_KEY_ERROR(90002,
            "秘钥验证失败！"),
    ELASTICSEARCH_INDEX_INIT_ERROR(90003, "索引初始化失败！"),
    PURCHASE_ORDER_DEADLINE_ERROR(90004, "供求单，已超过报名截止时间"),

    SENSITIVE_WORDS_EXIST_ERROR(90005, "当前敏感词已存在！"),
    IMPORT_GOODS_IMAGE_ERROR(90006, "导入商品图片失败！"),
    IMPORT_GOODS_NAME_ERROR(90007, "导入商品名称失败！"),
    GOODS_STOCK_IMPORT_ERROR(90008, "导入商品库存失败！"),

    GOODS_NAME_EXIST_ERROR(90008, "商品名称已存在！"),

    CATEGORY_NAME_EXIST_ERROR(90009, "分类名称已存在！"),

    DEPARTMENT_EXIST_ERROR(90010, "部门名称已存在！"),

    INDEX_BUILDING(90101, "索引正在生成"),
    EXPORT_DATA_ERROR(90102,"暂无导出数据"),

    //payment
    PAYMENT_ERROR(100001, "支付失败"),
    PAYMENT_NOT_EXIST(100002, "支付方式不存在"),
    PAYMENT_METHOD_NOT_EXIST(100003, "支付方式不存在"),
    PAYMENT_METHOD_NOT_SUPPORT(100004, "支付方式不支持"),
    PAYMENT_METHOD_NOT_SUPPORT_REFUND(100005, "支付方式不支持退款"),
    PAYMENT_METHOD_NOT_SUPPORT_CANCEL(100006, "支付方式不支持取消"),
    PAYMENT_METHOD_NOT_SUPPORT_QUERY(100007, "支付方式不支持查询"),
    PAYMENT_METHOD_NOT_SUPPORT_CLOSE(100008, "支付方式不支持关闭"),
    PAYMENT_METHOD_NOT_SUPPORT_REFUND_QUERY(100010, "支付方式不支持退款查询"),
    PAYMENT_METHOD_NOT_SUPPORT_REFUND_CLOSE(100011, "支付方式不支持退款关闭"),
    PAYMENT_METHOD_NOT_SUPPORT_REFUND_PAY(100012, "支付方式不支持退款支付"),

    BALANCE_NOT_ENOUGH(100013, "余额不足"),
    PAYMENT_AMOUNT_ERROR(100014, "支付金额错误"),
    PAYMENT_PASSWORD_ERROR(1000015,"支付密码错误"),

    //退款异常，请手动操作
    REFUND_EXCEPTION(100101, "退款异常，请手动操作"),
    REFUND_DUPLICATE(100102, "重复退款"),

    PROFIT_SHARING_ERROR(100400, "分账异常"),

    POINTS_UPDATE_ERROR(100500, "积分更新异常"),
    PROMOTION_SCOPE_TYPE_ERROR(100500, "活动范围类型错误"),

    GRADE_NOT_EXIST(100600, "等级不存在"),
    PREMIUM_SETTING_NOT_OPEN(100601, "高级会员设置未开启"),
    USER_GRADE_USED(100602, "会员等级已被使用, 无法删除"),
    USER_GRADE_IS_DEFAULT(100603, "会员等级为默认会员等级"),
    USER_GRADE_NAME_EXIST(100604, "会员等级名称已存在"),
    USER_GRADE_EXPERIENCE_EXIST(100606, "已存在相同经验值的会员等级"),
    UNBOUND_MOBILE(100605, "未绑定手机号，请先前往会员中心绑定手机号"),
    
    /**
     * 服务商
     */
    PROVIDER_NOT_EXIST(110101, "服务商不存在"),
    PROVIDER_ID_NOT_EXIST(110102, "服务商ID不存在"),
    PROVIDER_BANNER_NOT_EXIST(110103, "服务商轮播图不存在"),

    SERVICE_FEE_EXIST_ERROR(100607, "当前服务费已存在！"),
    SERVICE_FEE_GET_ERROR(100608, "获取服务费配置失败！"),

    STORE_MARKET_EXIST_ERROR(100608, "当前商家市场已存在！"),

    EMPTY_ERROR(99999, ""),
    FAILURE(5000, "Failure"),
    NOT_IMPLEMENTED(5001, "Not Implemented"),
    PARAMETER_INCOMPLETE(5002, "Parameter incomplete"),
    UNSUPPORTED(5003, "Unsupported operation"),
    NO_AUTH_SOURCE(5004, "AuthDefaultSource cannot be null"),
    UNIDENTIFIED_PLATFORM(5005, "Unidentified platform"),
    ILLEGAL_REDIRECT_URI(5006, "Illegal redirect uri"),
    ILLEGAL_REQUEST(5007, "Illegal request"),
    ILLEGAL_CODE(5008, "Illegal code"),
    ILLEGAL_STATUS(5009, "Illegal state"),
    REQUIRED_REFRESH_TOKEN(5010, "The refresh token is required; it must not be null"),

    ILLEGAL_AES_KEY(5011, "SymmetricKey length illegal"),
    MEMBER_NOT_PREMIUM(25010, "Not a premium member"),
    COUPON_RECEIVER_LIMIT(25011, "The number of coupons received has reached the limit"),

    ;
    private final Integer code;
    private final String message;


    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 根据code获取去value
     *
     * @param code code
     * @return ResultCode
     */
    public static ResultCode getResultByCode(Integer code) {
        for (ResultCode resultCode : ResultCode.values()) {
            if (code.equals(resultCode.code())) {
                return resultCode;
            }
        }
        return null;
    }

    public Integer code() {
        return this.code;
    }

    public String message() {
        return this.message;
    }


    /**
     * 拼接自定义消息
     *
     * @param customMessage 自定义消息
     * @return 拼接后的消息
     */
    public String appendMessage(String customMessage) {
        return this.message + customMessage;
    }

}
