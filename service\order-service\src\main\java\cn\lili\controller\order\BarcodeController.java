package cn.lili.controller.order;

import cn.lili.common.utils.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.order.order.utils.BarcodeGenerator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 条形码生成API
 *
 * <AUTHOR>
 * @since 2025/01/30
 */
@RestController
@RequestMapping("/order/barcode")
@Tag(name = "条形码生成API")
@RequiredArgsConstructor
public class BarcodeController {

    @Operation(summary = "生成Code128条形码")
    @GetMapping("/code128")
    public ResultMessage<Map<String, String>> generateCode128Barcode(
            @Parameter(description = "条形码内容") @RequestParam String content,
            @Parameter(description = "图片宽度") @RequestParam(defaultValue = "300") int width,
            @Parameter(description = "图片高度") @RequestParam(defaultValue = "100") int height) {
        
        String barcodeImage = BarcodeGenerator.generateCode128Barcode(content, width, height);
        
        Map<String, String> result = new HashMap<>();
        result.put("content", content);
        result.put("barcodeImage", barcodeImage);
        result.put("width", String.valueOf(width));
        result.put("height", String.valueOf(height));
        
        return ResultUtil.data(result);
    }

    @Operation(summary = "生成二维码")
    @GetMapping("/qrcode")
    public ResultMessage<Map<String, String>> generateQRCode(
            @Parameter(description = "二维码内容") @RequestParam String content,
            @Parameter(description = "图片宽度") @RequestParam(defaultValue = "200") int width,
            @Parameter(description = "图片高度") @RequestParam(defaultValue = "200") int height) {
        
        String qrCodeImage = BarcodeGenerator.generateQRCode(content, width, height);
        
        Map<String, String> result = new HashMap<>();
        result.put("content", content);
        result.put("qrCodeImage", qrCodeImage);
        result.put("width", String.valueOf(width));
        result.put("height", String.valueOf(height));
        
        return ResultUtil.data(result);
    }

    @Operation(summary = "生成订单条形码")
    @GetMapping("/order/{orderSn}")
    public ResultMessage<Map<String, String>> generateOrderBarcode(@PathVariable String orderSn) {
        try {
            String barcodeImage = BarcodeGenerator.generateTicketBarcode(orderSn);
            Map<String, String> result = new HashMap<>();
            result.put("barcodeImage", barcodeImage);
            result.put("orderSn", orderSn);
            return ResultUtil.data(result);
        } catch (Exception e) {
            // log.error("生成订单条形码失败: {}", e.getMessage(), e);
            return ResultUtil.error();
        }
    }
} 