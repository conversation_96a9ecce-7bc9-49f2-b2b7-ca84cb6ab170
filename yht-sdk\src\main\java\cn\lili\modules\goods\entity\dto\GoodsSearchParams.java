package cn.lili.modules.goods.entity.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.utils.DateUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.goods.entity.enums.GoodsAuthEnum;
import cn.lili.modules.goods.entity.enums.GoodsMarketEnum;
import cn.lili.modules.goods.entity.enums.SalesModeEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 商品查询条件
 *
 * <AUTHOR>
 * @since 2020-02-24 19:27:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GoodsSearchParams extends PageVO {

    @Serial
    private static final long serialVersionUID = 2544015852728566887L;

    @Schema(title = "关键字")
    private String keywords;

    @Schema(title = "商品编号")
    private String goodsId;

    @Schema(title = "商品编号")
    private String templateId;

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "商品名称")
    private String eqGoodsName;

    @Schema(title = "商品编号")
    private String id;

    @Schema(title = "不等于商品编号")
    private String neId;

    @Schema(title = "商品编号")
    private List<String> ids;

    @Schema(title = "商品编号")
    private List<String> goodsIds;

    @Schema(title = "商家ID")
    private String storeId;

    @Schema(title = "卖家名字")
    private String storeName;

    @Schema(title = "价格,可以为范围，如10_1000")
    private String price;

    @Schema(title = "销量,可以为范围，如10_1000")
    private String buyCount;

    @Schema(title = "分类path")
    private String categoryPath;

    @Schema(title = "店铺分类id")
    private String storeCategoryPath;

    @Schema(title = "是否自营")
    private Boolean selfOperated;

    /**
     * @see GoodsMarketEnum
     */
    @Schema(title = "上下架状态")
    private String marketEnable;

    /**
     * @see GoodsAuthEnum
     */
    @Schema(title = "审核状态")
    private String authFlag;

    @Schema(title = "库存数量")
    private Integer leQuantity;

    @Schema(title = "库存数量")
    private Integer geQuantity;

    @Schema(title = "是否为推荐商品")
    private Boolean recommend;

    /**
     * @see cn.lili.modules.goods.entity.enums.GoodsTypeEnum
     */
    @Schema(title = "商品类型")
    private String goodsType;

    @Schema(title = "支持代发")
    private Boolean supportProxy;

    @Schema(title = "支持采购")
    private Boolean supportPurchase;

    @Schema(title = "代理商品")
    private Boolean isProxyGoods;

    @Schema(title = "场景")
    private String scene;

    /**
     * @see SalesModeEnum
     */
    @Schema(title = "销售模式")
    private String salesModel;

    @Schema(title = "删除标识")
    private Boolean deleteFlag;

    @Schema(title = "供应商ID")
    private String supplierId;

    @Schema(title = "等于店铺和供应商ID")
    private String storeAndSupplierId;

    @Schema(title = "是否商品会员")
    private Boolean isMemberGoods;

    @Schema(title = "商品状态（结合上架和审核状态）")
    private String goodsStatus;

    @Schema(title = "下架时间开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date offShelfTimeStart;

    @Schema(title = "下架时间结束")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date offShelfTimeEnd;

    @Schema(title = "排序类型：offShelfTime-下架时间，price-价格，comprehensive-综合排序")
    private String sortType;

    @Schema(title = "排序方向：asc-升序，desc-降序")
    private String sortDirection;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "开始时间")
    private Date startDate;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "结束时间")
    private Date endDate;

    @Schema(title = "sn")
    private String sn;

    @Schema(title = "预警库存")
    private Boolean alertQuantity;

    public <T> QueryWrapper<T> queryWrapper() {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        if (CharSequenceUtil.isNotEmpty(id)) {
            List<String> idList = Arrays.asList(id.split(","));
            if (idList.size() > 1) {
                queryWrapper.in("id", idList);
            } else {
                queryWrapper.like("id", id);
            }
        }
        if (CharSequenceUtil.isNotBlank(keywords)) {
            queryWrapper.and(i -> i.like("goods_name", keywords).or().like("id", keywords));
        }
        queryWrapper.ne(CharSequenceUtil.isNotEmpty(neId), "id", neId);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(goodsId), "goods_id", goodsId);
        queryWrapper.like(CharSequenceUtil.isNotEmpty(goodsName), "goods_name", goodsName);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(eqGoodsName), "goods_name", eqGoodsName);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(storeId), "store_id", storeId);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(supplierId), "supplier_id", supplierId);
        queryWrapper.nested(CharSequenceUtil.isNotEmpty(storeAndSupplierId), i -> i.eq("store_id", storeAndSupplierId).or().eq("supplier_id", storeAndSupplierId));
        queryWrapper.like(CharSequenceUtil.isNotEmpty(storeName), "store_name", storeName);
        queryWrapper.like(CharSequenceUtil.isNotEmpty(categoryPath), "category_path", categoryPath);
        queryWrapper.like(CharSequenceUtil.isNotEmpty(storeCategoryPath), "store_category_path", storeCategoryPath);
        queryWrapper.eq(selfOperated != null, "self_operated", selfOperated);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(marketEnable), "market_enable", marketEnable);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(authFlag), "auth_flag", authFlag);
        queryWrapper.le(leQuantity != null, "quantity", leQuantity);
        queryWrapper.ge(geQuantity != null, "quantity", geQuantity);
        queryWrapper.le(recommend != null, "recommend", recommend);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(goodsType), "goods_type", goodsType);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(templateId), "template_id",templateId);
        queryWrapper.in(CollUtil.isNotEmpty(ids), "id", ids);
        queryWrapper.in(CollUtil.isNotEmpty(goodsIds), "goods_id", goodsIds);

        //根据商品状态筛选内容
        if (CharSequenceUtil.isNotEmpty(goodsStatus)) {
            if ("ALL".equals(goodsStatus)) {
            } else if (goodsStatus.equals(GoodsMarketEnum.UPPER.name()) || goodsStatus.equals(GoodsMarketEnum.DOWN.name())) {
                queryWrapper.eq("market_enable", goodsStatus);
            } else {
                queryWrapper.eq("auth_flag", goodsStatus);
            }
        } else {
            if (CharSequenceUtil.isNotEmpty(marketEnable)) {
                queryWrapper.eq("market_enable", marketEnable);
            }
            if (CharSequenceUtil.isNotEmpty(authFlag)) {
                queryWrapper.eq("auth_flag", authFlag);
            }
        }

        //按时间查询
        queryWrapper.ge(startDate != null, "create_time", startDate);

        queryWrapper.le(endDate != null, "create_time", DateUtil.endOfDate(endDate));

        queryWrapper.eq(isProxyGoods != null, "is_proxy_goods", isProxyGoods);

        queryWrapper.in(supportProxy != null, "support_proxy", supportProxy);
        queryWrapper.in(supportPurchase != null, "support_purchase", supportPurchase);

        queryWrapper.eq(CharSequenceUtil.isNotEmpty(scene), "scene", scene);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(salesModel), "sales_model", salesModel);
        queryWrapper.eq(isMemberGoods != null, "is_member_goods", isMemberGoods);

        if(alertQuantity != null && alertQuantity){
            queryWrapper.apply("quantity <= alert_quantity");
            queryWrapper.ge("alert_quantity", 0);
        }

        queryWrapper.eq(CharSequenceUtil.isNotBlank(sn), "sn", sn);

        queryWrapper.eq("delete_flag", deleteFlag != null && deleteFlag);
        this.betweenWrapper(queryWrapper);
        this.dateRangeWrapper(queryWrapper);
        this.orderByWrapper(queryWrapper);
        return queryWrapper;
    }

    private <T> void betweenWrapper(QueryWrapper<T> queryWrapper) {
        if (CharSequenceUtil.isNotEmpty(price)) {
            String[] s = price.split("_");
            if (s.length > 1) {
                queryWrapper.between("price", s[0], s[1]);
            } else {
                queryWrapper.ge("price", s[0]);
            }
        }
        if (CharSequenceUtil.isNotEmpty(buyCount)) {
            String[] s = buyCount.split("_");
            if (s.length > 1) {
                queryWrapper.between("buy_count", s[0], s[1]);
            } else {
                queryWrapper.ge("buy_count", s[0]);
            }
        }
    }

    /**
     * 日期范围查询条件
     */
    private <T> void dateRangeWrapper(QueryWrapper<T> queryWrapper) {
        if (offShelfTimeStart != null) {
            queryWrapper.ge("off_shelf_time", offShelfTimeStart);
        }
        if (offShelfTimeEnd != null) {
            queryWrapper.le("off_shelf_time", offShelfTimeEnd);
        }
    }

    /**
     * 排序条件
     */
    private <T> void orderByWrapper(QueryWrapper<T> queryWrapper) {
        boolean isAsc = "asc".equalsIgnoreCase(sortDirection);

        if (CharSequenceUtil.isNotEmpty(sortType)) {
            switch (sortType) {
                case "offShelfTime":
                    queryWrapper.orderBy(true, isAsc, "off_shelf_time");
                    break;
                case "price":
                    queryWrapper.orderBy(true, isAsc, "price");
                    break;
                case "comprehensive":
                    // 综合排序：按评分、销量、更新时间排序
                    queryWrapper.orderBy(true, false, "grade");
                    queryWrapper.orderBy(true, false, "buy_count");
                    queryWrapper.orderBy(true, false, "update_time");
                    break;
                default:
                    queryWrapper.orderBy(true, false, "update_time");
                    break;
            }
        } else {
            queryWrapper.orderBy(true, false, "update_time");
        }
    }

    public <T> QueryWrapper<T> queryWrapperGoodsSkuByCustomSql() {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        if (CharSequenceUtil.isNotEmpty(id)) {
            queryWrapper.in("gs.id", Arrays.asList(id.split(",")));
        }
        if (CharSequenceUtil.isNotEmpty(keywords)) {
            queryWrapper.and(i -> i.like("gs.goods_name", keywords).or().like("gs.goods_id", keywords).or().like("gs.sn", keywords));
        }
        //根据商品状态筛选内容
        if (CharSequenceUtil.isNotEmpty(goodsStatus)) {
            if ("ALL".equals(goodsStatus)) {
            } else if (goodsStatus.equals(GoodsMarketEnum.UPPER.name()) || goodsStatus.equals(GoodsMarketEnum.DOWN.name())) {
                queryWrapper.eq("g.market_enable", goodsStatus);
            } else {
                queryWrapper.eq("g.auth_flag", goodsStatus);
            }
        } else {
            if (CharSequenceUtil.isNotEmpty(marketEnable)) {
                queryWrapper.eq("g.market_enable", marketEnable);
            }
            if (CharSequenceUtil.isNotEmpty(authFlag)) {
                queryWrapper.eq("g.auth_flag", authFlag);
            }
        }
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(goodsId), "gs.goods_id", goodsId);
        queryWrapper.like(CharSequenceUtil.isNotEmpty(goodsName), "gs.goods_name", goodsName);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(storeId), "gs.store_id", storeId);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(supplierId), "gs.supplier_id", supplierId);
        queryWrapper.nested(CharSequenceUtil.isNotEmpty(storeAndSupplierId), i -> i.eq("gs.store_id", storeAndSupplierId).or().eq("gs.supplier_id", storeAndSupplierId));
        queryWrapper.like(CharSequenceUtil.isNotEmpty(storeName), "gs.store_name", storeName);
        queryWrapper.like(CharSequenceUtil.isNotEmpty(categoryPath), "gs.category_path", categoryPath);
        queryWrapper.like(CharSequenceUtil.isNotEmpty(storeCategoryPath), "gs.store_category_path", storeCategoryPath);
        queryWrapper.eq(selfOperated != null, "gs.self_operated", selfOperated);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(marketEnable), "gs.market_enable", marketEnable);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(authFlag), "gs.auth_flag", authFlag);
        queryWrapper.le(leQuantity != null, "gs.quantity", leQuantity);
        queryWrapper.ge(geQuantity != null, "gs.quantity", geQuantity);
        queryWrapper.le(recommend != null, "gs.recommend", recommend);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(goodsType), "gs.goods_type", goodsType);
        queryWrapper.in(CollUtil.isNotEmpty(ids), "gs.id", ids);
        queryWrapper.in(CollUtil.isNotEmpty(goodsIds), "gs.goods_id", goodsIds);

        queryWrapper.eq(isMemberGoods != null, "gs.is_member_goods", isMemberGoods);
        queryWrapper.eq(isProxyGoods != null, "gs.is_proxy_goods", isProxyGoods);

        queryWrapper.in(supportProxy != null, "gs.support_proxy", supportProxy);
        queryWrapper.in(supportPurchase != null, "gs.support_purchase", supportPurchase);

        queryWrapper.eq(CharSequenceUtil.isNotEmpty(scene), "gs.scene", scene);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(salesModel), "gs.sales_model", salesModel);

        // 根据创建时间查询
        if (startDate != null && endDate != null) {
            queryWrapper.between("gs.create_time", startDate, endDate);
        } else if (startDate != null) {
            queryWrapper.ge("gs.create_time", startDate);
        } else if (endDate != null) {
            queryWrapper.le("gs.create_time", endDate);
        }

        if(alertQuantity != null && alertQuantity){
            queryWrapper.apply("gs.quantity <= gs.alert_quantity");
            queryWrapper.ge("gs.alert_quantity", 0);
        }

        queryWrapper.eq(CharSequenceUtil.isNotBlank(sn), "gs.sn", sn);

        queryWrapper.eq("gs.delete_flag", false);
        return queryWrapper;
    }

    public <T> QueryWrapper<T> exportWrapperGoodsSkuByCustomSql() {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        if (CharSequenceUtil.isNotEmpty(id)) {
            queryWrapper.in("gs.id", Arrays.asList(id.split(",")));
        }
        if (CharSequenceUtil.isNotEmpty(keywords)) {
            queryWrapper.and(i -> i.like("gs.goods_name", keywords).or().like("gs.goods_id", keywords).or().like("gs.sn", keywords));
        }
        //根据商品状态筛选内容
        if (CharSequenceUtil.isNotEmpty(goodsStatus)) {
            if("ALL".equals(goodsStatus)){
            }else if(goodsStatus.equals(GoodsMarketEnum.UPPER.name())||goodsStatus.equals(GoodsMarketEnum.DOWN.name())){
                queryWrapper.eq("gs.market_enable", goodsStatus);
            }else {
                queryWrapper.eq("gs.auth_flag", goodsStatus);
            }
        }else{
            if (CharSequenceUtil.isNotEmpty(marketEnable)) {
                queryWrapper.eq("gs.market_enable", marketEnable);
            }
            if (CharSequenceUtil.isNotEmpty(authFlag)) {
                queryWrapper.eq("gs.auth_flag", authFlag);
            }
        }
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(goodsId), "gs.goods_id", goodsId);
        queryWrapper.like(CharSequenceUtil.isNotEmpty(goodsName), "gs.goods_name", goodsName);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(storeId), "gs.store_id", storeId);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(supplierId), "gs.supplier_id", supplierId);
        queryWrapper.nested(CharSequenceUtil.isNotEmpty(storeAndSupplierId), i -> i.eq("gs.store_id", storeAndSupplierId).or().eq("gs.supplier_id", storeAndSupplierId));
        queryWrapper.like(CharSequenceUtil.isNotEmpty(storeName), "gs.store_name", storeName);
        queryWrapper.like(CharSequenceUtil.isNotEmpty(categoryPath), "gs.category_path", categoryPath);
        queryWrapper.like(CharSequenceUtil.isNotEmpty(storeCategoryPath), "gs.store_category_path", storeCategoryPath);
        queryWrapper.eq(selfOperated != null, "gs.self_operated", selfOperated);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(marketEnable), "gs.market_enable", marketEnable);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(authFlag), "gs.auth_flag", authFlag);
        queryWrapper.le(leQuantity != null, "gs.quantity", leQuantity);
        queryWrapper.ge(geQuantity != null, "gs.quantity", geQuantity);
        queryWrapper.le(recommend != null, "gs.recommend", recommend);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(goodsType), "gs.goods_type", goodsType);
        queryWrapper.in(CollUtil.isNotEmpty(ids), "gs.id", ids);
        queryWrapper.in(CollUtil.isNotEmpty(goodsIds), "gs.goods_id", goodsIds);

        queryWrapper.eq(isMemberGoods != null, "gs.is_member_goods", isMemberGoods);
        queryWrapper.eq(isProxyGoods != null, "gs.is_proxy_goods", isProxyGoods);

        queryWrapper.in(supportProxy != null, "gs.support_proxy", supportProxy);
        queryWrapper.in(supportPurchase != null, "gs.support_purchase", supportPurchase);

        queryWrapper.eq(CharSequenceUtil.isNotEmpty(scene), "gs.scene", scene);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(salesModel), "gs.sales_model", salesModel);

        queryWrapper.eq("gs.delete_flag", false);
        return queryWrapper;
    }


}
