package cn.lili.modules.order.cart.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.lili.cache.Cache;
import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.SceneEnums;
import cn.lili.common.utils.CurrencyUtil;
import cn.lili.common.utils.StringUtils;
import cn.lili.modules.goods.client.GoodsClient;
import cn.lili.modules.goods.client.GoodsIndexClient;
import cn.lili.modules.goods.entity.dos.GoodsSku;
import cn.lili.modules.goods.entity.dto.BatchUpdateGoodsDTO;
import cn.lili.modules.goods.entity.enums.GoodsAuthEnum;
import cn.lili.modules.goods.entity.enums.GoodsMarketEnum;
import cn.lili.modules.goods.entity.enums.SalesModeEnum;
import cn.lili.modules.member.client.UserAddressClient;
import cn.lili.modules.member.entity.dos.UserAddress;
import cn.lili.modules.member.entity.dto.AddressSearchParams;
import cn.lili.modules.member.entity.enums.AddressTypeEnum;
import cn.lili.modules.order.cart.entity.CheckedParams;
import cn.lili.modules.order.cart.entity.dto.MemberCouponDTO;
import cn.lili.modules.order.cart.entity.dto.TradeCouponDTO;
import cn.lili.modules.order.cart.entity.dto.TradeDTO;
import cn.lili.modules.order.cart.entity.enums.CartSceneEnum;
import cn.lili.modules.order.cart.entity.enums.DeliveryMethodEnum;
import cn.lili.modules.order.cart.entity.vo.CartSkuVO;
import cn.lili.modules.order.cart.entity.vo.CartVO;
import cn.lili.modules.order.cart.render.TradeBuilder;
import cn.lili.modules.order.cart.render.impl.CheckDataRender;
import cn.lili.modules.order.order.client.OrderClient;
import cn.lili.modules.order.order.client.OrderItemClient;
import cn.lili.modules.order.order.entity.dto.CartParamsDTO;
import cn.lili.modules.order.order.entity.dto.FreightDTO;
import cn.lili.modules.order.order.entity.dto.ServiceFeeDTO;
import cn.lili.modules.order.order.entity.vo.ReceiptVO;
import cn.lili.modules.payment.client.WalletPointClient;
import cn.lili.modules.promotion.client.KanjiaActivityClient;
import cn.lili.modules.promotion.client.MemberCouponClient;
import cn.lili.modules.promotion.client.PromotionGoodsClient;
import cn.lili.modules.promotion.client.PromotionsClient;
import cn.lili.modules.promotion.entity.dos.Coupon;
import cn.lili.modules.promotion.entity.dos.KanjiaActivity;
import cn.lili.modules.promotion.entity.dos.MemberCoupon;
import cn.lili.modules.promotion.entity.dos.PromotionGoods;
import cn.lili.modules.promotion.entity.dto.search.KanjiaActivitySearchParams;
import cn.lili.modules.promotion.entity.dto.search.MemberCouponSearchParams;
import cn.lili.modules.promotion.entity.dto.search.PromotionGoodsSearchParams;
import cn.lili.modules.promotion.entity.enums.KanJiaStatusEnum;
import cn.lili.modules.promotion.entity.enums.MemberCouponStatusEnum;
import cn.lili.modules.promotion.entity.enums.PromotionsStatusEnum;
import cn.lili.modules.promotion.entity.vos.CouponVO;
import cn.lili.modules.promotion.entity.vos.PointsGoodsVO;
import cn.lili.modules.search.entity.dos.EsGoodsIndex;
import cn.lili.modules.store.client.FreightTemplateClient;
import cn.lili.modules.store.client.StoreClient;
import cn.lili.modules.store.entity.dos.FreightTemplateChild;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.dto.FreightTemplateChildDTO;
import cn.lili.modules.store.entity.enums.FreightTemplateEnum;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import cn.lili.modules.store.entity.vos.StoreVO;

import java.util.*;
import java.util.stream.Collectors;

import cn.lili.modules.system.client.ServiceFeeClient;
import cn.lili.modules.system.entity.dos.ServiceFee;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 购物车业务层实现
 *
 * <AUTHOR>
 * @since 2020-03-23 12:29 下午
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CartHandler {

    static String errorMessage = "购物车异常，请稍后重试";

    private final TradeBuilder tradeBuilder;

    private final UserAddressClient userAddressClient;

    private final PromotionsClient promotionsClient;

    private final KanjiaActivityClient kanjiaActivityClient;

    private final PromotionGoodsClient promotionGoodsClient;

    private final GoodsClient goodsClient;

    private final StoreClient storeClient;


    private final GoodsIndexClient goodsIndexClient;

    private final WalletPointClient walletPointClient;

    private final Cache cache;

    private final ServiceFeeClient serviceFeeClient;

    private final MemberCouponClient memberCouponClient;

    private final FreightTemplateClient freightTemplateClient;


    /**
     * 购物车添加商品
     *
     * @param cartParamsDTO 参数
     */
    public void add(CartParamsDTO cartParamsDTO) {
        try {
            String skuId = cartParamsDTO.getSkuId();
            CartSceneEnum cartSceneEnum = cartParamsDTO.cartSceneEnum();

            AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
            //购买数量大于0
            if (cartParamsDTO.getNum() <= 0) {
                throw new ServiceException(ResultCode.CART_NUM_ERROR);
            }

            //获取商品详情
            GoodsSku dataSku = checkGoods(skuId);
            StoreVO storeVO = checkStore(dataSku.getStoreId());
            if (storeVO == null) {
                throw new ServiceException(ResultCode.STORE_NOT_EXIST);
            }

            //购物车方式购买需要保存之前的选择，其他方式购买，则直接抹除掉之前的记录
            TradeDTO tradeDTO;

            //立即购买则清空之前的立即购买栏
            if (cartParamsDTO.cartSceneEnum().equals(CartSceneEnum.BUY_NOW)) {
                tradeDTO = new TradeDTO(cartSceneEnum, currentUser);
            } else {
                //如果存在，则变更数量不做新增，否则新增一个商品进入集合
                tradeDTO = tradeBuilder.readDTO(cartSceneEnum);
            }



            //整笔交易规格商品
            List<CartSkuVO> cartSkuVOS = tradeDTO.getSkuList();
            //获取购物车中的商品
            CartSkuVO cartSkuVO = cartSkuVOS.stream().filter(i -> i.getGoodsSku().getId().equals(skuId)).findFirst().orElse(null);

            //ps 判定时间是因为商品再编辑sku时，会删除之前的sku，重新创建sku，保留skuid不变，所以这里判定创建时间
            //如果购物车中存在sku，且sku的创建时间等于最新获取数据中的sku，则修改数量
            if (cartSkuVO != null && dataSku.getCreateTime().equals(cartSkuVO.getGoodsSku().getCreateTime())) {
                //如果覆盖购物车中商品数量 则写入传入的库存，否则累加之前的库存
                cartSkuVO.setNum(Boolean.TRUE.equals(cartParamsDTO.getCover()) ? cartParamsDTO.getNum() :
                        cartSkuVO.getNum() + cartParamsDTO.getNum());
            }
            //购物车不存在商品
            else {
                // 删除购物车中的原商品
                cartSkuVOS.removeIf(i -> i.getGoodsSku().getId().equals(skuId));

                //购物车中不存在此商品，则新建立一个
                cartSkuVO = new CartSkuVO(dataSku);
                cartSkuVO.setNum(cartParamsDTO.getNum());

                cartSkuVOS.add(cartSkuVO);
            }

            try {
                if (SalesModeEnum.WHOLESALE.name().equals(dataSku.getSalesModel()) && CharSequenceUtil.isNotEmpty(dataSku.getWholesale())) {
                    Integer num = dataSku.getWholesaleList().getFirst().getNum();
                    if(cartSkuVO.getNum() < num) {
                        cartSkuVO.setNum(num);
                    }
                }
            } catch (Exception e) {
                log.error("批发商品获取批发价格异常", e);
            }

            cartSkuVO.setCartType(cartSceneEnum);
            //判定购物车商品库存
            this.checkSetGoodsQuantity(cartSkuVO);

            //检查促销信息
            this.checkGoodsPromotion(cartParamsDTO, cartSkuVO);

            // 检查促销活动是否存在
            if (cartSkuVO.getPromotionTypeEnum() != null) {
                tradeDTO.setPromotionType(cartSkuVO.getPromotionTypeEnum());
            }


            cartSkuVO.setSubTotal(CurrencyUtil.mul(cartSkuVO.getPurchasePrice(), cartSkuVO.getNum()));
            cartSkuVO.setSelfOperated(storeVO.getSelfOperated());
            //新加入的商品都是选中的
            cartSkuVO.setChecked(true);
            cartSkuVO.setIsServiceFee(!storeVO.getSelfOperated());

            // 购物车操作后，清除使用的优惠券
            tradeDTO.removeUsingCoupon();

            //购物车添加缓存
            tradeBuilder.resetTradeDTO(tradeDTO);
        } catch (ServiceException serviceException) {
            throw serviceException;
        } catch (Exception e) {
            log.error("购物车渲染异常", e);
            throw new ServiceException(errorMessage);
        }

    }


    /**
     * 更新全部的选中状态
     *
     * @param checkedParams 选中参数
     */
    public void updateChecked(CheckedParams checkedParams) {
        TradeDTO tradeDTO = tradeBuilder.readDTO(checkedParams.getCartSceneEnum());

        List<CartSkuVO> cartSkuVOS = new ArrayList<>();

        switch (checkedParams.getCartCheckedWay()) {
            case ALL:
                cartSkuVOS.addAll(tradeDTO.getSkuList());
                break;
            case STORE:
                //过滤出店铺的商品
                cartSkuVOS.addAll(
                        tradeDTO.getSkuList().stream().filter(i -> i.getStoreId().equals(checkedParams.getId())).toList());
                break;
            case GOODS:
                //过滤出商品的商品
                cartSkuVOS.addAll(
                        tradeDTO.getSkuList().stream().filter(i -> i.getGoodsSku().getGoodsId().equals(checkedParams.getId())).toList());
                break;
            case SKU:
                //过滤出指定SKU的商品
                cartSkuVOS.addAll(
                        tradeDTO.getSkuList().stream().filter(i -> i.getGoodsSku().getId().equals(checkedParams.getId())).toList());
                break;
            default:
                break;
        }

        //设置选中状态
        for (CartSkuVO cartSkuVO : cartSkuVOS) {
            cartSkuVO.setChecked(checkedParams.getChecked());
        }

        tradeBuilder.resetTradeDTO(tradeDTO);
    }


    /**
     * 批量删除
     *
     * @param skuIds 要写入的skuIds
     */
    public void delete(String way, String[] skuIds) {
        TradeDTO tradeDTO = tradeBuilder.readDTO(CartSceneEnum.getCartType(way));
        List<CartSkuVO> cartSkuVOS = tradeDTO.getSkuList();
        List<CartSkuVO> deleteVos = new ArrayList<>();
        for (CartSkuVO cartSkuVO : cartSkuVOS) {
            for (String skuId : skuIds) {
                if (cartSkuVO.getGoodsSku().getId().equals(skuId)) {
                    deleteVos.add(cartSkuVO);
                }
            }
        }
        cartSkuVOS.removeAll(deleteVos);

        // 购物车操作后，清除使用的优惠券
        tradeDTO.removeUsingCoupon();

        tradeBuilder.resetTradeDTO(tradeDTO);
    }

    /**
     * 获取可使用的优惠券数量
     *
     * @param way 购物车购买：CART/立即购买：BUY_NOW/拼团购买：PINTUAN / 积分购买：POINT
     * @return 可使用的优惠券数量
     */
    public Long getCanUseCoupon(String way) {
        TradeDTO tradeDTO = tradeBuilder.readDTO(CartSceneEnum.getCartType(way));
        long count = 0L;
        // 计算购物车总价
        double totalPrice = tradeDTO.getSkuList().stream().mapToDouble(i -> i.getPurchasePrice() * i.getNum()).sum();
        // 如果购物车中有商品
        if (!tradeDTO.getSkuList().isEmpty()) {
            // 获取购物车中的商品id
            List<String> ids =
                    tradeDTO.getSkuList().stream().filter(i -> Boolean.TRUE.equals(i.getChecked())).map(i -> i.getGoodsSku().getId()).toList();

            // 获取购物车中的商品信息
            List<EsGoodsIndex> esGoodsList = goodsIndexClient.getEsGoodsBySkuIds(ids);
            // 遍历购物车中的商品信息
            for (EsGoodsIndex esGoodsIndex : esGoodsList) {
                // 如果商品信息中有促销信息
                if (esGoodsIndex != null && esGoodsIndex.getPromotionMap() != null && !esGoodsIndex.getPromotionMap().isEmpty()) {
                    // 获取商品信息中的优惠券id
                    List<String> couponIds =
                            esGoodsIndex.getPromotionMap().keySet().stream().filter(i -> i.contains(PromotionTypeEnum.COUPON.name())).map(i -> i.substring(i.lastIndexOf("-") + 1)).toList();
                    // 如果商品信息中有优惠券id
                    if (!couponIds.isEmpty()) {
                        // 获取当前商品可用的优惠券
                        List<MemberCoupon> currentGoodsCanUse = promotionsClient.getCurrentGoodsCanUse(tradeDTO.getMemberId(), couponIds, totalPrice);
                        count = currentGoodsCanUse.size();
                    }
                }
            }
            // 获取购物车中的店铺id
            List<String> storeIds = new ArrayList<>();
            // 遍历购物车中的商品信息
            for (CartSkuVO cartSkuVO : tradeDTO.getSkuList()) {
                if (!storeIds.contains(cartSkuVO.getStoreId())) {
                    // 如果店铺id不为空
                    storeIds.add(cartSkuVO.getStoreId());
                }
            }

            //获取可操作的优惠券集合
            List<MemberCoupon> allScopeMemberCoupon = promotionsClient.getAllScopeMemberCoupon(tradeDTO.getMemberId(), storeIds);
            if (allScopeMemberCoupon != null && !allScopeMemberCoupon.isEmpty()) {
                //过滤满足消费门槛
                count += allScopeMemberCoupon.stream().filter(i -> i.getConsumeThreshold() <= totalPrice).count();
            }
        }
        return count;
    }

    /**
     * 选择收货地址
     *
     * @param shippingAddressId 收货地址id
     * @param way               购物车类型
     */
    public void shippingAddress(String way, String shippingAddressId) {
        try {
            //默认购物车
            CartSceneEnum cartSceneEnum = CartSceneEnum.getCartType(way);

            TradeDTO tradeDTO = tradeBuilder.readDTO(cartSceneEnum);
            AddressSearchParams addressSearchParams = new AddressSearchParams();
            addressSearchParams.setId(shippingAddressId);
            addressSearchParams.setScene(UserContext.getScene());
            UserAddress userAddress = userAddressClient.getByParams(addressSearchParams);
            tradeDTO.setUserAddress(userAddress);

            tradeDTO.setFreightDTOList(new ArrayList<>());
            tradeBuilder.buildFreightList(tradeDTO, userAddress);
            tradeBuilder.resetTradeDTO(tradeDTO);
        } catch (ServiceException se) {
            log.error(ResultCode.SHIPPING_NOT_APPLY.message(), se);
            throw new ServiceException(ResultCode.SHIPPING_NOT_APPLY);
        } catch (Exception e) {
            log.error(ResultCode.CART_ERROR.message(), e);
            throw new ServiceException(ResultCode.CART_ERROR);
        }
    }


    /**
     * 自提地址
     *
     * @param way            购物车类型
     * @param storeAddressId 自提地址id
     */
    public void shippingSelfAddress(String way, String storeAddressId) {
        try {
            //默认购物车
            CartSceneEnum cartSceneEnum = CartSceneEnum.getCartType(way);

            TradeDTO tradeDTO = tradeBuilder.readDTO(cartSceneEnum);

            AddressSearchParams addressSearchParams = new AddressSearchParams();
            addressSearchParams.setId(storeAddressId);
            addressSearchParams.setScene(SceneEnums.STORE);
            addressSearchParams.setType(AddressTypeEnum.SELF_PICKUP);
            addressSearchParams.setVerifyPermissions(false);

            UserAddress storeAddress = userAddressClient.getByParams(addressSearchParams);
            tradeDTO.setStoreAddress(storeAddress);
            tradeBuilder.resetTradeDTO(tradeDTO);
        } catch (ServiceException se) {
            log.error(ResultCode.SHIPPING_NOT_APPLY.message(), se);
            throw new ServiceException(ResultCode.SHIPPING_NOT_APPLY);
        } catch (Exception e) {
            log.error(ResultCode.CART_ERROR.message(), e);
            throw new ServiceException(ResultCode.CART_ERROR);
        }
    }

    /**
     * 选择发票
     *
     * @param way       购物车类型
     * @param receiptVO 发票信息
     */
    public void shippingReceipt(String way, ReceiptVO receiptVO) {
        CartSceneEnum cartSceneEnum = CartSceneEnum.getCartType(way);
        TradeDTO tradeDTO = tradeBuilder.readDTO(cartSceneEnum);
        tradeDTO.setNeedReceipt(true);
        tradeDTO.setReceiptVO(receiptVO);
        tradeBuilder.resetTradeDTO(tradeDTO);
    }

    /**
     * 选择配送方式
     *
     * @param storeId        店铺id
     * @param deliveryMethod 配送方式
     * @param way            购物车类型
     */
    public void shippingMethod(String storeId, String deliveryMethod, String way) {
        try {
            CartSceneEnum cartSceneEnum = CartSceneEnum.getCartType(way);
            TradeDTO tradeDTO = tradeBuilder.readDTO(cartSceneEnum);
            for (CartVO cartVO : tradeDTO.getCartList()) {
                if (cartVO.getStoreId().equals(storeId)) {
                    cartVO.setDeliveryMethodEnum(DeliveryMethodEnum.valueOf(deliveryMethod));
                }
            }
            tradeDTO.setUserAddress(null);
            tradeDTO.setStoreAddress(null);
            tradeDTO.setDeliveryMethodEnum(DeliveryMethodEnum.valueOf(deliveryMethod));
            tradeBuilder.resetTradeDTO(tradeDTO);
        } catch (ServiceException se) {
            log.error(se.getMsg(), se);
            throw se;
        } catch (Exception e) {
            log.error(ResultCode.CART_ERROR.message(), e);
            throw new ServiceException(ResultCode.CART_ERROR);
        }
    }

    /**
     * 获取配送方式列表
     *
     * @param way 购物车类型
     * @return 配送方式列表
     */
    public List<String> shippingMethodList(String way) {
        CartSceneEnum cartSceneEnum = CartSceneEnum.getCartType(way);

        List<String> list = new ArrayList<>();
        try {
            list.add(DeliveryMethodEnum.LOGISTICS.name());
            TradeDTO tradeDTO = tradeBuilder.buildChecked(cartSceneEnum);
            if (tradeDTO.getCartList().size() == 1) {
                for (CartVO cartVO : tradeDTO.getCartList()) {
                    Store store = storeClient.getStore(cartVO.getStoreId());
                    if (store.getEnablePickup() != null && store.getEnablePickup()) {
                        list.add(DeliveryMethodEnum.SELF_PICK_UP.name());
                    }
                }
            }

            //如果交易中有供应商物品，同样无法提供自提。供应商无法提供自提业务。
            if (tradeDTO.getCheckedSkuList().stream()
                    .anyMatch(j -> CharSequenceUtil.isNotEmpty(j.getGoodsSku().getSupplierSkuId()))) {
                list.remove(DeliveryMethodEnum.SELF_PICK_UP.name());
            }
        } catch (Exception e) {
            log.error("获取配送方式列表异常", e);
        }
        return list;
    }

    /**
     * 获取购物车商品数量
     *
     * @param cartTypeEnum 购物车类型
     * @return 购物车商品数量
     */
    public int getCartNum(String cartTypeEnum) {
        try {
            TradeDTO tradeDTO = tradeBuilder.readDTO(CartSceneEnum.getCartType(cartTypeEnum));
            return tradeDTO.getSkuList().size();
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 选择优惠券
     *
     * @param couponId 优惠券id
     * @param way      购物车类型
     * @param use      true使用 false 弃用
     */
    public void selectCoupon(String way, String couponId, boolean use) {
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        //获取购物车，然后重新写入优惠券
        CartSceneEnum cartSceneEnum = CartSceneEnum.getCartType(way);
        TradeDTO tradeDTO = tradeBuilder.readDTO(cartSceneEnum);

        //判断优惠券是否已被选择
        if (use && tradeDTO.getPlatformCoupon() != null) {
            if (tradeDTO.getPlatformCoupon().getMemberCoupon().getId().equals(couponId)) {
                throw new ServiceException(ResultCode.COUPON_SELECTED_EXIST);
            }
        }
        if (use && CollUtil.isNotEmpty(tradeDTO.getStoreCoupons())
            && tradeDTO.getStoreCoupons().stream().anyMatch(i -> i.getMemberCouponDTO().getMemberCoupon().getId().equals(couponId))) {
            throw new ServiceException(ResultCode.COUPON_SELECTED_EXIST);
        }

        MemberCouponSearchParams searchParams = new MemberCouponSearchParams();
        searchParams.setMemberCouponStatus(MemberCouponStatusEnum.NEW.name());
        searchParams.setMemberId(currentUser.getExtendId());
        searchParams.setId(couponId);
        MemberCoupon memberCoupon = promotionsClient.getMemberCoupon(searchParams);
        if (memberCoupon == null) {
            throw new ServiceException(ResultCode.COUPON_EXPIRED);
        }
        //使用优惠券 与否
        if (use) {
            this.useCoupon(tradeDTO, memberCoupon);
        } else {
            if (Boolean.TRUE.equals(memberCoupon.getPlatformFlag())) {
                tradeDTO.setPlatformCoupon(null);
            } else {
                tradeDTO.getStoreCoupons().removeIf(i -> i.getStoreId().equals(memberCoupon.getStoreId()));
            }
        }
        tradeBuilder.resetTradeDTO(tradeDTO);
    }


    /**
     * 使用优惠券判定
     *
     * @param tradeDTO     交易对象
     * @param memberCoupon 会员优惠券
     */
    private void useCoupon(TradeDTO tradeDTO, MemberCoupon memberCoupon) {

        if (Boolean.TRUE.equals(memberCoupon.getPlatformFlag())) {
            tradeDTO.setPlatformCoupon(new MemberCouponDTO(memberCoupon));
        } else {
            if (tradeDTO.getStoreCoupons() == null) {
                tradeDTO.setStoreCoupons(new ArrayList<>());
            }
            if (tradeDTO.getStoreCoupons().stream().noneMatch(i -> i.getMemberCouponDTO().getMemberCoupon().getId().equals(memberCoupon.getId()) || i.getStoreId().equals(memberCoupon.getStoreId()))) {
                tradeDTO.getStoreCoupons().add(new TradeCouponDTO(memberCoupon.getStoreId(), new MemberCouponDTO(memberCoupon)));
            } else {
                tradeDTO.getStoreCoupons().removeIf(i -> i.getStoreId().equals(memberCoupon.getStoreId()));
                tradeDTO.getStoreCoupons().add(new TradeCouponDTO(memberCoupon.getStoreId(), new MemberCouponDTO(memberCoupon)));
            }
        }
    }



    /**
     * 校验拼团信息
     *
     * @param cartSkuVO 购物车信息
     */
    private void checkPintuan(CartSkuVO cartSkuVO) {
        //拼团活动，需要对限购数量进行判定
        //获取拼团信息
        if (cartSkuVO.getPromotionMap() != null && !cartSkuVO.getPromotionMap().isEmpty()) {
            Optional<Map.Entry<String, Object>> pintuanPromotions =
                    cartSkuVO.getPromotionMap().entrySet().stream().filter(i -> i.getKey().contains(PromotionTypeEnum.PINTUAN.name())).findFirst();
            if (pintuanPromotions.isPresent()) {
                JSONObject promotionsObj = JSONUtil.parseObj(pintuanPromotions.get().getValue());
                //写入拼团信息
                cartSkuVO.setPromotionId(promotionsObj.get("id").toString());
                //检测拼团限购数量
                Integer limitNum = promotionsObj.get("limitNum", Integer.class);
                if (limitNum != 0 && cartSkuVO.getNum() > limitNum) {
                    throw new ServiceException(ResultCode.CART_PINTUAN_LIMIT_ERROR);
                }
            }
        }
    }

    /**
     * 校验砍价信息
     *
     * @param cartSkuVO 购物车信息
     */
    private void checkKanjia(CartSkuVO cartSkuVO) {
        if (cartSkuVO.getPromotionMap() != null && !cartSkuVO.getPromotionMap().isEmpty()) {
            Optional<Map.Entry<String, Object>> kanjiaPromotions =
                    cartSkuVO.getPromotionMap().entrySet().stream().filter(i -> i.getKey().contains(PromotionTypeEnum.KANJIA.name())).findFirst();
            if (kanjiaPromotions.isPresent()) {
                JSONObject promotionsObj = JSONUtil.parseObj(kanjiaPromotions.get().getValue());
                //查找当前会员的砍价商品活动
                KanjiaActivitySearchParams kanjiaActivitySearchParams = new KanjiaActivitySearchParams();
                kanjiaActivitySearchParams.setKanjiaActivityGoodsId(promotionsObj.get("id", String.class));
                kanjiaActivitySearchParams.setMemberId(UserContext.getCurrentExistUser().getExtendId());
                kanjiaActivitySearchParams.setStatus(KanJiaStatusEnum.SUCCESS.name());
                KanjiaActivity kanjiaActivity = kanjiaActivityClient.getKanjiaActivity(kanjiaActivitySearchParams);

                //校验砍价活动是否满足条件
                //判断发起砍价活动
                if (kanjiaActivity == null) {
                    throw new ServiceException(ResultCode.KANJIA_ACTIVITY_NOT_FOUND_ERROR);
                    //判断砍价活动是否已满足条件
                } else if (!KanJiaStatusEnum.SUCCESS.name().equals(kanjiaActivity.getStatus())) {
                    cartSkuVO.setPromotionId(kanjiaActivity.getId());
                    cartSkuVO.setPurchasePrice(0D);
                    throw new ServiceException(ResultCode.KANJIA_ACTIVITY_NOT_PASS_ERROR);
                }
                //砍价商品默认一件货物
                cartSkuVO.setPromotionId(kanjiaActivity.getId());
                cartSkuVO.setNum(1);
            }
        }
    }

    /**
     * 校验积分商品信息
     *
     * @param cartSkuVO 购物车信息
     */
    private void checkPoint(CartSkuVO cartSkuVO) {
        PointsGoodsVO pointsGoodsVO = promotionsClient.getPointsGoodsDetailBySkuId(cartSkuVO.getGoodsSku().getId());

        if (pointsGoodsVO != null) {
            if (walletPointClient.getUserPoint(Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId()) < pointsGoodsVO.getPoints()) {
                throw new ServiceException(ResultCode.POINT_NOT_ENOUGH);
            }
            if (pointsGoodsVO.getActiveStock() < 1) {
                throw new ServiceException(ResultCode.POINT_GOODS_ACTIVE_STOCK_INSUFFICIENT);
            }
            cartSkuVO.setPoints(pointsGoodsVO.getPoints());
            cartSkuVO.setPurchasePrice(0D);
            cartSkuVO.setPromotionId(pointsGoodsVO.getId());
        }
    }

    private void checkMinus(CartSkuVO cartSkuVO) {
        PromotionGoodsSearchParams params = new PromotionGoodsSearchParams();
        params.setSkuId(cartSkuVO.getGoodsSku().getId());
        params.setPromotionStatus(PromotionsStatusEnum.START.name());
        params.setPromotionType(PromotionTypeEnum.MINUS.name());
        PromotionGoods promotionsGoods = promotionsClient.getPromotionsGoods(params);
        if (promotionsGoods.getQuantity() == 0) {
            throw new ServiceException("该商品已抢光。");
        }
        if (cartSkuVO.getNum() > promotionsGoods.getQuantity()) {
            throw new ServiceException("活动库存不足，最多还可购买 " + promotionsGoods.getQuantity() + "个商品");
        }
        if (cartSkuVO.getNum() > promotionsGoods.getLimitNum()) {
            throw new ServiceException("超过购买限制，最多可购买 " + promotionsGoods.getLimitNum() + "个商品");
        }
    }

    private void checkRaffle(CartSkuVO cartSkuVO){
        PromotionGoodsSearchParams params = new PromotionGoodsSearchParams();
        params.setSkuId(cartSkuVO.getGoodsSku().getId());
        params.setPromotionStatus(PromotionsStatusEnum.START.name());
        PromotionGoods promotionsGoods = promotionsClient.getPromotionsGoods(params);
//        cartSkuVO.setPurchasePrice(0D);
        cartSkuVO.setPromotionId(promotionsGoods.getPromotionId());
    }


    /**
     * 检查商品促销
     *
     * @param cartParamsDTO 加入购物车参数
     * @param cartSkuVO     商品sku信息
     */
    private void checkGoodsPromotion(CartParamsDTO cartParamsDTO, CartSkuVO cartSkuVO) {
        CartSkuVO currentGoodsPromotion = promotionGoodsClient.getCurrentGoodsPromotion(cartSkuVO);
        //如果促销活动为空则直接返回
        if (currentGoodsPromotion.getPromotionMap() == null || currentGoodsPromotion.getPromotionMap().isEmpty()) {
            return;
        }
        cartSkuVO.setPromotionMap(currentGoodsPromotion.getPromotionMap());

        //如果加入购物车参数存在促销且currentGoodsPromotion.getPromotionMap()里存在对应的促销，则为商品设置对应的促销
        if (cartParamsDTO.getPromotionType() != null && cartSkuVO.getPromotionMap().keySet().stream().anyMatch(i -> i.contains(cartParamsDTO.getPromotionType().name()))) {
            cartSkuVO.setPromotionTypeEnum(cartParamsDTO.getPromotionType());
        }
        //如果参数没有促销，又存在促销活动，则为商品设置默认促销
        else {
            //促销类型等于以下类型：
            if (cartSkuVO.getPromotionMap().keySet().stream().anyMatch(i -> i.contains(PromotionTypeEnum.SECKILL.name()))) {
                Optional<Map.Entry<String, Object>> containsPromotion =
                        cartSkuVO.getPromotionMap().entrySet().stream().filter(i -> i.getKey().contains(PromotionTypeEnum.SECKILL.name())).findFirst();
                containsPromotion.ifPresent(stringObjectEntry -> this.setGoodsPromotionInfo(cartSkuVO, stringObjectEntry));

                // 获取秒杀活动id
                String promotionId = containsPromotion.get().getKey().substring(containsPromotion.get().getKey().lastIndexOf("-") + 1);

                // 检查秒杀活动库存
                String promotionGoodsStockCacheKey = PromotionGoodsClient.getPromotionGoodsStockCacheKey(PromotionTypeEnum.SECKILL, promotionId ,cartSkuVO.getGoodsSku().getId());
                Object quantity = cache.get(promotionGoodsStockCacheKey);

                // 如果秒杀活动库存小于等于0，则移除秒杀活动
                if (quantity == null || Integer.parseInt(quantity.toString()) <= 0) {
                    cartSkuVO.getPromotionMap().remove(containsPromotion.get().getKey());
                    cartSkuVO.setPromotionTypeEnum(null);
                    cartSkuVO.setPromotionId(null);
                }
            }
        }

        if (cartSkuVO.getPromotionTypeEnum() != null) {
            //拼团判定
            if (cartSkuVO.getPromotionTypeEnum().equals(PromotionTypeEnum.PINTUAN)) {
                //砍价判定
                checkPintuan(cartSkuVO);
            } else if (cartSkuVO.getPromotionTypeEnum().equals(PromotionTypeEnum.KANJIA)) {
                //检测购物车的数量
                checkKanjia(cartSkuVO);
            } else if (cartSkuVO.getPromotionTypeEnum().equals(PromotionTypeEnum.POINTS_GOODS)) {
                //检测购物车的数量
                checkPoint(cartSkuVO);
            } else if (cartSkuVO.getPromotionTypeEnum().equals(PromotionTypeEnum.MINUS)) {
                //直降商品判断
                checkMinus(cartSkuVO);
            } else if(cartSkuVO.getPromotionTypeEnum().equals(PromotionTypeEnum.RAFFLE)){
                //抽奖奖品判断
                checkRaffle(cartSkuVO);
            }
        }
    }

    /**
     * 设置商品促销信息
     *
     * @param cartSkuVO     商品sku信息
     * @param promotionInfo 促销信息
     */
    private void setGoodsPromotionInfo(CartSkuVO cartSkuVO, Map.Entry<String, Object> promotionInfo) {
        JSONObject promotionsObj = JSONUtil.parseObj(promotionInfo.getValue());
        PromotionGoodsSearchParams searchParams = new PromotionGoodsSearchParams();
        searchParams.setSkuId(cartSkuVO.getGoodsSku().getId());
        searchParams.setPromotionId(promotionsObj.get("id").toString());
        PromotionGoods promotionsGoods = this.promotionGoodsClient.getPromotionsGoods(searchParams);
        if (promotionsGoods != null && promotionsGoods.getPrice() != null) {
            cartSkuVO.getGoodsSku().setPromotionFlag(true);
            cartSkuVO.getGoodsSku().setPromotionPrice(promotionsGoods.getPrice());
        } else {
            cartSkuVO.getGoodsSku().setPromotionFlag(false);
            cartSkuVO.getGoodsSku().setPromotionPrice(null);
        }
    }


    /**
     * 校验商品有效性，判定失效和库存，促销活动价格
     *
     * @param skuId 商品skuId
     */
    private GoodsSku checkGoods(String skuId) {
        GoodsSku dataSku = this.goodsClient.getGoodsSkuByIdFromCache(skuId);
        if (dataSku == null) {
            throw new ServiceException(ResultCode.GOODS_NOT_EXIST);
        }
        if (!GoodsAuthEnum.PASS.name().equals(dataSku.getAuthFlag()) || !GoodsMarketEnum.UPPER.name().equals(dataSku.getMarketEnable())) {
            throw new ServiceException(ResultCode.GOODS_NOT_EXIST);
        }
        if (SceneEnums.SUPPLIER.name().equals(dataSku.getScene()) && dataSku.getSupportPurchase() != null && Boolean.FALSE.equals(dataSku.getSupportPurchase())) {
            throw new ServiceException(ResultCode.GOODS_NOT_SUPPORT_PURCHASE);
        }
        return dataSku;
    }

    /**
     * 校验店铺有效性
     *
     * @param storeId 店铺id
     * @return 店铺信息
     */
    private StoreVO checkStore(String storeId) {
        StoreVO store = storeClient.getStore(storeId);
        //如果店铺出现极端情况，则下架店铺商品
        if (store == null || !store.checkValid()) {
            BatchUpdateGoodsDTO batchUpdateGoodsDTO = new BatchUpdateGoodsDTO();
            batchUpdateGoodsDTO.setStoreId(storeId);
            batchUpdateGoodsDTO.setOperationType(2);
            batchUpdateGoodsDTO.setUnderReason("店铺信息无效，下架店铺商品");
            goodsClient.batchUpdateGoods(batchUpdateGoodsDTO);
        }
        return store;
    }

    /**
     * 检查并设置购物车商品数量
     *
     * @param cartSkuVO 购物车商品对象
     */
    private void checkSetGoodsQuantity(CartSkuVO cartSkuVO) {
        Integer enableStock = goodsClient.getStock(cartSkuVO.getGoodsSku().realSkuId());

        //如果sku的可用库存小于等于0或者小于用户购买的数量，则不允许购买
        if (enableStock <= 0 || enableStock < cartSkuVO.getNum()) {
            throw new ServiceException(ResultCode.GOODS_SKU_QUANTITY_NOT_ENOUGH);
        }

        if (enableStock <= cartSkuVO.getNum()) {
            cartSkuVO.setNum(enableStock);
        } else {
            cartSkuVO.setNum(cartSkuVO.getNum());
        }

        if (cartSkuVO.getGoodsSku() != null && !SalesModeEnum.WHOLESALE.name().equals(cartSkuVO.getGoodsSku().getSalesModel()) && cartSkuVO.getNum() > 99) {
            cartSkuVO.setNum(99);
        }
    }

    /**
     * 选择优惠券
     *
     * @param serviceFeeId 服务费id数组
     * @param way      购物车类型
     */
    public List<ServiceFeeDTO> selectServiceFee(String way,String storeId, String serviceFeeId) {
        CartSceneEnum cartSceneEnum = CartSceneEnum.getCartType(way);
        TradeDTO tradeDTO = tradeBuilder.readDTO(cartSceneEnum);
        if (CharSequenceUtil.isEmpty(storeId) || CharSequenceUtil.isEmpty(serviceFeeId)) {
            throw new ServiceException(ResultCode.PARAMS_ERROR);
        }
        List<ServiceFee> serviceFeeList = serviceFeeClient.getServiceFeeListByIds(Arrays.asList(serviceFeeId.split(",")));
        if (CollectionUtils.isEmpty(serviceFeeList)) {
            throw new ServiceException(ResultCode.SERVICE_FEE_GET_ERROR);
        }
        List<String> parentIds = serviceFeeList.stream()
                .map(ServiceFee::getParentId)
                .filter(parentId -> !parentId.equals("0")).toList();
        List<ServiceFee> parentServiceFeeList = serviceFeeClient.getServiceFeeListByIds(parentIds);
        List<CartVO> cartList = new ArrayList<>();
        //根据店铺分组
        Map<String, List<CartSkuVO>> storeCollect = tradeDTO.getSkuList().stream().collect(Collectors.groupingBy(CartSkuVO::getStoreId));
        for (Map.Entry<String, List<CartSkuVO>> storeCart : storeCollect.entrySet()) {
            if (!storeCart.getValue().isEmpty()) {
                //根据销售类型再次分类
                CartVO cartVO = new CartVO(storeCart.getValue().getFirst());
                if (tradeDTO.getDeliveryMethodEnum() == null) {
                    cartVO.setDeliveryMethodEnum(DeliveryMethodEnum.LOGISTICS);
                } else {
                    cartVO.setDeliveryMethodEnum(tradeDTO.getDeliveryMethodEnum());
                }
                cartVO.setSkuList(storeCart.getValue());

                cartVO.setGoodsNum(storeCart.getValue().stream().map(CartSkuVO::getNum).reduce(Integer::sum).orElse(0));

                try {
                    //筛选属于当前店铺的优惠券
                    storeCart.getValue().forEach(i -> i.getPromotionMap().forEach((key, value) -> {
                        if (key.contains(PromotionTypeEnum.COUPON.name())) {
                            JSONObject promotionsObj = JSONUtil.parseObj(value);
                            Coupon coupon = JSONUtil.toBean(promotionsObj, Coupon.class);
                            if (key.contains(PromotionTypeEnum.COUPON.name()) && coupon.getStoreId().equals(storeCart.getKey())) {
                                cartVO.getCanReceiveCoupon().add(new CouponVO(coupon));
                            }
                        }
                    }));

                    //去重并检测是否超过领取限制
                    Set<String> ids = new HashSet<>();
                    List<CouponVO> checkList = new ArrayList<>();
                    for (CouponVO couponVO : cartVO.getCanReceiveCoupon().stream()
                            .filter(p -> ids.add(p.getId()))
                            .toList()) {
                        Long memberCouponCount = memberCouponClient.getMemberCouponCount(UserContext.getCurrentId(), couponVO.getId());
                        if(memberCouponCount < couponVO.getCouponLimitNum()){
                            checkList.add(couponVO);
                        }
                    }
                    cartVO.setCanReceiveCoupon(checkList);
                } catch (Exception e) {
                    log.error("筛选属于当前店铺的优惠券发生异常！", e);
                }
                storeCart.getValue().stream().filter(i -> Boolean.TRUE.equals(i.getChecked())).findFirst().ifPresent(cartSkuVO -> cartVO.setChecked(true));
                cartList.add(cartVO);
            }
        }
        tradeDTO.setCartList(cartList);
        for (CartVO cartVO : tradeDTO.getCartList()) {
            if(cartVO.getStoreId().equals(storeId)) {
                List<ServiceFeeDTO> serviceFeeDTOList = new ArrayList<>();
                serviceFeeList.forEach(serviceFee -> {
                    ServiceFeeDTO serviceFeeDTO = new ServiceFeeDTO();
                    if (!serviceFee.getParentId().equals("0")) {
                        ServiceFee pServiceFee = parentServiceFeeList.stream().filter(parentServiceFee -> parentServiceFee.getId().equals(serviceFee.getParentId())).findFirst().orElse(null);
                        assert pServiceFee != null;
                        serviceFeeDTO.setId(pServiceFee.getId());
                        serviceFeeDTO.setName(pServiceFee.getName());
                    }
                    serviceFeeDTO.setPrice(serviceFee.getPrice());
                    serviceFeeDTO.setSubId(serviceFee.getId());
                    serviceFeeDTO.setSubName(serviceFee.getName());
                    serviceFeeDTO.setStoreId(storeId);
                    serviceFeeDTO.setNums(cartVO.getGoodsNum());
                    serviceFeeDTO.setTotalPrice(serviceFee.getPrice() * cartVO.getGoodsNum());
                    serviceFeeDTOList.add(serviceFeeDTO);
                });
                cartVO.setServiceFeeDTOList(serviceFeeDTOList);
                if (CollectionUtils.isNotEmpty(tradeDTO.getServiceFeeDTOList())) {
                    tradeDTO.getServiceFeeDTOList().removeIf(i -> i.getStoreId().equals(storeId));
                    tradeDTO.getServiceFeeDTOList().addAll(serviceFeeDTOList);
                }else {
                    tradeDTO.setServiceFeeDTOList(serviceFeeDTOList);
                }
            }
        }
        //tradeDTO.setServiceFeeDTOList(serviceFeeDTOList);
        tradeBuilder.resetTradeDTO(tradeDTO);
        return tradeDTO.getServiceFeeDTOList();
    }

    /**
     * 选择优惠券
     *
     * @param freightId 运费模版ID
     * @param way      购物车类型
     */
    public List<ServiceFeeDTO> selectFreight(String way,String freightId) {
        CartSceneEnum cartSceneEnum = CartSceneEnum.getCartType(way);
        TradeDTO tradeDTO = tradeBuilder.readDTO(cartSceneEnum);
        tradeDTO.setFreightTemplateId(freightId);
        tradeBuilder.resetTradeDTO(tradeDTO);
        return tradeDTO.getServiceFeeDTOList();
    }
}
