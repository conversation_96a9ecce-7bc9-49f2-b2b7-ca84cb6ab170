package cn.lili.modules.store.client;

import cn.lili.modules.constant.ServiceConstant;
import cn.lili.modules.store.entity.dos.FreightTemplate;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import cn.lili.modules.store.fallback.FreightTemplateFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: ftyy
 * @date: 2021-12-25 17:38
 * @description: 运费模板client
 */
@FeignClient(name = ServiceConstant.SYSTEM_SERVICE, contextId = "freightTemplate",
        fallback = FreightTemplateFallback.class)
public interface FreightTemplateClient {

    /**
     * 获取商家的运费模板
     *
     * @param storeId
     * @return 运费模板列表
     */
    @GetMapping("/feign/store/freightTemplate/store/{storeId}")
    List<FreightTemplateVO> getFreightTemplateList(@PathVariable String storeId);


    /**
     * 获取管理端的运费模板，用于代发订单的运费处理

     * @return 运费模板列表
     */
    @GetMapping("/feign/store/freightTemplate/manager")
    List<FreightTemplateVO> getFreightTemplateList();


    /**
     * 获取运费模板详细信息
     *
     * @param id 运费模板ID
     * @return 运费模板
     */
    @GetMapping("/feign/store/freightTemplate/{id}")
    FreightTemplateVO getFreightTemplate(@PathVariable("id") String id);

    /**
     * 添加商家运费模板 运费模板分为卖家包邮、运费计算两种类型
     *
     * @param freightTemplateVO 运费模板
     * @return 运费模板
     */
    @PostMapping("/feign/store/freightTemplate")
    FreightTemplateVO addFreightTemplate(@RequestBody FreightTemplateVO freightTemplateVO);

    /**
     * 修改商家运费模板
     *
     * @param freightTemplateVO 运费模板
     * @return 运费模板
     */
    @PutMapping("/feign/store/freightTemplate/editFreightTemplate")
    FreightTemplateVO editFreightTemplate(@RequestBody FreightTemplateVO freightTemplateVO);

    /**
     * 获取运费模版
     *
     * @param id 运费模板ID
     */
    @DeleteMapping("/feign/store/freightTemplate/{id}")
    boolean removeFreightTemplate(@PathVariable("id") String id);

    /**
     * 获取运费模版DO     *
     *
     * @param id 运费模板ID
     * @return 操作状态
     */
    @DeleteMapping("/feign/store/freightTemplate/query/{id}")
    FreightTemplate getById(@PathVariable("id") String id);
}
