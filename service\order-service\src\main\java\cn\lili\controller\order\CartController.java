package cn.lili.controller.order;

import cn.lili.common.aop.annotation.PreventDuplicateSubmissions;
import cn.lili.common.utils.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.order.cart.entity.CheckedParams;
import cn.lili.modules.order.cart.entity.dto.TradeDTO;
import cn.lili.modules.order.cart.entity.enums.CartSceneEnum;
import cn.lili.modules.order.cart.entity.vo.TradeParams;
import cn.lili.modules.order.cart.render.TradeBuilder;
import cn.lili.modules.order.cart.service.CartHandler;
import cn.lili.modules.order.order.entity.dto.CartParamsDTO;
import cn.lili.modules.order.order.entity.dto.ServiceFeeDTO;
import cn.lili.modules.order.order.entity.vo.ReceiptVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 买家端，购物车接口
 *
 * <AUTHOR>
 * @since 2020/11/16 10:04 下午
 */
@Slf4j
@RestController
@Tag(name = "买家端，购物车接口")
@RequestMapping("/order/carts")
@RequiredArgsConstructor
public class CartController {

    private final CartHandler cartHandler;

    private final TradeBuilder tradeBuilder;


    @Operation(summary = "向购物车中添加一个产品")
    @PostMapping
    public ResultMessage<Object> add(CartParamsDTO cartParamsDTO) {
        //校验参数
        cartParamsDTO.validateParams();
        //添加购物车
        cartParamsDTO.setCover(false);
        cartHandler.add(cartParamsDTO);
        return ResultUtil.success();
    }


    @Operation(summary = "获取购物车页面购物车详情")
    @GetMapping("/all")
    public ResultMessage<TradeDTO> cartAll(String way) {
        return ResultUtil.data(this.tradeBuilder.buildCart(CartSceneEnum.getCartType(way)));
    }

    @Operation(summary = "获取购物车数量")
    @GetMapping("/count")
    public ResultMessage<Integer> cartCount(String way) {
        return ResultUtil.data(this.cartHandler.getCartNum(way));
    }

    @Operation(summary = "获取购物车可用优惠券数量")
    @GetMapping("/coupon/num")
    public ResultMessage<Long> cartCouponNum(String way) {
        return ResultUtil.data(this.cartHandler.getCanUseCoupon(way));
    }

    @Operation(summary = "更新购物车中单个产品数量", description = "更新购物车中的多个产品的数量或选中状态")
    @PostMapping(value = "/sku/num/{skuId}")
    public ResultMessage<Object> update(@PathVariable String skuId,CartParamsDTO cartParamsDTO) {
        //传入的skuId  校验参数
        cartParamsDTO.setSkuId(skuId);
        cartParamsDTO.setCover(true);
        cartParamsDTO.validateParams();
        cartHandler.add(cartParamsDTO);
        return ResultUtil.success();
    }


    @Operation(summary = "更新购物车中单个产品", description = "更新购物车中的多个产品的数量或选中状态")
    @PostMapping(value = "/update/checked")
    public ResultMessage<Object> updateChecked(CheckedParams checkedParams) {
        checkedParams.selfCheck();
        cartHandler.updateChecked(checkedParams);
        return ResultUtil.success();
    }


    @Operation(summary = "清空购物车")
    @DeleteMapping
    public ResultMessage<Object> clean(String way) {
        tradeBuilder.clean(way);
        return ResultUtil.success();
    }

    @Operation(summary = "删除购物车中的一个或多个产品")
    @DeleteMapping(value = "/sku/remove")
    public ResultMessage<Object> delete(String way, String[] skuIds) {
        cartHandler.delete(way, skuIds);
        return ResultUtil.success();
    }


    @Operation(summary = "获取结算页面购物车详情")
    @GetMapping("/checked")
    public ResultMessage<TradeDTO> cartChecked(@NotNull(message = "读取选中列表") String way) {
        return ResultUtil.data(this.tradeBuilder.buildChecked(CartSceneEnum.getCartType(way)));
    }

    @Operation(summary = "选择收货地址")
    @PutMapping("/shippingAddress")
    public ResultMessage<Object> shippingAddress(String way, @NotNull(message = "收货地址ID不能为空") String shippingAddressId) {
        cartHandler.shippingAddress(way, shippingAddressId);
        return ResultUtil.success();
    }

    @Operation(summary = "选择自提地址")
    @GetMapping("/storeAddress")
    public ResultMessage<Object> shippingSelfPickAddress(@NotNull(message = "自提地址ID不能为空") String shippingAddressId,
        String way) {
        cartHandler.shippingSelfAddress(way, shippingAddressId);
        return ResultUtil.success();
    }

    @Operation(summary = "选择配送方式")
    @PutMapping("/shippingMethod")
    public ResultMessage<Object> shippingMethod(@NotNull(message = "配送方式不能为空") String shippingMethod, String selleId, String way) {
        cartHandler.shippingMethod(selleId, shippingMethod, way);
        return ResultUtil.success();
    }

    @Operation(summary = "获取用户可选择的物流方式")
    @GetMapping("/shippingMethodList")
    public ResultMessage<Object> shippingMethodList(String way) {
        return ResultUtil.data(cartHandler.shippingMethodList(way));
    }

    @Operation(summary = "选择发票")
    @GetMapping("/select/receipt")
    public ResultMessage<Object> selectReceipt(String way, ReceiptVO receiptVO) {
        this.cartHandler.shippingReceipt(way, receiptVO);
        return ResultUtil.success();
    }

    @Operation(summary = "选择优惠券")
    @GetMapping("/select/coupon")
    public ResultMessage<Object> selectCoupon(String way, @NotNull(message = "优惠券id不能为空") String memberCouponId, boolean used) {
        this.cartHandler.selectCoupon(way, memberCouponId, used);
        return ResultUtil.success();
    }


    @Operation(summary = "获取服务费")
    @GetMapping("/getServiceFee")
    public ResultMessage<List<ServiceFeeDTO>> getServiceFee(@NotNull(message = "读取选中列表") String way,
                                                            @NotNull(message = "店铺id不能为空") String storeId,
                                                            @NotNull(message = "服务费不能为空") String serviceFeeId) {
        return ResultUtil.data(this.cartHandler.selectServiceFee(way, storeId, serviceFeeId));
    }

    @Operation(summary = "获取选中的运费模版")
    @GetMapping("/getFreight")
    public ResultMessage<List<ServiceFeeDTO>> getFreight(@NotNull(message = "读取选中列表") String way,
                                                            @NotNull(message = "运费模版id不能为空") String freightId) {
        return ResultUtil.data(this.cartHandler.selectFreight(way, freightId));
    }
}
