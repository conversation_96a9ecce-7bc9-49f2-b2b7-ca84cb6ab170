package cn.lili.modules.goods.task;

import cn.lili.modules.goods.service.GoodsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 下架商品清理定时任务
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Component
public class OffShelfGoodsCleanupTask {

    @Autowired
    private GoodsService goodsService;

    /**
     * 每天凌晨2点执行，自动删除超过一年的下架商品
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void autoDeleteExpiredOffShelfGoods() {
        try {
            log.info("开始执行下架商品清理任务...");
            int deletedCount = goodsService.autoDeleteExpiredOffShelfGoods();
            log.info("下架商品清理任务完成，删除了 {} 个超过一年的下架商品", deletedCount);
        } catch (Exception e) {
            log.error("下架商品清理任务执行失败", e);
        }
    }
}
