package cn.lili.modules.system.fallback;

import cn.lili.common.exception.ServiceException;
import cn.lili.modules.message.entity.dos.MemberMessage;
import cn.lili.modules.system.client.MemberMessageClient;
import cn.lili.modules.system.client.RefundAddressClient;
import cn.lili.modules.system.entity.dos.RefundAddress;

import java.util.List;

/**
 * @author: ftyy
 * @date: 2022-01-17 11:29
 * @description: 会员消息发送Fallback
 */
public class RefundAddressFallback implements RefundAddressClient {

    @Override
    public RefundAddress getRefundAddress(String refundAddressId) {
        throw new ServiceException();
    }

    @Override
    public RefundAddress getRefundAddressDefault() {
        throw new ServiceException();
    }
}
