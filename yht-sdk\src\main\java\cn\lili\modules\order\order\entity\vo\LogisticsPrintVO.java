package cn.lili.modules.order.order.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 物流单打印VO
 * 包含物流单打印所需的所有信息
 *
 * <AUTHOR>
 * @since 2025/01/30
 */
@Data
@NoArgsConstructor
@Schema(title = "物流单打印VO")
public class LogisticsPrintVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(title = "订单编号")
    private String orderSn;

    @Schema(title = "下单时间")
    private String createTime;

    @Schema(title = "商家名称")
    private String storeName;

    @Schema(title = "物流公司名称")
    private String logisticsName;

    @Schema(title = "物流单号")
    private String logisticsNo;

    @Schema(title = "收货人信息")
    private ConsigneeInfo consigneeInfo;

    @Schema(title = "商品信息列表")
    private List<LogisticsGoodsInfo> goodsList;

    /**
     * 收货人信息
     */
    @Data
    @NoArgsConstructor
    @Schema(title = "收货人信息")
    public static class ConsigneeInfo implements Serializable {
        
        private static final long serialVersionUID = 1L;

        @Schema(title = "收货人姓名")
        private String consigneeName;

        @Schema(title = "收货人手机")
        private String consigneeMobile;

        @Schema(title = "收货地址路径")
        private String consigneeAddressPath;

        @Schema(title = "详细地址")
        private String consigneeDetail;

        @Schema(title = "完整地址")
        public String getFullAddress() {
            return (consigneeAddressPath != null ? consigneeAddressPath : "") + 
                   (consigneeDetail != null ? consigneeDetail : "");
        }
    }

    /**
     * 物流商品信息
     */
    @Data
    @NoArgsConstructor
    @Schema(title = "物流商品信息")
    public static class LogisticsGoodsInfo implements Serializable {
        
        private static final long serialVersionUID = 1L;

        @Schema(title = "SKU名称")
        private String goodsName;

        @Schema(title = "货号")
        private String skuId;

        @Schema(title = "数量")
        private Integer num;

        @Schema(title = "规格信息")
        private String specs;
    }
}
