package cn.lili.controller.feign.system;


import cn.lili.common.security.enums.SceneEnums;
import cn.lili.modules.store.client.FreightTemplateClient;
import cn.lili.modules.store.entity.dos.FreightTemplate;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import cn.lili.modules.system.service.FreightTemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: ftyy
 * @date: 2021-12-25 17:41
 * @description: 运费模板 feign client
 */
@RestController
@RequiredArgsConstructor
public class FreightTemplateFeignController implements FreightTemplateClient {

    private final FreightTemplateService freightTemplateService;

    @Override
    public List<FreightTemplateVO> getFreightTemplateList(String storeId) {
        return freightTemplateService.getFreightTemplateList(storeId);
    }

    @Override
    public List<FreightTemplateVO> getFreightTemplateList() {
        return freightTemplateService.getFreightTemplateList("", SceneEnums.MANAGER.value());
    }

    @Override
    public FreightTemplateVO getFreightTemplate(String id) {
        return freightTemplateService.getFreightTemplate(id);
    }

    @Override
    public FreightTemplateVO addFreightTemplate(FreightTemplateVO freightTemplateVO) {
        return freightTemplateService.addFreightTemplate(freightTemplateVO);
    }

    @Override
    public FreightTemplateVO editFreightTemplate(FreightTemplateVO freightTemplateVO) {
        return freightTemplateService.editFreightTemplate(freightTemplateVO);
    }

    @Override
    public boolean removeFreightTemplate(String id) {
        return freightTemplateService.removeFreightTemplate(id);
    }

    @Override
    public FreightTemplate getById(String id) {
        return freightTemplateService.getById(id);
    }
}
