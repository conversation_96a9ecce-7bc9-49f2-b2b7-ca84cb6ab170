package cn.lili.controller.order;

import cn.lili.common.aop.annotation.PreventDuplicateSubmissions;
import cn.lili.common.utils.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.member.entity.dto.MemberAddressDTO;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dto.OrderSearchParams;
import cn.lili.modules.order.order.entity.vo.*;
import cn.lili.modules.order.order.integration.OrderIntegrationHandler;
import cn.lili.modules.order.order.service.OrderPriceService;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.order.order.service.OrderStatisticsService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单API
 *
 * <AUTHOR>
 * @since 2020/11/17 4:34 下午
 */
@RestController
@RequestMapping("/order")
@Tag(name = "订单API")
@RequiredArgsConstructor
public class OrderController {

    /**
     * 订单
     */
    private final OrderService orderService;
    /**
     * 订单价格
     */
    private final OrderPriceService orderPriceService;
    /**
     * 订单集成处理
     */
    private final OrderIntegrationHandler orderIntegrationHandler;

    /**
     * 订单统计
     */
    private final OrderStatisticsService orderStatisticsService;

    @Operation(summary = "查询订单 单行数据，方便列表展示")
    @GetMapping
    public ResultMessage<Page<OrderSimpleVO>> queryOrder(OrderSearchParams orderSearchParams) {
        return ResultUtil.data(orderService.queryByParams(orderSearchParams));
    }

    @Operation(summary = "查询订单统计数字")
    @GetMapping("/statistics")
    public ResultMessage<OrderStatisticsVO> statistics(String orderType, Boolean isProxy) {
        return ResultUtil.data(orderStatisticsService.statistics(orderType,isProxy));
    }


    @Operation(summary = "订单明细 返回订单完整详情，包含订单商品信息，子订单信息等，方便详情页面展示")
    @GetMapping(value = "/{orderSn}")
    public ResultMessage<OrderDetailVO> detail(@PathVariable String orderSn) {
        return ResultUtil.data(orderService.queryDetail(orderSn));
    }

    @Operation(summary = "买家订单备注")
    @PutMapping("/{orderSn}/remark")
    public ResultMessage<Object> remark(@PathVariable String orderSn, @RequestParam String remark) {
        orderService.updateRemark(orderSn, remark);
        return ResultUtil.success();
    }

    @Operation(summary = "卖家订单备注")
    @PutMapping("/{orderSn}/sellerRemark")
    public ResultMessage<Object> sellerRemark(@PathVariable String orderSn, @RequestParam String sellerRemark) {
        orderService.updateSellerRemark(orderSn, sellerRemark);
        return ResultUtil.success();
    }

    @Operation(summary = "查询订单导出列表")
    @GetMapping("/queryExportOrder")
    public  ResultMessage<Object> queryExportOrder(OrderSearchParams orderSearchParams) {
        //校验导出周期
        orderSearchParams.checkoutExportParams();
        orderService.queryExportOrder(orderSearchParams);
        return ResultUtil.success();
    }

    @Operation(summary = "修改收货人信息")
    @PostMapping(value = "/update/{orderSn}/consignee")
    public ResultMessage<Order> consignee(@NotNull(message = "参数非法") @PathVariable String orderSn, @RequestBody MemberAddressDTO memberAddressDTO) {
        memberAddressDTO.validateParams();
        return ResultUtil.data(orderService.updateConsignee(orderSn, memberAddressDTO));
    }

    @Operation(summary = "修改订单价格")
    @PutMapping(value = "/update/{orderSn}/price")
    public ResultMessage<Order> updateOrderPrice(@PathVariable String orderSn, @RequestParam Double orderPrice) {
        return ResultUtil.data(orderPriceService.updatePrice(orderSn, orderPrice));
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "订单核验")
    @PutMapping(value = "/verification/take/{verificationCode}")
    public ResultMessage<Object> take(@PathVariable String verificationCode) {
        return ResultUtil.data(orderIntegrationHandler.take(verificationCode));
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "核验全部订单")
    @PutMapping(value = "/verification/take/all/{verificationCode}")
    public ResultMessage<Object> takeAll(@PathVariable String verificationCode) {
        orderIntegrationHandler.takeAll(verificationCode);
        return ResultUtil.success();
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "取消订单")
    @PostMapping(value = "/{orderSn}/cancel")
    public ResultMessage<Object> cancel(@PathVariable String orderSn, @RequestParam String reason) {
        orderIntegrationHandler.cancel(orderSn, reason);
        return ResultUtil.success();
    }

    @Operation(summary = "删除订单")
    @DeleteMapping(value = "/{orderSn}")
    public ResultMessage<Object> deleteOrder(@PathVariable String orderSn) {
        orderService.deleteOrder(orderSn);
        return ResultUtil.success();
    }

    @Operation(summary = "获取物流单打印数据")
    @GetMapping(value = "/logistics-print/{orderSn}")
    public ResultMessage<LogisticsPrintVO> getLogisticsPrintData(@PathVariable String orderSn) {
        return ResultUtil.data(orderService.getLogisticsPrintData(orderSn));
    }

    @Operation(summary = "批量获取物流单打印数据")
    @PostMapping(value = "/logistics-print/batch")
    public ResultMessage<List<LogisticsPrintVO>> getBatchLogisticsPrintData(@RequestBody List<String> orderSns) {
        return ResultUtil.data(orderService.getBatchLogisticsPrintData(orderSns));
    }

    @Operation(summary = "获取发货单打印数据")
    @GetMapping(value = "/delivery-note-print/{orderSn}")
    public ResultMessage<DeliveryNotePrintVO> getDeliveryNotePrintData(@PathVariable String orderSn) {
        return ResultUtil.data(orderService.getDeliveryNotePrintData(orderSn));
    }

    @Operation(summary = "批量获取发货单打印数据")
    @PostMapping(value = "/delivery-note-print/batch")
    public ResultMessage<List<DeliveryNotePrintVO>> getBatchDeliveryNotePrintData(@RequestBody List<String> orderSns) {
        return ResultUtil.data(orderService.getBatchDeliveryNotePrintData(orderSns));
    }

    @Operation(summary = "获取每个子订单的发货单打印数据（每个子订单生成一张小票）")
    @GetMapping(value = "/delivery-note-print/sub-orders/{orderSn}")
    public ResultMessage<List<DeliveryNotePrintVO>> getDeliveryNotePrintDataBySubOrders(@PathVariable String orderSn) {
        return ResultUtil.data(orderService.getDeliveryNotePrintDataBySubOrders(orderSn));
    }

    @Operation(summary = "批量获取每个子订单的发货单打印数据（每个子订单生成一张小票）")
    @PostMapping(value = "/delivery-note-print/sub-orders/batch")
    public ResultMessage<List<DeliveryNotePrintVO>> getBatchDeliveryNotePrintDataBySubOrders(@RequestBody List<String> orderSns) {
        return ResultUtil.data(orderService.getBatchDeliveryNotePrintDataBySubOrders(orderSns));
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "确认收货")
    @PostMapping(value = "/{orderSn}/receiving")
    public ResultMessage<Object> receiving(@NotNull(message = "订单编号不能为空") @PathVariable("orderSn") String orderSn) {
        orderService.complete(orderSn);
        return ResultUtil.success();
    }

    @Operation(summary = "开票")
    @PostMapping(value = "/receipt/{orderSn}")
    public ResultMessage<Object> invoice(@NotBlank(message = "订单编号不能为空") @PathVariable String orderSn) {
        return ResultUtil.data(orderService.invoice(orderSn));
    }

    @Operation(summary = "下载的订单列表", responses = @ApiResponse(content = {
            @io.swagger.v3.oas.annotations.media.Content(mediaType = "application/octet-stream")
    }))
    @GetMapping(value = "/downUploadDeliverExcel/{id}")
    public void downUploadDeliverExcel(@PathVariable String id, String status) {
        //下载订单批量发货Excel
        this.orderService.getBatchDeliverList(id, status);
    }

    @Operation(summary = "订单备忘录")
    @PutMapping("/{orderSn}/memo")
    public ResultMessage<Object> memo(@PathVariable String orderSn, @RequestParam String memo) {
        orderService.updateMemo(orderSn, memo);
        return ResultUtil.success();
    }

}