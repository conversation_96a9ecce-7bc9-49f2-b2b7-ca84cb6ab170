package cn.lili.modules.statistics.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 商品统计VO
 *
 * <AUTHOR>
 * @since 2020/12/9 14:25
 */
@Data
public class GoodsStatisticsDataVO {

    @Schema(title = "商品ID")
    private String goodsId;

    @Schema(title = "商品名称")
    private String goodsName;

    @Schema(title = "销售数量")
    private String num;

    @Schema(title = "销售金额")
    private Double price;

    @Schema(title = "店铺ID")
    private String storeId;
}
