package cn.lili.modules.goods.service;

import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.goods.entity.dto.OffShelfGoodsSearchParams;
import cn.lili.modules.goods.entity.vos.OffShelfGoodsCategoryCountVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * 下架商品服务测试
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@SpringBootTest
@ActiveProfiles("test")
public class OffShelfGoodsServiceTest {

    @Autowired
    private GoodsService goodsService;

    @Test
    public void testGetOffShelfGoodsByPage() {
        // 创建查询参数
        OffShelfGoodsSearchParams searchParams = new OffShelfGoodsSearchParams();
        searchParams.setPageNumber(1);
        searchParams.setPageSize(10);
        searchParams.setSortType("offShelfTime");
        searchParams.setSortDirection("desc");

        // 执行查询
        Page<Goods> result = goodsService.getOffShelfGoodsByPage(searchParams);

        // 验证结果
        assert result != null;
        System.out.println("查询到下架商品数量: " + result.getTotal());
        System.out.println("当前页商品数量: " + result.getRecords().size());
    }

    @Test
    public void testGetOffShelfGoodsCategoryCount() {
        // 测试分类统计
        String storeId = "test-store-id";
        List<OffShelfGoodsCategoryCountVO> result = goodsService.getOffShelfGoodsCategoryCount(storeId);

        // 验证结果
        assert result != null;
        System.out.println("分类统计数量: " + result.size());
        
        for (OffShelfGoodsCategoryCountVO category : result) {
            System.out.println("分类: " + category.getCategoryName() + ", 商品数量: " + category.getGoodsCount());
        }
    }

    @Test
    public void testAutoDeleteExpiredOffShelfGoods() {
        // 测试自动删除超期商品
        int deletedCount = goodsService.autoDeleteExpiredOffShelfGoods();
        
        System.out.println("删除的超期商品数量: " + deletedCount);
        assert deletedCount >= 0;
    }

    @Test
    public void testSearchWithConditions() {
        // 测试带条件的查询
        OffShelfGoodsSearchParams searchParams = new OffShelfGoodsSearchParams();
        searchParams.setGoodsName("测试商品");
        searchParams.setPrice("10_1000");
        searchParams.setSortType("price");
        searchParams.setSortDirection("asc");
        searchParams.setPageNumber(1);
        searchParams.setPageSize(5);

        Page<Goods> result = goodsService.getOffShelfGoodsByPage(searchParams);

        assert result != null;
        System.out.println("条件查询结果数量: " + result.getTotal());
    }
}
