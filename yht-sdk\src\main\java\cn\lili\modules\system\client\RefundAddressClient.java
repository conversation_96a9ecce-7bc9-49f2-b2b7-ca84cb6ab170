package cn.lili.modules.system.client;

import cn.lili.modules.constant.ServiceConstant;
import cn.lili.modules.message.entity.dos.MemberMessage;
import cn.lili.modules.system.entity.dos.RefundAddress;
import cn.lili.modules.system.fallback.MemberMessageFallback;
import cn.lili.modules.system.fallback.RefundAddressFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会员消息发送Client
 * <AUTHOR>
 * @since 2022/1/13
 **/
@FeignClient(name = ServiceConstant.SYSTEM_SERVICE, contextId = "refund-address",fallback = RefundAddressFallback.class)
public interface RefundAddressClient {

    @PostMapping("/feign/refund-address/getRefundAddress")
    RefundAddress getRefundAddress(@RequestParam("refundAddressId") String refundAddressId);


    @PostMapping("/feign/refund-address/getDefault")
    RefundAddress getRefundAddressDefault();

}
