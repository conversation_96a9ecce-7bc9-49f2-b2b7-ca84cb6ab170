package cn.lili.modules.goods.entity.vos;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 下架商品分类统计VO
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@Schema(title = "下架商品分类统计")
public class OffShelfGoodsCategoryCountVO {

    @Schema(title = "分类ID")
    private String categoryId;

    @Schema(title = "分类名称")
    private String categoryName;

    @Schema(title = "分类路径")
    private String categoryPath;

    @Schema(title = "商品数量")
    private Long goodsCount;

    @Schema(title = "父级分类ID")
    private String parentId;

    @Schema(title = "分类层级")
    private Integer level;
}
