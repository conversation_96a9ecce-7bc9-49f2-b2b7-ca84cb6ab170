package cn.lili.controller.statistics;

import cn.lili.common.utils.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.goods.client.GoodsClient;
import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.goods.entity.dto.GoodsSearchParams;
import cn.lili.modules.order.order.client.OrderFlowStatisticsClient;
import cn.lili.modules.statistics.entity.dto.GoodsStatisticsQueryParam;
import cn.lili.modules.statistics.entity.vo.CategoryStatisticsDataVO;
import cn.lili.modules.statistics.entity.vo.GoodsStatisticsDataVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 商品统计接口
 *
 * <AUTHOR>
 * @since 2020/12/9 19:04
 */
@Tag(name = "商品流水统计接口")
@RestController
@RequestMapping("/statistics/goods")
@RequiredArgsConstructor
public class GoodsStatisticsController {

    private final OrderFlowStatisticsClient orderFlowStatisticsClient;

    private final GoodsClient goodsClient;

    @Operation(summary = "获取统计列表,排行前一百的数据")
    @GetMapping
    public ResultMessage<List<GoodsStatisticsDataVO>> getByPage(GoodsStatisticsQueryParam goodsStatisticsQueryParam) {
        return ResultUtil.data(orderFlowStatisticsClient.getGoodsStatisticsData(goodsStatisticsQueryParam, 100));
    }

    @Operation(summary = "获取行业统计列表")
    @GetMapping("/getCategoryByPage")
    public ResultMessage<List<CategoryStatisticsDataVO>> getCategoryByPage(GoodsStatisticsQueryParam goodsStatisticsQueryParam) {
        return ResultUtil.data(orderFlowStatisticsClient.getCategoryStatisticsData(goodsStatisticsQueryParam));
    }

    @Operation(summary = "获取商品访客排行")
    @GetMapping("/visitor/ranking")
    public ResultMessage<Page<Goods>> getGoodsVisitorRanking(GoodsSearchParams params) {
        return ResultUtil.data(goodsClient.getGoodsVisitorRanking(params));
    }

    @Operation(summary = "获取商品图片下载量排行")
    @GetMapping("/image/download/ranking")
    public ResultMessage<Page<Goods>> getGoodsImageDownloadRanking(GoodsSearchParams params) {
        return ResultUtil.data(goodsClient.getGoodsImageDownloadRanking(params));
    }
}
