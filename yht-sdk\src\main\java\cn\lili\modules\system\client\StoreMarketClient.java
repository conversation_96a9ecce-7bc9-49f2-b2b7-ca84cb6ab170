package cn.lili.modules.system.client;

import cn.lili.modules.constant.ServiceConstant;
import cn.lili.modules.system.entity.dos.StoreMarket;
import cn.lili.modules.system.fallback.StoreMarketFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(name = ServiceConstant.SYSTEM_SERVICE, contextId = "store-market",fallback = StoreMarketFallback.class)
public interface StoreMarketClient {

    /**
     * 根据市场id获取店铺市场信息
     * @param id
     * @return
     */
    @GetMapping("/feign/store-market/getStoreMarketById/{id}")
    StoreMarket getStoreMarketById(@PathVariable("id") String id);
}
