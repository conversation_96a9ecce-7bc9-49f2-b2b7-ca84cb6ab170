package cn.lili.controller.feign.system;


import cn.lili.modules.store.client.StoreLogisticsClient;
import cn.lili.modules.system.client.StoreMarketClient;
import cn.lili.modules.system.entity.dos.StoreMarket;
import cn.lili.modules.system.service.StoreMarketService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class StoreMarketFeignController implements StoreMarketClient {

    private final StoreMarketService storeMarketService;

    @Override
    public StoreMarket getStoreMarketById(String id) {
        return storeMarketService.getById(id);
    }
}
