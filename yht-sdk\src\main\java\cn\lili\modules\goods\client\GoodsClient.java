package cn.lili.modules.goods.client;

import cn.lili.common.vo.PageVO;
import cn.lili.modules.constant.ServiceConstant;
import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.goods.entity.dos.GoodsSku;
import cn.lili.modules.goods.entity.dto.BatchUpdateGoodsDTO;
import cn.lili.modules.goods.entity.dto.GoodsCompleteMessage;
import cn.lili.modules.goods.entity.dto.GoodsSearchParams;
import cn.lili.modules.goods.entity.dto.OffShelfGoodsSearchParams;
import cn.lili.modules.goods.entity.dto.ProxyGoodsOperationDTO;
import cn.lili.modules.goods.entity.vos.OffShelfGoodsCategoryCountVO;
import cn.lili.modules.goods.fallback.GoodsFallback;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.params.StoreRankStatisticsParams;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import cn.lili.modules.store.entity.vos.StoreRankStatisticsVO;
import cn.lili.modules.supplier.entity.dto.SupplierGoodsOperationDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 商品服务client
 *
 * <AUTHOR>
 * @version v1.0 2021-11-08 09:52
 */
@Service
@FeignClient(name = ServiceConstant.GOODS_SERVICE, contextId = "goods", fallback = GoodsFallback.class)
public interface GoodsClient {

    /**
     * 供应商添加商品
     *
     * @param supplierGoodsOperationDTO 商品查询条件
     */
    @PostMapping("/feign/supplierGoods/add")
    void addSupplierGoods(@RequestBody SupplierGoodsOperationDTO supplierGoodsOperationDTO);

    /**
     * 供应商修改商品
     *
     * @param supplierGoodsOperationDTO 商品属性
     */
    @PostMapping("/feign/supplierGoods/edit")
    void editSupplierGoods(@RequestBody SupplierGoodsOperationDTO supplierGoodsOperationDTO);

    /**
     * 店铺修改代理供应商的商品
     *
     * @param proxyGoodsOperationDTO 代理商品编辑参数
     */
    @PostMapping("/feign/goods/proxy/edit")
    void editProxyGoods(@RequestBody ProxyGoodsOperationDTO proxyGoodsOperationDTO);

    /**
     * 从缓存中获取商品
     *
     * @param skuId skuid
     * @return 商品详情
     */
    @GetMapping("/feign/goods/sku/{skuId}/cache")
    GoodsSku getGoodsSkuByIdFromCache(@PathVariable String skuId);

    /**
     * 根据商品Id获取商品
     *
     * @param skuId skuId
     * @return 商品详情
     */
    @GetMapping("/feign/goods/sku/{skuId}")
    GoodsSku getGoodsSkuById(@PathVariable String skuId);

    /**
     * 从缓存中获取可参与促销商品
     *
     * @param skuId skuid
     * @return 商品详情
     */
    @GetMapping("/feign/goods/sku/promotion/{skuId}/cache")
    GoodsSku getCanPromotionGoodsSkuByIdFromEs(@PathVariable String skuId);

    /**
     * 批量修改商品信息 以及 sku信息
     *
     * @param store 店铺信息
     */
    @PutMapping("/feign/goods/update/store")
    void updateStoreDetail(@RequestBody Store store);

    /**
     * 条件统计商品
     *
     * @param storeId 店铺ID
     * @return 总数
     */
    @GetMapping("/feign/goods/{storeId}/count")
    Long count(@PathVariable String storeId);

    /**
     * 批量更新商品
     *
     * @param batchUpdateGoodsDTO 批量更新商品DTO
     */
    @PostMapping("/feign/goods/batch/update/goods")
    void batchUpdateGoods(@RequestBody BatchUpdateGoodsDTO batchUpdateGoodsDTO);

    /**
     * 获取sku库存
     *
     * @param skuId skuId
     * @return 库存
     */
    @GetMapping("/feign/goods/sku/{skuId}/stock")
    Integer getStock(@PathVariable String skuId);

    /**
     * 添加商品评价数量
     *
     * @param commentNum 评价数量
     * @param goodsId    商品ID
     */
    @GetMapping("/feign/goods/comment/{goodsId}/num")
    void addGoodsCommentNum(@RequestParam Integer commentNum, @PathVariable String goodsId);

    /**
     * 根据商品id获取商品信息
     *
     * @param goodsId 商品id
     * @return 商品信息
     */
    @GetMapping("/feign/goods/{goodsId}/getById")
    Goods getById(@PathVariable String goodsId);

    /**
     * 查询参数查询商品列表
     *
     * @param searchParams 查询参数
     * @return 商品列表
     */
    @PostMapping("/feign/goods/queryListByParams")
    List<Goods> queryListByParams(@RequestBody GoodsSearchParams searchParams);

    /**
     * 查询参数查询商品列表
     *
     * @param searchParams 查询参数
     * @return 商品列表
     */
    @PostMapping("/feign/goods/sku/list")
    List<GoodsSku> getGoodsSkuByList(@RequestBody GoodsSearchParams searchParams);

    /**
     * 更新商品购买数量
     *
     * @param goodsCompleteMessageList     购买数量
     */
    @PutMapping("/feign/goods/buy/count")
    void updateGoodsBuyCount(@RequestBody List<GoodsCompleteMessage> goodsCompleteMessageList);

    /**
     * 更新商品sku信息
     *
     * @param goodsSku 商品sku信息
     */
    @PutMapping("/feign/goods/goods-sku/updateGoodsSku")
    void updateGoodsSku(@RequestBody GoodsSku goodsSku);

    /**
     * 根据参数查询商品信息
     *
     * @param searchParams 查询参数
     * @return 商品信息
     */
    @PostMapping("/feign/goods/getGoodsByParams")
    Goods getGoodsByParams(@RequestBody GoodsSearchParams searchParams);

    /**
     * 统计sku总数
     *
     * @param storeId 店铺id
     * @return sku总数
     */
    @GetMapping("/feign/goods/sku/countSkuNum/{storeId}")
    Long countSkuNum(@PathVariable String storeId);

    /**
     * 商品删除
     *
     * @param goodsIds 批量删除的id集合
     */
    @DeleteMapping("/feign/goods/delete")
    void deleteGoods(@RequestBody List<String> goodsIds);

    /**
     * 店铺代理供应商商品
     *
     * @param goodsId 商品id
     */
    @PutMapping("/feign/goods/proxy")
    void addSupplierGoods(@RequestParam String goodsId);

    /**
     * 供应商删除供应商商品
     *
     * @param goodsIds 商品id
     */
    @DeleteMapping("/feign/goods/supplier/delete")
    void deleteSupplierGoods(@RequestBody List<String> goodsIds);

    /**
     * 店铺代理供应商商品
     *
     * @param goodsId 商品id
     */
    @PutMapping("/feign/goods/syncStock")
    void syncStock(@RequestBody List<String> goodsId);

    /**
     * 同步商品sku评价数量
     *
     * @param skuId skuId
     */
    @PutMapping("/feign/goods/sku/{skuId}/comment")
    void syncGoodsSkuCommentCount(@PathVariable String skuId);

    /**
     * 根据查询条件获取商品sku分页
     *
     * @param searchParams 查询条件
     * @return 商品sku分页
     */
    @PostMapping("/feign/goods/sku/page")
    Page<GoodsSku> getGoodsSkuByPage(GoodsSearchParams searchParams);

    /**
     * 通过商品Id获取物流信息
     * @param goodsId
     * @return
     */
    @GetMapping("/feign/goods/template/{goodsId}/")
    FreightTemplateVO getGoodsTemplate(@PathVariable String goodsId);

    /**
     * 批量从es中获取商品SKU信息
     *
     * @param ids 商品SKU ID集合
     * @return 商品SKU信息集合
     */
    @PostMapping("/feign/goods/goods-sku/getGoodsSkuByIdFromCache")
    List<GoodsSku> getGoodsSkuByIdFromCache(@RequestBody List<String> ids);

    /**
     * 获取所有商品SKU的ID列表
     *
     * @return 所有商品SKU的ID列表
     */
    @GetMapping("/feign/goods/sku/getAllSkuIds")
    List<String> getAllSkuIds();
    
    /**
     * 获取商品SKU的成本价
     *
     * @param skuId 商品SKU ID
     * @return 商品成本价
     */
    @GetMapping("/feign/goods/sku/{skuId}/cost")
    Double getSkuCostPrice(@PathVariable String skuId);

    /**
     * 获取商品图片下载量排名
     *
     * @param params 查询参数
     * @return 商品图片下载量排名
     */
    @PostMapping("/feign/goods/getGoodsImageDownloadCountByStore")
    Page<StoreRankStatisticsVO> getGoodsImageDownloadCountByStore (@RequestBody StoreRankStatisticsParams params);

    /**
     * 获取店铺商品总数
     *
     * @param storeId 店铺ID
     * @return 商品图片下载量
     */
    @GetMapping("/feign/goods/countGoodsNum/{storeId}")
    List<Goods> countGoodsNum(@PathVariable String storeId);

    /**
     * 获取店铺商品实拍率
     *
     * @param storeId 店铺ID
     * @return 商品实拍率（百分比）
     */
    @GetMapping("/feign/goods/getRealShootRate/{storeId}")
    Double getRealShootRate(@PathVariable String storeId);

    /**
     * 增加商品访客统计数量
     *
     * @param goodsId 商品ID
     */
    @PutMapping("/feign/goods/{goodsId}/visitor/increment")
    void incrementVisitorCount(@PathVariable String goodsId);

    /**
     * 获取商品访客排行
     *
     * @param params 查询参数
     * @return 商品访客排行
     */
    @PostMapping("/feign/goods/visitor/ranking")
    Page<Goods> getGoodsVisitorRanking(@RequestBody GoodsSearchParams params);

    /**
     * 获取商品图片下载量排行
     *
     * @param params 查询参数
     * @return 商品图片下载量排行
     */
    @PostMapping("/feign/goods/image/download/ranking")
    Page<Goods> getGoodsImageDownloadRanking(@RequestBody GoodsSearchParams params);

    /**
     * 获取店铺商品质量合格率
     *
     * @param storeId 店铺ID
     * @return 商品质量合格率（百分比）
     */
    @GetMapping("/feign/goods/getQualityPassRate/{storeId}")
    Double getQualityPassRate(@PathVariable String storeId);

    /**
     * 分页查询下架商品
     *
     * @param searchParams 查询参数
     * @return 下架商品分页数据
     */
    @PostMapping("/feign/goods/offShelf/page")
    Page<Goods> getOffShelfGoodsByPage(@RequestBody OffShelfGoodsSearchParams searchParams);

    /**
     * 获取下架商品分类统计
     *
     * @param storeId 商家ID
     * @return 分类统计列表
     */
    @GetMapping("/feign/goods/offShelf/category/count")
    List<OffShelfGoodsCategoryCountVO> getOffShelfGoodsCategoryCount(@RequestParam String storeId);
}
